#!/usr/bin/env python3
"""
Test script for the comprehensive analytics functionality
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.api.services.analyzers.comprehensive_analytics import ComprehensiveAnalyzer


def create_sample_data():
    """Create sample call flow data for testing"""
    
    # Generate sample dates
    start_date = datetime.now() - timedelta(days=30)
    dates = [start_date + timedelta(days=i) for i in range(30)]
    
    # Sample data for inbound calls
    inbound_data = []
    for i, date in enumerate(dates):
        for hour in range(8, 18):  # Business hours
            for call in range(np.random.randint(5, 20)):  # Random calls per hour
                inbound_data.append({
                    'Call Date': date.replace(hour=hour, minute=np.random.randint(0, 60)),
                    'Length': np.random.randint(30, 600),  # 30 seconds to 10 minutes
                    'User': np.random.choice(['Agent_A', 'Agent_B', 'Agent_C', 'Agent_D']),
                    'Campaign': np.random.choice(['Transaction_English', 'GeneralArabic', 'TechSupp']),
                    'Language': np.random.choice(['English', 'Arabic'], p=[0.6, 0.4])
                })
    
    # Sample data for outbound calls
    outbound_data = []
    for i, date in enumerate(dates):
        for hour in range(9, 17):  # Business hours
            for call in range(np.random.randint(3, 15)):  # Random calls per hour
                outbound_data.append({
                    'Call Date': date.replace(hour=hour, minute=np.random.randint(0, 60)),
                    'Length': np.random.randint(60, 900),  # 1 minute to 15 minutes
                    'User': np.random.choice(['Agent_A', 'Agent_B', 'Agent_C', 'Agent_D']),
                    'Campaign': np.random.choice(['ONLINE', 'AGENTDIRECT']),
                    'Language': np.random.choice(['English', 'Arabic'], p=[0.7, 0.3])
                })
    
    # Sample data for missed calls
    missed_data = []
    for i, date in enumerate(dates):
        for hour in range(8, 20):  # Extended hours for missed calls
            for call in range(np.random.randint(1, 8)):  # Random missed calls per hour
                missed_data.append({
                    'Call Date': date.replace(hour=hour, minute=np.random.randint(0, 60)),
                    'Length': 0,  # Missed calls have no length
                    'User': None,  # No agent for missed calls
                    'Campaign': np.random.choice(['Transaction_English', 'GeneralArabic', 'TechSupp']),
                    'Language': np.random.choice(['English', 'Arabic'], p=[0.5, 0.5])
                })
    
    return {
        'inbound': pd.DataFrame(inbound_data),
        'outbound': pd.DataFrame(outbound_data),
        'missed': pd.DataFrame(missed_data)
    }


def test_comprehensive_analytics():
    """Test the comprehensive analytics functionality"""
    
    print("🧪 Testing Comprehensive Analytics...")
    print("=" * 50)
    
    # Create sample data
    print("📊 Creating sample data...")
    data = create_sample_data()
    
    print(f"   - Inbound calls: {len(data['inbound'])}")
    print(f"   - Outbound calls: {len(data['outbound'])}")
    print(f"   - Missed calls: {len(data['missed'])}")
    
    # Initialize analyzer
    analyzer = ComprehensiveAnalyzer()
    
    # Test parameters
    test_params = {
        'start_date': (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
        'end_date': datetime.now().strftime('%Y-%m-%d'),
        'include_avg_call_time': True,
        'include_total_duration': True,
        'include_standard_deviation': True,
        'include_call_volume': True,
        'group_by_language': True,
        'group_by_campaign': True,
        'group_by_agent': True,
        'group_by_call_type': True,
        'include_percentiles': True,
        'include_outlier_analysis': True,
        'include_charts': True
    }
    
    print("\n🔍 Running comprehensive analysis...")
    
    try:
        # Run analysis
        result = analyzer.analyze(data, test_params)
        
        print("✅ Analysis completed successfully!")
        
        # Check results
        if 'data' in result:
            result_data = result['data']
            
            print("\n📈 Analysis Results:")
            print("-" * 30)
            
            # Core metrics
            if 'core_metrics' in result_data:
                core = result_data['core_metrics']
                if 'error' not in core:
                    print("✅ Core Metrics:")
                    if 'average_call_time' in core:
                        print(f"   - Average Call Time: {core['average_call_time']['formatted']}")
                    if 'total_duration' in core:
                        print(f"   - Total Duration: {core['total_duration']['formatted']}")
                    if 'call_volume' in core:
                        vol = core['call_volume']
                        print(f"   - Total Calls: {vol['total_calls']}")
                        print(f"   - Answer Rate: {vol['answer_rate']*100:.1f}%")
                else:
                    print(f"❌ Core Metrics Error: {core['error']}")
            
            # Time analysis
            if 'time_analysis' in result_data:
                time_data = result_data['time_analysis']
                print("✅ Time Analysis:")
                if 'hourly_volume' in time_data:
                    peak_hour = max(time_data['hourly_volume'], key=time_data['hourly_volume'].get)
                    print(f"   - Peak Hour: {peak_hour}:00 ({time_data['hourly_volume'][peak_hour]} calls)")
                if 'daily_volume' in time_data:
                    busiest_day = max(time_data['daily_volume'], key=time_data['daily_volume'].get)
                    print(f"   - Busiest Day: {busiest_day} ({time_data['daily_volume'][busiest_day]} calls)")
            
            # Segmentation
            if 'segmentation' in result_data:
                seg_data = result_data['segmentation']
                print("✅ Segmentation Analysis:")
                if 'by_language' in seg_data:
                    print(f"   - Languages analyzed: {list(seg_data['by_language'].keys())}")
                if 'by_campaign' in seg_data:
                    print(f"   - Campaigns analyzed: {list(seg_data['by_campaign'].keys())}")
                if 'by_call_type' in seg_data:
                    print(f"   - Call types analyzed: {list(seg_data['by_call_type'].keys())}")
            
            # Statistics
            if 'statistics' in result_data:
                stats = result_data['statistics']
                print("✅ Statistical Analysis:")
                if 'percentiles' in stats:
                    print(f"   - Percentiles calculated: {list(stats['percentiles'].keys())}")
                if 'outliers' in stats:
                    outliers = stats['outliers']
                    print(f"   - Outliers detected: {outliers['count']} ({outliers['percentage']:.1f}%)")
        
        # Check charts
        if 'charts' in result:
            charts = result['charts']
            print(f"✅ Charts generated: {len(charts)}")
            for i, chart in enumerate(charts):
                print(f"   - Chart {i+1}: {chart.get('title', 'Untitled')}")
        
        print("\n🎉 All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_comprehensive_analytics()
    if success:
        print("\n✅ Comprehensive Analytics test completed successfully!")
        exit(0)
    else:
        print("\n❌ Comprehensive Analytics test failed!")
        exit(1)
