# Call Flow Analytics

A comprehensive call flow analytics and predictive modeling system with FastAPI backend and Streamlit frontend.

## Features

This application provides 21 different analytics use cases for call flow data, plus advanced ML models and custom reporting:

### Core Analytics
1. **Agent Performance Analytics**: Score agents based on resolution rates, call handling time, and other metrics
2. **Call Volume Prediction**: Forecast call volumes by hour, day, week, or month
3. **Staffing Optimization**: Recommend optimal agent staffing based on predicted call volumes
4. **Campaign Analysis**: Analyze and compare campaign performance metrics
5. **Language Analysis**: Analyze performance across different languages (Arabic/English)
6. **Missed Call Analysis**: Identify patterns leading to missed calls
7. **Customer Journey Analytics**: Track customer interactions across multiple calls

### Advanced Analytics
8. **First Call Resolution (FCR) Analysis**: Track which calls are resolved on the first attempt
9. **Customer Sentiment Analysis**: Analyze customer satisfaction based on call patterns
10. **Agent Specialization Recommendations**: Identify which agents excel with specific types of calls
11. **Conversion Rate Optimization**: Track and optimize conversion rates for sales campaigns
12. **Queue Optimization**: Analyze queue times and abandonment rates
13. **Seasonal Trend Analysis**: Identify seasonal patterns in call volumes
14. **Geographic Insights**: Analyze call patterns by customer location
15. **Call Quality Scoring**: Create an automated quality assessment framework
16. **A/B Testing Framework**: Design and analyze experiments for different call approaches
17. **Churn Prediction**: Identify early warning signs of customer dissatisfaction
18. **Cross-selling Opportunity Detection**: Identify patterns for cross-selling
19. **Anomaly Detection**: Automatically flag unusual call patterns
20. **Callback Optimization**: Predict optimal callback times for missed calls
21. **Knowledge Base Enhancement**: Identify common issues for knowledge base updates

### Advanced ML Models
- **Ensemble Learning**: Combine multiple machine learning models for better prediction accuracy
- **Time Series Forecasting**: Advanced time series models for call volume prediction
- **Feature Engineering**: Automated feature extraction for improved model performance
- **Model Persistence**: Save and load trained models for faster predictions
- **Hyperparameter Tuning**: Optimize model parameters for better performance

### Custom Reporting
- **PDF Reports**: Generate professional PDF reports with charts and tables
- **Excel Reports**: Export data and analysis to Excel for further processing
- **Report Templates**: Pre-defined report templates for common use cases
- **Customizable Sections**: Select which sections to include in reports
- **Report Scheduling**: Schedule reports to be generated automatically (coming soon)

## System Architecture

- **Backend**: FastAPI for RESTful API endpoints
- **Frontend**: Streamlit for interactive dashboards and visualizations
- **Data Processing**: Pandas, NumPy, and scikit-learn for data analysis and ML models
- **Visualization**: Plotly for interactive charts and graphs

## Installation

### Prerequisites
- Python 3.8+
- pip (Python package manager)

### Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd call-flow
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

## Running the Application

### Option 1: Using the Run Script (Recommended)

The easiest way to run the application is to use the provided run script:

```bash
python run_app.py
```

This script will:
1. Set up the Python path correctly
2. Start the FastAPI backend
3. Start the Streamlit frontend

### Option 2: Manual Startup

If you prefer to start the components manually, make sure to run the commands from the project root directory:

#### Start the FastAPI Backend

```bash
# Make sure you're in the project root directory
export PYTHONPATH=$PYTHONPATH:$(pwd)
uvicorn app.api.main:app --host 127.0.0.1 --port 8000 --reload
```

#### Start the Streamlit Frontend

```bash
# In a new terminal, make sure you're in the project root directory
export PYTHONPATH=$PYTHONPATH:$(pwd)
streamlit run app/frontend/app.py
```

### Accessing the Application

- API documentation: http://127.0.0.1:8000/docs or http://127.0.0.1:8000/redoc
- Streamlit frontend: http://localhost:8501

## Data Format

The application expects Excel files with the following sheets:
- **Inbound**: Inbound call data
- **Outbound**: Outbound call data
- **Missed Call**: Missed call data

Each sheet should contain columns such as:
- Lead ID
- Phone
- Campaign
- Call Date
- Status
- User (Agent)
- List ID
- Length (Call duration)
- Queue Time (for inbound calls)

## Usage

1. **Upload Data**: Use the "Data Upload" page to upload Excel files
2. **Select Analysis**: Choose the type of analysis from the sidebar
3. **Configure Parameters**: Set date ranges, filters, and other parameters
4. **View Results**: Explore the analysis results, charts, and recommendations

### Advanced ML Models

Access the Advanced ML Models page from the sidebar to:

1. **Call Volume Prediction**: Use ensemble models for more accurate call volume forecasting
   - Select model type (Linear, Random Forest, Gradient Boosting, XGBoost, LightGBM, or Ensemble)
   - Choose prediction granularity (hourly, daily, weekly, monthly)
   - Set the number of periods to forecast
   - Optionally retrain the model with new data

2. **Model Training**: Train machine learning models for various prediction tasks
   - Select the model to train (call volume, agent performance)
   - Choose model type and parameters
   - Filter data for training
   - View model metrics after training

3. **Model Evaluation**: Evaluate trained models (coming soon)
   - Compare model performance
   - Analyze feature importance
   - View prediction errors

### Custom Reports

Access the Custom Reports page from the sidebar to:

1. **Generate Reports**: Create custom PDF or Excel reports
   - Set report title and type
   - Select date range and filters
   - Choose which sections to include
   - Include or exclude charts and tables

2. **Report Templates**: Use pre-defined templates for common reports
   - Agent Performance Report
   - Campaign Analysis Report
   - Customer Experience Report

3. **Report Scheduling**: Schedule automatic report generation (coming soon)
   - Set frequency (daily, weekly, monthly)
   - Configure email delivery
   - Manage report archives

## Development

### Project Structure

```
call-flow/
├── app/
│   ├── api/                  # FastAPI backend
│   │   ├── main.py           # FastAPI entry point
│   │   ├── models/           # Data models
│   │   ├── routers/          # API endpoints
│   │   │   ├── data.py       # Data management endpoints
│   │   │   ├── analytics.py  # Analytics endpoints
│   │   │   └── advanced.py   # Advanced ML and reporting endpoints
│   │   ├── services/         # Business logic
│   │   │   ├── analytics.py  # Analytics services
│   │   │   ├── ml_models.py  # Advanced ML models
│   │   │   └── report_generator.py # Report generation
│   │   └── utils/            # Helper functions
│   ├── frontend/             # Streamlit frontend
│   │   ├── app.py            # Main Streamlit app
│   │   ├── pages/            # Different pages for each use case
│   │   │   ├── advanced_ml.py # Advanced ML models page
│   │   │   └── custom_reports.py # Custom reports page
│   │   └── components/       # Reusable UI components
│   └── shared/               # Shared code between API and frontend
│       ├── config.py         # Configuration
│       ├── data_loader.py    # Excel data loading
│       └── preprocessing.py  # Data preprocessing
├── data/                     # Data directory
├── models/                   # Saved ML models
├── tests/                    # Unit tests
├── requirements.txt          # Dependencies
└── README.md                 # Documentation
```

### Adding New Features

To add a new analytics feature:

1. Create a new analyzer class in `app/api/services/analytics.py`
2. Add a new endpoint in `app/api/routers/analytics.py`
3. Add a new request/response schema in `app/api/models/schemas.py`
4. Add a new page in the Streamlit frontend

To add a new ML model:

1. Create a new model class in `app/api/services/ml_models.py`
2. Add training and prediction methods to the model class
3. Add a new endpoint in `app/api/routers/advanced.py`
4. Update the Advanced ML Models page in the Streamlit frontend

To add a new report type:

1. Add new report generation methods in `app/api/services/report_generator.py`
2. Add a new endpoint in `app/api/routers/advanced.py`
3. Update the Custom Reports page in the Streamlit frontend

## License

[MIT License](LICENSE)

## Contributors

- Your Name - Initial work
