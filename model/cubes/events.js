cube(`events`, {
  sql_table: `public.events`,
  
  data_source: `default`,
  
  joins: {
    
  },
  
  dimensions: {
    data: {
      sql: `data`,
      type: `string`
    },
    
    name: {
      sql: `name`,
      type: `string`
    },
    
    created_at: {
      sql: `created_at`,
      type: `time`
    },
    
    updated_at: {
      sql: `updated_at`,
      type: `time`
    },
    
    event_time: {
      sql: `event_time`,
      type: `time`
    }
  },
  
  measures: {
    count: {
      type: `count`
    }
  },
  
  pre_aggregations: {
    // Pre-aggregation definitions go here.
    // Learn more in the documentation: https://cube.dev/docs/caching/pre-aggregations/getting-started
  }
});
