#!/usr/bin/env python
"""
Run script for Call Flow Analytics application.
This script ensures the app package is properly in the Python path.
"""
import os
import sys
import subprocess
import time

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Set environment variable for child processes
os.environ["PYTHONPATH"] = current_dir + ":" + os.environ.get("PYTHONPATH", "")


def run_backend():
    """Run the FastAPI backend"""
    print("Starting FastAPI backend...")
    backend_process = subprocess.Popen(
        [
            "uvicorn",
            "app.api.main:app",
            "--host",
            "127.0.0.1",
            "--port",
            "8003",  # Changed port to 8001
            "--reload",
        ],
        env=os.environ.copy(),
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
    )
    return backend_process


def run_frontend():
    """Run the Streamlit frontend"""
    print("Starting Streamlit frontend...")
    frontend_process = subprocess.Popen(
        ["streamlit", "run", "app/frontend/app.py"],
        env=os.environ.copy(),
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
    )
    return frontend_process


def main():
    """Main function to run the application"""
    backend_process = None
    frontend_process = None

    try:
        # Start backend
        backend_process = run_backend()

        # Wait for backend to start
        print("Waiting for backend to start...")
        time.sleep(5)

        # Start frontend
        frontend_process = run_frontend()

        print("Services started. Press Ctrl+C to stop.")

        # Monitor processes for errors
        while True:
            backend_return = backend_process.poll()
            frontend_return = frontend_process.poll()

            if backend_return is not None:
                stderr = backend_process.stderr.read()
                print(f"Backend process exited with code {backend_return}")
                if stderr:
                    print(f"Backend error: {stderr}")
                break

            if frontend_return is not None:
                stderr = frontend_process.stderr.read()
                print(f"Frontend process exited with code {frontend_return}")
                if stderr:
                    print(f"Frontend error: {stderr}")
                break

            time.sleep(1)

    except KeyboardInterrupt:
        print("\nStopping services...")

    except Exception as e:
        print(f"Error: {str(e)}")

    finally:
        # Clean up processes
        if backend_process and backend_process.poll() is None:
            print("Terminating backend process...")
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()

        if frontend_process and frontend_process.poll() is None:
            print("Terminating frontend process...")
            frontend_process.terminate()
            try:
                frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                frontend_process.kill()

        print("Services stopped.")


if __name__ == "__main__":
    main()
