#!/usr/bin/env python
"""
Standalone script to run the Streamlit app with the correct Python path.
"""
import os
import sys
import subprocess

# Get the absolute path to the project root
project_root = os.path.dirname(os.path.abspath(__file__))

# Run the Streamlit app with the correct PYTHONPATH
env = os.environ.copy()
# Make sure the project root and app directory are in the Python path
app_dir = os.path.join(project_root, "app")
frontend_dir = os.path.join(app_dir, "frontend")
pages_dir = os.path.join(frontend_dir, "pages")

# Build the Python path with all necessary directories
python_path = [project_root, app_dir, frontend_dir, pages_dir]

# Add existing PYTHONPATH if it exists
if "PYTHONPATH" in env:
    python_path.append(env["PYTHONPATH"])

# Join all paths with the appropriate separator
env["PYTHONPATH"] = os.pathsep.join(python_path)

print(f"Setting PYTHONPATH to: {env['PYTHONPATH']}")
print(f"Running Streamlit app from: {project_root}")

# Run the Streamlit app
process = subprocess.run(
    ["streamlit", "run", "app/frontend/app.py"], env=env, cwd=project_root
)

# Exit with the same code as the Streamlit process
sys.exit(process.returncode)
