import pandas as pd
import numpy as np
import os
import sys
from app.shared.data_loader import CallFlowDataLoader
from app.api.services.analyzers import (
    AgentPerformanceAnalyzer,
    CallVolumePredictor,
    StaffingOptimizer,
    CampaignAnalyzer,
    LanguageAnalyzer,
    MissedCallAnalyzer,
    CustomerJourneyAnalyzer,
    FCRAnalyzer,
    SentimentAnalyzer,
    AgentSpecializationAnalyzer,
)


def test_data_loader():
    """Test data loader functionality"""
    try:
        # Initialize data loader with default files
        data_loader = CallFlowDataLoader()

        # Load data
        success = data_loader.load_data()
        if not success:
            print("❌ Data loader failed to load data")
            return False

        # Preprocess data
        success = data_loader.preprocess_data()
        if not success:
            print("❌ Data loader failed to preprocess data")
            return False

        # Get data
        data = data_loader.get_data("all")

        # Check if we have data
        if not data or all(df.empty for df in data.values()):
            print("❌ Data loader returned empty data")
            return False

        print("✅ Data loader test passed")
        print(f"   Loaded data types: {list(data.keys())}")
        for key, df in data.items():
            if not df.empty:
                print(f"   {key}: {len(df)} rows, {len(df.columns)} columns")

        return True, data

    except Exception as e:
        print(f"❌ Data loader test failed: {str(e)}")
        return False, None


def test_agent_performance_analyzer(data):
    """Test agent performance analyzer"""
    try:
        # Initialize analyzer
        analyzer = AgentPerformanceAnalyzer()

        # Perform analysis
        result = analyzer.analyze(
            data, ["resolution_rate", "call_length", "call_volume"]
        )

        # Check if we have results
        if not result or "data" not in result or "charts" not in result:
            print("❌ Agent performance analyzer returned invalid results")
            return False

        print("✅ Agent performance analyzer test passed")
        print(f"   Result data keys: {list(result['data'].keys())}")
        print(f"   Charts: {len(result['charts'])}")

        return True

    except Exception as e:
        print(f"❌ Agent performance analyzer test failed: {str(e)}")
        return False


def test_call_volume_predictor(data):
    """Test call volume predictor"""
    try:
        # Initialize predictor
        predictor = CallVolumePredictor()

        # Perform prediction
        result = predictor.predict(data, "daily")

        # Check if we have results
        if not result or "data" not in result or "charts" not in result:
            print("❌ Call volume predictor returned invalid results")
            return False

        print("✅ Call volume predictor test passed")
        print(f"   Result data keys: {list(result['data'].keys())}")
        print(f"   Forecast periods: {len(result['data'].get('forecast', []))}")

        return True

    except Exception as e:
        print(f"❌ Call volume predictor test failed: {str(e)}")
        return False


def run_tests():
    """Run all tests"""
    print("🔍 Testing Call Flow Analytics modules...")

    # Test data loader
    data_loader_result, data = test_data_loader()

    if not data_loader_result:
        print("❌ Data loader test failed, skipping other tests")
        return False

    # Test analyzers
    analyzer_tests = [
        lambda: test_agent_performance_analyzer(data),
        lambda: test_call_volume_predictor(data),
    ]

    results = [data_loader_result]
    for test in analyzer_tests:
        results.append(test())

    # Print summary
    print("\n📊 Test Summary:")
    print(f"   Passed: {results.count(True)}/{len(results)}")
    print(f"   Failed: {results.count(False)}/{len(results)}")

    return all(results)


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
