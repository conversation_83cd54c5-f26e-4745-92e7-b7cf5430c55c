# Call Flow Analytics System
## Quick Start Guide

This guide will help you get started with the Call Flow Analytics System, from installation to running your first analysis.

## 1. Installation

### 1.1 Prerequisites

Before installing the Call Flow Analytics System, ensure you have:

- Python 3.8 or higher
- Redis server (for background task processing)
- Git (for cloning the repository)

### 1.2 Clone the Repository

```bash
git clone <repository-url>
cd call-flow
```

### 1.3 Install Dependencies

```bash
pip install -r requirements.txt
```

### 1.4 Start Redis Server

Redis is required for background task processing. Start the Redis server:

```bash
# On macOS/Linux
redis-server

# On Windows
# Follow the Redis for Windows installation instructions
```

## 2. Running the Application

### 2.1 Using the Run Script (Recommended)

The easiest way to run the application is to use the provided run script:

```bash
# On macOS/Linux
./run.sh

# On Windows
python run_app.py
```

This script will:
1. Set up the Python path correctly
2. Start the FastAPI backend
3. Start the Streamlit frontend

### 2.2 Manual Startup

If you prefer to start the components manually:

#### Start the FastAPI Backend

```bash
# Make sure you're in the project root directory
export PYTHONPATH=$PYTHONPATH:$(pwd)
uvicorn app.api.main:app --host 127.0.0.1 --port 8002 --reload
```

#### Start the Streamlit Frontend

```bash
# In a new terminal, make sure you're in the project root directory
export PYTHONPATH=$PYTHONPATH:$(pwd)
streamlit run app/frontend/app.py
```

### 2.3 Start Celery Worker (for Background Tasks)

```bash
# In a new terminal
./run_celery.sh

# Or manually
export PYTHONPATH=$PYTHONPATH:$(pwd)
celery -A app.celery_app worker --loglevel=info -E
```

### 2.4 Start Flower (for Task Monitoring)

```bash
# In a new terminal
./run_flower.sh

# Or manually
export PYTHONPATH=$PYTHONPATH:$(pwd)
celery -A app.celery_app flower --port=5555
```

## 3. Accessing the Application

Once the application is running, you can access it at:

- **Streamlit Frontend**: http://localhost:8501
- **FastAPI Documentation**: http://localhost:8002/docs
- **Flower Monitoring**: http://localhost:5555

## 4. Using the Application

### 4.1 Uploading Data

1. Navigate to the "Data Upload" page from the sidebar
2. Click "Upload Excel Files" and select your Excel files
3. The files should contain sheets named "Inbound", "Outbound", and "Missed Call"
4. Click "Process Files" to load the data

### 4.2 Running Basic Analytics

1. Select an analysis type from the sidebar (e.g., "Agent Performance")
2. Configure the analysis parameters (date range, filters, etc.)
3. Click "Run Analysis" to generate results
4. View the results in the interactive charts and tables

### 4.3 Using Advanced ML Models

1. Navigate to the "Advanced ML Models" page from the sidebar
2. Select a model type (e.g., "Call Volume Prediction")
3. Configure the model parameters
4. Click "Train Model" to start the training process
5. Monitor the training progress in the "Task Monitor" page
6. Once training is complete, use the model for predictions

### 4.4 Generating Reports

1. Navigate to the "Custom Reports" page from the sidebar
2. Select the report type and configure the parameters
3. Click "Generate Report" to create the report
4. Download the report in PDF or Excel format

## 5. Data Format

The application expects Excel files with the following sheets:

### 5.1 Inbound Sheet

Contains data about inbound calls with columns:
- Lead ID
- Phone
- Campaign
- Call Date
- Status
- User (Agent)
- List ID
- Length (Call duration)
- Queue Time

### 5.2 Outbound Sheet

Contains data about outbound calls with columns:
- Lead ID
- Phone
- Campaign
- Call Date
- Status
- User (Agent)
- List ID
- Length (Call duration)

### 5.3 Missed Call Sheet

Contains data about missed calls with columns:
- Lead ID
- Phone
- Campaign
- Call Date
- Status
- User (Agent)
- List ID

## 6. Troubleshooting

### 6.1 Import Errors

If you encounter import errors, ensure that the Python path is set correctly:

```bash
export PYTHONPATH=$PYTHONPATH:$(pwd)
```

### 6.2 Redis Connection Errors

If Celery cannot connect to Redis:

1. Ensure Redis server is running
2. Check the Redis URL in `app/celery_app.py`
3. Restart the Celery worker

### 6.3 Task Processing Issues

If background tasks are not processing:

1. Check the Celery worker logs for errors
2. Ensure Redis is running
3. Restart the Celery worker
4. Check the Flower monitoring interface for task status

### 6.4 Data Loading Issues

If data is not loading correctly:

1. Ensure your Excel files have the correct sheet names
2. Check the column names match the expected format
3. Verify the data types are correct
4. Check the application logs for specific errors

## 7. Getting Help

If you encounter issues not covered in this guide:

1. Check the application logs for error messages
2. Refer to the technical documentation for detailed information
3. Contact the development team for support

## 8. Next Steps

After getting started with the basic functionality, explore:

- Advanced analytics features
- Custom report generation
- Machine learning model training and tuning
- Integration with your existing systems

---

*For more detailed information, refer to the business and technical documentation.*
