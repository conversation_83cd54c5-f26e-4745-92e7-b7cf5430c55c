# Call Flow Analytics System
## Technical Documentation for Developers

This documentation is designed to help developers, including junior developers, understand the Call Flow Analytics System architecture, implementation, and how to work with the codebase.

## 1. System Architecture

### 1.1 High-Level Architecture

The Call Flow Analytics System is built using a modern, modular architecture with the following components:

1. **FastAPI Backend**: RESTful API service that handles data processing, analytics, and machine learning
   - FastAPI is a modern Python web framework that makes it easy to build APIs
   - It provides automatic API documentation, data validation, and high performance

2. **Streamlit Frontend**: Interactive web application for user interface and visualizations
   - Streamlit allows us to create web applications using pure Python
   - No HTML, CSS, or JavaScript knowledge required

3. **Celery Task Queue**: Background task processing for computationally intensive operations
   - Celery allows us to run time-consuming tasks in the background
   - This keeps the UI responsive while heavy processing happens asynchronously

4. **Redis**: Message broker for Celery and result backend
   - Redis is an in-memory data store that Celery uses to manage tasks
   - It stores task messages and results

5. **Flower**: Monitoring tool for Celery tasks
   - Flower provides a web interface to monitor Celery tasks
   - It shows task status, progress, and allows task management

### 1.1.1 How the Components Work Together

Here's how data flows through the system:

1. User uploads Excel files through the Streamlit frontend
2. Frontend sends the files to the FastAPI backend
3. Backend processes the data and stores it in memory
4. User requests an analysis through the frontend
5. If it's a quick analysis, the backend processes it immediately
6. If it's a complex analysis, the backend creates a Celery task
7. Celery worker picks up the task from Redis and processes it
8. Results are stored in Redis and can be retrieved by the frontend
9. User can monitor task progress through the Task Monitor page or Flower

### 1.2 Directory Structure

```
call-flow/
├── app/
│   ├── api/                  # FastAPI backend
│   │   ├── main.py           # FastAPI entry point
│   │   ├── models/           # Data models
│   │   ├── routers/          # API endpoints
│   │   │   ├── data.py       # Data management endpoints
│   │   │   ├── analytics.py  # Analytics endpoints
│   │   │   └── advanced.py   # Advanced ML and reporting endpoints
│   │   ├── services/         # Business logic
│   │   │   ├── analytics.py  # Analytics services
│   │   │   ├── ml_models.py  # Advanced ML models
│   │   │   └── report_generator.py # Report generation
│   │   └── utils/            # Helper functions
│   ├── frontend/             # Streamlit frontend
│   │   ├── app.py            # Main Streamlit app
│   │   ├── pages/            # Different pages for each use case
│   │   │   ├── advanced_ml.py # Advanced ML models page
│   │   │   └── custom_reports.py # Custom reports page
│   │   └── components/       # Reusable UI components
│   ├── shared/               # Shared code between API and frontend
│   │   ├── config.py         # Configuration
│   │   ├── data_loader.py    # Excel data loading
│   │   └── preprocessing.py  # Data preprocessing
│   ├── celery_app.py         # Celery configuration
│   └── tasks.py              # Celery task definitions
├── data/                     # Data directory
├── models/                   # Saved ML models
├── tests/                    # Unit tests
├── run_app.py                # Script to run the application
├── run.sh                    # Shell script to run the application
├── run_celery.sh             # Shell script to run Celery worker
├── run_flower.sh             # Shell script to run Flower monitoring
└── requirements.txt          # Dependencies
```

## 2. Backend Implementation

### 2.1 FastAPI Backend

The backend is implemented using FastAPI, a modern, fast web framework for building APIs with Python. The main components are:

#### 2.1.1 API Endpoints

- **Data Endpoints** (`app/api/routers/data.py`): Endpoints for uploading, retrieving, and managing call data
- **Analytics Endpoints** (`app/api/routers/analytics.py`): Endpoints for running various analytics on the call data
- **Advanced Endpoints** (`app/api/routers/advanced.py`): Endpoints for machine learning models and advanced analytics
- **Task Endpoints** (`app/api/routers/tasks.py`): Endpoints for managing background tasks

**Example: Creating an API Endpoint in FastAPI**

Here's an example of how we define an API endpoint for language analysis:

```python
# In app/api/routers/analytics.py

@router.post("/language-analysis", response_model=AnalysisResultWithCharts)
async def analyze_languages(
    request: LanguageAnalysisRequest,
    data_loader = Depends(get_data_loader)
):
    """
    Analyze performance across different languages
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request) for key, df in data.items() if not df.empty
        }

        # Initialize analyzer
        analyzer = LanguageAnalyzer()

        # Perform analysis
        result = analyzer.analyze(filtered_data, request.languages)

        return {
            "success": True,
            "message": "Language analysis completed successfully",
            "data": result["data"],
            "charts": result["charts"]
        }
    except Exception as e:
        logger.error(f"Error in language analysis: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error in language analysis: {str(e)}"
        )
```

**Key FastAPI Concepts:**

1. **Route Decorators**: `@router.post("/language-analysis")` defines a POST endpoint at `/analytics/language-analysis`
2. **Request Models**: `LanguageAnalysisRequest` defines the expected request body structure
3. **Response Models**: `response_model=AnalysisResultWithCharts` defines the response structure
4. **Dependency Injection**: `Depends(get_data_loader)` injects the data loader dependency
5. **Exception Handling**: We catch exceptions and return appropriate HTTP responses

#### 2.1.2 Services

- **Analytics Services** (`app/api/services/analyzers/`): Implementation of the 21 analytics use cases in separate modules
- **ML Models** (`app/api/services/ml_models.py`): Implementation of machine learning models
- **Report Generator** (`app/api/services/report_generator.py`): Generation of custom reports
- **Task Manager** (`app/api/services/task_manager.py`): Management of background tasks

**Example: Implementing an Analytics Service**

Here's an example of how we implement an analytics service:

```python
# In app/api/services/analyzers/language.py

class LanguageAnalyzer(BaseAnalyzer):
    """Analyzer for language performance"""

    def analyze(
        self, data: Dict[str, pd.DataFrame], languages: List[str]
    ) -> Dict[str, Any]:
        """
        Analyze performance across languages

        Args:
            data: Dictionary of DataFrames
            languages: List of languages to analyze

        Returns:
            Analysis result
        """
        result_data = {}
        charts = []

        # Combine all data for language analysis
        df_combined = pd.DataFrame()
        for key, df in data.items():
            if not df.empty:
                # If Language column doesn't exist, try to detect from Campaign
                if "Language" not in df.columns and "Campaign" in df.columns:
                    df["Language"] = df["Campaign"].apply(
                        lambda x: "Arabic" if "Arabic" in str(x) else "English"
                    )

                if "Language" in df.columns:
                    df_combined = pd.concat([df_combined, df])

        # Filter by languages if specified
        if languages and len(languages) > 0:
            df_combined = df_combined[df_combined["Language"].isin(languages)]

        # Calculate metrics by language
        if not df_combined.empty and "Language" in df_combined.columns:
            # Call volume by language
            call_volume = df_combined.groupby("Language").size().reset_index(name="Count")
            result_data["call_volume_by_language"] = call_volume.to_dict("records")

            # Create chart
            charts.append({
                "id": "call_volume_by_language",
                "title": "Call Volume by Language",
                "type": "bar",
                "data": {
                    "x": call_volume["Language"].tolist(),
                    "y": call_volume["Count"].tolist()
                }
            })

            # More analysis code...

        return {
            "data": result_data,
            "charts": charts
        }
```

**Key Service Concepts:**

1. **Class-Based Design**: Each analyzer is a class that inherits from `BaseAnalyzer`
2. **Type Hints**: We use Python type hints to make the code more readable and maintainable
3. **Data Processing**: We use pandas for data manipulation and analysis
4. **Result Structure**: Results include both data (for tables) and charts (for visualization)

### 2.2 Data Processing

#### 2.2.1 Data Loader

The `CallFlowDataLoader` class in `app/shared/data_loader.py` handles loading and preprocessing of Excel files:

- Loads Excel files with Inbound, Outbound, and Missed Call sheets
- Normalizes column names and data formats
- Combines data from multiple files
- Provides methods to retrieve data by type (inbound, outbound, missed, combined)

**Example: Data Loader Implementation**

```python
# In app/shared/data_loader.py

class CallFlowDataLoader:
    """Loader for call flow data from Excel files"""

    def __init__(self, file_paths: List[str]):
        """
        Initialize the data loader

        Args:
            file_paths: List of paths to Excel files
        """
        self.file_paths = file_paths
        self.inbound_data = []
        self.missed_data = []
        self.outbound_data = []
        self.df_inbound = pd.DataFrame()
        self.df_missed = pd.DataFrame()
        self.df_outbound = pd.DataFrame()
        self.combined_data = pd.DataFrame()

    def load_relevant_sheets(self, file_path: str, sheet_aliases: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """
        Load relevant sheets from an Excel file

        Args:
            file_path: Path to the Excel file
            sheet_aliases: Dictionary mapping sheet names to normalized names

        Returns:
            Dictionary of DataFrames with normalized sheet names as keys
        """
        try:
            xls = pd.ExcelFile(file_path)
            data = {}

            for sheet in xls.sheet_names:
                if sheet in sheet_aliases:
                    df = xls.parse(sheet)
                    df["Source File"] = os.path.basename(file_path)
                    df["Call Type"] = sheet_aliases[sheet]
                    data[sheet_aliases[sheet]] = df

            return data
        except Exception as e:
            logger.error(f"Error loading {file_path}: {str(e)}")
            return {}

    def load_data(self) -> bool:
        """
        Load data from all specified Excel files

        Returns:
            True if data was loaded successfully, False otherwise
        """
        try:
            # Load data from each file
            for path in self.file_paths:
                if not os.path.exists(path):
                    logger.warning(f"File not found: {path}")
                    continue

                sheets = self.load_relevant_sheets(path, SHEET_MAP)

                if "Inbound" in sheets:
                    self.inbound_data.append(sheets["Inbound"])
                if "Missed Call" in sheets:
                    self.missed_data.append(sheets["Missed Call"])
                if "Outbound" in sheets:
                    self.outbound_data.append(sheets["Outbound"])

            # Combine data from all files
            if self.inbound_data:
                self.df_inbound = pd.concat(self.inbound_data, ignore_index=True)
            if self.missed_data:
                self.df_missed = pd.concat(self.missed_data, ignore_index=True)
            if self.outbound_data:
                self.df_outbound = pd.concat(self.outbound_data, ignore_index=True)

            # Create combined dataset for cross-analysis
            dfs_to_combine = []
            if not self.df_inbound.empty:
                dfs_to_combine.append(self.df_inbound)
            if not self.df_missed.empty:
                dfs_to_combine.append(self.df_missed)
            if not self.df_outbound.empty:
                dfs_to_combine.append(self.df_outbound)

            if dfs_to_combine:
                self.combined_data = pd.concat(dfs_to_combine, ignore_index=True)

            return True
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return False

    def get_data(self, data_type: str = "all") -> Union[pd.DataFrame, Dict[str, pd.DataFrame]]:
        """
        Get the processed data

        Args:
            data_type: Type of data to return ('inbound', 'missed', 'outbound', 'combined', or 'all')

        Returns:
            DataFrame or dictionary of DataFrames
        """
        if data_type.lower() == "inbound":
            return self.df_inbound
        elif data_type.lower() == "missed":
            return self.df_missed
        elif data_type.lower() == "outbound":
            return self.df_outbound
        elif data_type.lower() == "combined":
            return self.combined_data
        elif data_type.lower() == "all":
            return {
                "inbound": self.df_inbound,
                "missed": self.df_missed,
                "outbound": self.df_outbound,
                "combined": self.combined_data
            }
        else:
            return pd.DataFrame()
```

**How to Use the Data Loader:**

```python
# Example usage
file_paths = ["data/Jan2025.xlsx", "data/Feb2025.xlsx"]
loader = CallFlowDataLoader(file_paths)
loader.load_data()

# Get all data
all_data = loader.get_data("all")

# Get just inbound calls
inbound_data = loader.get_data("inbound")

# Process the data
inbound_data["Call Date"] = pd.to_datetime(inbound_data["Call Date"])
call_counts = inbound_data.groupby(inbound_data["Call Date"].dt.date).size()
```

#### 2.2.2 Preprocessing

The preprocessing module (`app/shared/preprocessing.py`) includes:

- `DataPreprocessor`: General data preprocessing
- `TimeFeatureExtractor`: Extraction of time-related features
- `LanguageDetector`: Detection of language based on campaign name
- `CampaignCategorizer`: Categorization of campaigns

**Example: Time Feature Extraction**

```python
# In app/shared/preprocessing.py

class TimeFeatureExtractor:
    """Extractor for time-related features"""

    def extract_features(self, df: pd.DataFrame, date_col: str = "Call Date") -> pd.DataFrame:
        """
        Extract time-related features from a date column

        Args:
            df: DataFrame containing the date column
            date_col: Name of the date column

        Returns:
            DataFrame with additional time features
        """
        if date_col not in df.columns:
            return df

        try:
            # Make a copy to avoid modifying the original
            result_df = df.copy()

            # Ensure date column is datetime
            result_df[date_col] = pd.to_datetime(result_df[date_col])

            # Extract features
            result_df['hour'] = result_df[date_col].dt.hour
            result_df['day'] = result_df[date_col].dt.day
            result_df['weekday'] = result_df[date_col].dt.dayofweek
            result_df['weekday_name'] = result_df[date_col].dt.day_name()
            result_df['month'] = result_df[date_col].dt.month
            result_df['year'] = result_df[date_col].dt.year
            result_df['is_weekend'] = result_df['weekday'].isin([5, 6]).astype(int)

            # Time of day categories
            result_df['time_of_day'] = pd.cut(
                result_df['hour'],
                bins=[0, 6, 12, 18, 24],
                labels=['Night', 'Morning', 'Afternoon', 'Evening'],
                include_lowest=True
            )

            return result_df

        except Exception as e:
            logger.error(f"Error extracting time features: {str(e)}")
            return df
```

**How to Use the Preprocessor:**

```python
# Example usage
loader = CallFlowDataLoader(file_paths)
loader.load_data()
inbound_data = loader.get_data("inbound")

# Extract time features
time_extractor = TimeFeatureExtractor()
inbound_with_time_features = time_extractor.extract_features(inbound_data)

# Now you can analyze by time of day
calls_by_time_of_day = inbound_with_time_features.groupby("time_of_day").size()
print(calls_by_time_of_day)
```

### 2.3 Analytics Implementation

The analytics services (in `app/api/services/analyzers/`) implement the 21 analytics use cases, each in its own module:

1. `AgentPerformanceAnalyzer`: Analyzes agent performance metrics
2. `CallVolumePredictor`: Predicts call volumes
3. `StaffingOptimizer`: Optimizes staffing levels
4. `CampaignAnalyzer`: Analyzes campaign performance
5. `LanguageAnalyzer`: Analyzes performance across languages
6. `MissedCallAnalyzer`: Analyzes missed calls
7. `CustomerJourneyAnalyzer`: Analyzes customer journeys
8. `FCRAnalyzer`: Analyzes first call resolution
9. `SentimentAnalyzer`: Analyzes customer sentiment
10. `AgentSpecializationAnalyzer`: Recommends agent specializations
11. `ConversionRateOptimizer`: Optimizes conversion rates
12. `QueueOptimizer`: Optimizes queue management
13. `SeasonalTrendAnalyzer`: Analyzes seasonal trends
14. `GeographicAnalyzer`: Analyzes geographic insights
15. `CallQualityScorer`: Scores call quality
16. `ABTestingAnalyzer`: Analyzes A/B tests
17. `ChurnPredictor`: Predicts customer churn
18. `CrossSellingDetector`: Detects cross-selling opportunities
19. `AnomalyDetector`: Detects anomalies
20. `CallbackOptimizer`: Optimizes callback times
21. `KnowledgeBaseEnhancer`: Enhances knowledge base

### 2.4 Machine Learning Models

The ML models (`app/api/services/ml_models.py`) include:

- `BaseModel`: Abstract base class for all models
- `CallVolumeModel`: Predicts call volumes
- `AgentPerformanceModel`: Predicts agent performance
- `ChurnPredictionModel`: Predicts customer churn
- Additional specialized models

Each model includes methods for:
- Data preparation
- Feature engineering
- Model training
- Prediction
- Evaluation

## 3. Frontend Implementation

### 3.1 Streamlit Frontend

The frontend is implemented using Streamlit, a Python library for creating web applications with minimal code. Streamlit allows us to build interactive data applications without having to know HTML, CSS, or JavaScript.

#### 3.1.1 Main App

The main app (`app/frontend/app.py`) includes:
- Page configuration
- Navigation sidebar
- Session state management
- Page routing

**Example: Main Streamlit App**

```python
# In app/frontend/app.py

import streamlit as st
import os
import sys
import requests
from datetime import datetime, timedelta

# Set page config
def setup_page_config():
    """Set up the page configuration for Streamlit"""
    try:
        st.set_page_config(
            page_title="Call Flow Analytics",
            page_icon="📞",
            layout="wide",
            initial_sidebar_state="expanded",
        )
    except Exception as e:
        # If the config is already set, this will raise an exception
        # We can safely ignore it
        pass

# Set up page config
setup_page_config()

# Import API URL from config
try:
    from app.shared.config import API_URL
except ImportError:
    # Fallback if import fails
    API_URL = "http://127.0.0.1:8002"

# Define API endpoints
API_ENDPOINTS = {
    "upload": f"{API_URL}/data/upload",
    "data_info": f"{API_URL}/data/info",
    "agent_performance": f"{API_URL}/analytics/agent-performance",
    "call_volume": f"{API_URL}/analytics/call-volume",
    # More endpoints...
}

# Initialize session state
if "page" not in st.session_state:
    st.session_state.page = "Dashboard"

# Sidebar navigation
st.sidebar.title("Call Flow Analytics")
st.sidebar.image("app/frontend/assets/logo.png", width=200)

# Create the page selection dropdown
page = st.sidebar.selectbox(
    "Select Analysis",
    [
        "Dashboard",
        "Data Upload",
        "Agent Performance",
        "Call Volume Prediction",
        # More pages...
    ],
    index=0 if "page" not in st.session_state else [
        "Dashboard",
        "Data Upload",
        "Agent Performance",
        # More pages...
    ].index(st.session_state.page)
)

# Update session state
st.session_state.page = page

# Page routing
if page == "Dashboard":
    st.title("Call Flow Analytics Dashboard")
    # Dashboard content...

elif page == "Data Upload":
    # Import the data upload module
    try:
        from app.frontend.pages.data_upload import data_upload_page
        data_upload_page()
    except ImportError as e:
        st.error(f"Error importing data upload page: {str(e)}")

elif page == "Agent Performance":
    # Import the agent performance module
    try:
        from app.frontend.pages.agent_performance import agent_performance_page
        agent_performance_page()
    except ImportError as e:
        st.error(f"Error importing agent performance page: {str(e)}")

# More page routing...
```

**Key Streamlit Concepts:**

1. **Page Configuration**: `st.set_page_config()` sets up the page layout and appearance
2. **Session State**: `st.session_state` stores persistent data between reruns
3. **Sidebar**: `st.sidebar` creates a navigation sidebar
4. **Widgets**: `st.selectbox`, `st.button`, etc. create interactive UI elements
5. **Dynamic Imports**: We import page modules dynamically to keep the code modular

#### 3.1.2 Pages

Each analytics use case has a dedicated page in the frontend. Here's a list of the main pages:

- Dashboard
- Data Upload
- Agent Performance
- Call Volume Prediction
- Staffing Optimization
- Campaign Analysis
- Language Analysis
- Missed Call Analysis
- Customer Journey
- First Call Resolution
- Sentiment Analysis
- Agent Specialization
- Conversion Rate Optimization
- Queue Optimization
- Seasonal Trend Analysis
- Geographic Insights
- Call Quality Scoring
- A/B Testing Framework
- Churn Prediction
- Cross-selling Opportunities
- Anomaly Detection
- Callback Optimization
- Knowledge Base Enhancement
- Advanced ML Models
- Model Training
- Task Monitor
- Custom Reports

**Example: Page Implementation**

```python
# In app/frontend/pages/agent_performance.py

import streamlit as st
import pandas as pd
import plotly.express as px
import requests
from datetime import datetime, timedelta
import json

# Import API endpoints
from app.frontend.app import API_ENDPOINTS

def agent_performance_page():
    """Agent performance analysis page"""
    st.title("Agent Performance Analysis")

    # Sidebar filters
    st.sidebar.header("Filters")

    # Date range filter
    st.sidebar.subheader("Date Range")
    today = datetime.now().date()
    start_date = st.sidebar.date_input(
        "Start Date",
        today - timedelta(days=30)
    )
    end_date = st.sidebar.date_input(
        "End Date",
        today
    )

    # Agent filter
    st.sidebar.subheader("Agents")
    all_agents = ["Agent1", "Agent2", "Agent3"]  # This would come from the API
    selected_agents = st.sidebar.multiselect(
        "Select Agents",
        all_agents,
        default=all_agents
    )

    # Campaign filter
    st.sidebar.subheader("Campaigns")
    all_campaigns = ["Campaign1", "Campaign2", "Campaign3"]  # This would come from the API
    selected_campaigns = st.sidebar.multiselect(
        "Select Campaigns",
        all_campaigns,
        default=all_campaigns
    )

    # Run analysis button
    if st.sidebar.button("Run Analysis"):
        # Show loading spinner
        with st.spinner("Running agent performance analysis..."):
            try:
                # Prepare request data
                request_data = {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "agents": selected_agents,
                    "campaigns": selected_campaigns
                }

                # Call API
                response = requests.post(
                    API_ENDPOINTS["agent_performance"],
                    json=request_data,
                    timeout=30
                )

                # Check if request was successful
                if response.status_code == 200:
                    result = response.json()

                    if result["success"]:
                        # Display results
                        st.success("Analysis completed successfully!")

                        # Display agent performance metrics
                        st.header("Agent Performance Metrics")

                        # Convert data to DataFrame
                        agent_metrics = pd.DataFrame(result["data"]["agent_metrics"])

                        # Display metrics table
                        st.dataframe(agent_metrics)

                        # Display charts
                        st.header("Performance Charts")

                        # Process and display each chart
                        for chart in result["charts"]:
                            if chart["type"] == "bar":
                                fig = px.bar(
                                    x=chart["data"]["x"],
                                    y=chart["data"]["y"],
                                    title=chart["title"]
                                )
                                st.plotly_chart(fig, use_container_width=True)
                            elif chart["type"] == "line":
                                fig = px.line(
                                    x=chart["data"]["x"],
                                    y=chart["data"]["y"],
                                    title=chart["title"]
                                )
                                st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.error(f"Analysis failed: {result['message']}")
                else:
                    st.error(f"API request failed with status code {response.status_code}")

            except Exception as e:
                st.error(f"Error running analysis: {str(e)}")
    else:
        # Display instructions when the page first loads
        st.info("Use the filters in the sidebar to configure the analysis, then click 'Run Analysis'.")
```

### 3.2 User Interface Components

The frontend includes reusable UI components for:
- Data filtering
- Date range selection
- Chart visualization
- Table display
- File upload
- Parameter configuration
- Report generation

**Example: File Upload Component**

```python
# In app/frontend/components/file_uploader.py

import streamlit as st
import requests
import os
import pandas as pd

def file_upload_component(api_endpoint):
    """
    Reusable file upload component

    Args:
        api_endpoint: API endpoint for file upload
    """
    st.subheader("Upload Excel Files")

    # File uploader
    uploaded_files = st.file_uploader(
        "Upload Excel files with call data",
        type=["xlsx", "xls"],
        accept_multiple_files=True
    )

    if uploaded_files:
        # Display uploaded files
        st.write(f"Uploaded {len(uploaded_files)} files:")
        for file in uploaded_files:
            st.write(f"- {file.name} ({file.size} bytes)")

        # Process files button
        if st.button("Process Files"):
            with st.spinner("Processing files..."):
                try:
                    # Create a list to store file data
                    files = []

                    # Process each file
                    for file in uploaded_files:
                        # Save file temporarily
                        temp_path = f"temp_{file.name}"
                        with open(temp_path, "wb") as f:
                            f.write(file.getbuffer())

                        # Add to files list
                        files.append(
                            ("files", (file.name, open(temp_path, "rb"), "application/vnd.ms-excel"))
                        )

                    # Send files to API
                    response = requests.post(
                        api_endpoint,
                        files=files,
                        timeout=60
                    )

                    # Clean up temporary files
                    for file in files:
                        os.remove(file[1][1].name)
                        file[1][1].close()

                    # Check response
                    if response.status_code == 200:
                        result = response.json()

                        if result["success"]:
                            st.success(f"Files processed successfully! {result['message']}")

                            # Display data summary
                            if "data_summary" in result:
                                st.subheader("Data Summary")

                                summary = result["data_summary"]
                                col1, col2, col3 = st.columns(3)

                                with col1:
                                    st.metric("Inbound Calls", summary.get("inbound_count", 0))

                                with col2:
                                    st.metric("Outbound Calls", summary.get("outbound_count", 0))

                                with col3:
                                    st.metric("Missed Calls", summary.get("missed_count", 0))

                                # Display date range
                                if "date_range" in summary:
                                    st.write(f"Date Range: {summary['date_range']['start']} to {summary['date_range']['end']}")
                        else:
                            st.error(f"Error processing files: {result['message']}")
                    else:
                        st.error(f"API request failed with status code {response.status_code}")

                except Exception as e:
                    st.error(f"Error uploading files: {str(e)}")
    else:
        st.info("Please upload Excel files containing call data.")
```

**Example: Chart Component**

```python
# In app/frontend/components/charts.py

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd

def create_bar_chart(data, x_column, y_column, title, color_column=None):
    """
    Create a bar chart

    Args:
        data: DataFrame or dict with data
        x_column: Column name for x-axis
        y_column: Column name for y-axis
        title: Chart title
        color_column: Optional column name for color
    """
    # Convert dict to DataFrame if needed
    if isinstance(data, dict):
        data = pd.DataFrame(data)

    # Create chart
    if color_column:
        fig = px.bar(
            data,
            x=x_column,
            y=y_column,
            color=color_column,
            title=title
        )
    else:
        fig = px.bar(
            data,
            x=x_column,
            y=y_column,
            title=title
        )

    # Update layout
    fig.update_layout(
        xaxis_title=x_column,
        yaxis_title=y_column,
        legend_title=color_column if color_column else None,
        height=400
    )

    # Display chart
    st.plotly_chart(fig, use_container_width=True)
```

## 4. Background Task Processing

### 4.1 Celery Implementation

Celery is used for background task processing, with Redis as the message broker and result backend. This allows us to run time-consuming operations (like training ML models) without blocking the web interface.

#### 4.1.1 Celery Configuration

The Celery configuration (`app/celery_app.py`) includes:
- Redis connection settings
- Task serialization settings
- Task tracking settings
- Task state management

**Example: Celery Configuration**

```python
# In app/celery_app.py

from celery import Celery
import os
from celery.signals import task_postrun, task_prerun
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Redis URL - can be configured via environment variable
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# Create Celery app
celery_app = Celery("call_flow", broker=REDIS_URL, backend=REDIS_URL)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_publish_retry=True,
    worker_prefetch_multiplier=1,  # Process one task at a time per worker
    task_acks_late=True,  # Acknowledge tasks after they are executed
)

# Task state tracking
task_states = {}

@task_prerun.connect
def task_prerun_handler(task_id, task, *args, **kwargs):
    """Handler called before a task is run"""
    logger.info(f"Task started: {task_id}")
    task_states[task_id] = {
        "status": "STARTED",
        "progress": 0,
        "message": "Task started",
    }

@task_postrun.connect
def task_postrun_handler(task_id, task, retval, state, *args, **kwargs):
    """Handler called after a task is run"""
    logger.info(f"Task completed: {task_id} with state {state}")
    if task_id in task_states:
        task_states[task_id]["status"] = state
        task_states[task_id]["progress"] = 100
        task_states[task_id]["message"] = "Task completed"
        if retval:
            task_states[task_id]["result"] = retval

def get_task_info(task_id):
    """Get information about a task"""
    return task_states.get(task_id)

def get_all_tasks():
    """Get information about all tasks"""
    return task_states

def update_task_progress(task_id, progress, message=None):
    """Update task progress"""
    if task_id in task_states:
        task_states[task_id]["progress"] = min(max(0, progress), 100)
        if message:
            task_states[task_id]["message"] = message
        logger.info(f"Task {task_id} progress: {progress}% - {message}")
```

#### 4.1.2 Task Definitions

The task definitions (`app/tasks.py`) include:
- `train_call_volume_model`: Trains a call volume prediction model
- `train_agent_performance_model`: Trains an agent performance prediction model
- Additional task definitions for other ML models and analytics

**Example: Celery Task Definition**

```python
# In app/tasks.py

from app.celery_app import celery_app, update_task_progress
from app.api.services.ml_models import CallVolumeModel
from app.api.routers.data import get_data_loader, filter_data
import pandas as pd
import logging
from typing import Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@celery_app.task(
    bind=True,
    name="train_call_volume_model",
    queue="default",
    max_retries=3,
    soft_time_limit=3600,  # 1 hour timeout
    time_limit=3600 * 2,  # 2 hour hard timeout
    ignore_result=False,
    track_started=True,
)
def train_call_volume_model(
    self,
    date_col: str = "Call Date",
    granularity: str = "daily",
    model_type: str = "ensemble",
    filter_params: Optional[Dict[str, Any]] = None,
):
    """
    Celery task to train call volume model

    Args:
        date_col: Name of the date column
        granularity: Time granularity ('hourly', 'daily', 'weekly', 'monthly')
        model_type: Type of model to train ('linear', 'rf', 'gbm', 'xgb', 'lgb', 'ensemble')
        filter_params: Optional filter parameters for data

    Returns:
        Training result dictionary
    """
    task_id = self.request.id
    logger.info(f"Starting call volume model training task {task_id}")
    update_task_progress(task_id, 0, "Starting model training")

    try:
        # Get data loader
        data_loader = get_data_loader()

        # Get data
        data = data_loader.get_data("all")

        # Filter data if needed
        if filter_params:
            filtered_data = {
                key: filter_data(df, filter_params)
                for key, df in data.items()
                if not df.empty
            }
        else:
            filtered_data = data

        # Combine all data for training
        df_combined = filtered_data.get("combined", pd.DataFrame())

        if df_combined.empty:
            return {
                "success": False,
                "message": "No data available for training"
            }

        # Initialize model
        model = CallVolumeModel()

        # Update progress
        update_task_progress(task_id, 10, "Data prepared, starting model training")

        # Train model
        result = model.train(
            df_combined,
            date_col=date_col,
            granularity=granularity,
            model_type=model_type,
            task_id=task_id,
            update_progress=update_task_progress
        )

        # Update progress
        update_task_progress(task_id, 100, "Model training completed")

        return result

    except Exception as e:
        logger.error(f"Error in call volume model training task: {str(e)}")
        update_task_progress(task_id, 100, f"Error in model training: {str(e)}")
        return {"success": False, "message": f"Error in model training: {str(e)}"}
```

**How to Use Celery Tasks:**

```python
# In app/api/routers/tasks.py

@router.post("/train-model", response_model=TaskResponse)
async def start_model_training(request: ModelTrainingTaskRequest):
    """
    Start a background task to train a model
    """
    try:
        # Prepare task parameters
        task_kwargs = {
            "date_col": request.date_col,
            "granularity": request.granularity,
            "model_type": request.model_type,
            "filter_params": request.filter_params
        }

        # Send task to Celery
        task = celery_app.send_task(
            "train_call_volume_model",
            kwargs=task_kwargs,
        )

        return {
            "success": True,
            "message": "Model training task started",
            "data": {
                "model_name": request.model_name,
                "model_type": request.model_type,
                "granularity": request.granularity,
            },
            "task_id": task.id,
        }
    except Exception as e:
        logger.error(f"Error starting model training task: {str(e)}")
        return {
            "success": False,
            "message": f"Error starting task: {str(e)}",
            "data": None,
        }
```

### 4.2 Task Monitoring

#### 4.2.1 Task Manager

The task manager (`app/api/services/task_manager.py`) provides:
- Task creation and management
- Task status tracking
- Task result storage
- Task cancellation

**Example: Getting Task Status**

```python
# In app/api/routers/tasks.py

@router.get("/status/{task_id}", response_model=TaskInfoResponse)
async def get_task_status(task_id: str):
    """
    Get the status of a task
    """
    try:
        # Try to get task info from our tracking
        task_info = get_task_info(task_id)

        if not task_info:
            # If not found in our tracking, try to get from Celery
            task = celery_app.AsyncResult(task_id)

            if task.state == "PENDING":
                task_info = {
                    "status": "PENDING",
                    "progress": 0,
                    "message": "Task is pending",
                    "result": None,
                }
            elif task.state == "STARTED":
                task_info = {
                    "status": "RUNNING",
                    "progress": 0,
                    "message": "Task is running",
                    "result": None,
                }
            elif task.state == "SUCCESS":
                task_info = {
                    "status": "SUCCESS",
                    "progress": 100,
                    "message": "Task completed successfully",
                    "result": task.result,
                }
            elif task.state == "FAILURE":
                task_info = {
                    "status": "FAILURE",
                    "progress": 100,
                    "message": f"Task failed: {str(task.result)}",
                    "result": None,
                }
            else:
                task_info = {
                    "status": task.state,
                    "progress": 0,
                    "message": f"Task is in state {task.state}",
                    "result": None,
                }

        return {
            "success": True,
            "task_id": task_id,
            "task_info": task_info
        }
    except Exception as e:
        logger.error(f"Error getting task status: {str(e)}")
        return {
            "success": False,
            "task_id": task_id,
            "task_info": {
                "status": "ERROR",
                "progress": 0,
                "message": f"Error getting task status: {str(e)}",
                "result": None,
            }
        }
```

#### 4.2.2 Flower Monitoring

Flower is used for monitoring Celery tasks, providing:
- Real-time task monitoring
- Worker status monitoring
- Task history
- Task management (revoke, terminate)

**Running Flower:**

```bash
# Start Flower monitoring
./run_flower.sh

# Or manually
export PYTHONPATH=$PYTHONPATH:$(pwd)
celery -A app.celery_app flower --port=5555
```

**Accessing Flower:**

Once Flower is running, you can access it at http://localhost:5555 to see:
- Active tasks
- Task history
- Worker status
- Task details and results

**Integrating Flower with Streamlit:**

```python
# In app/frontend/task_monitor.py

def task_monitor_page():
    """Task monitor page"""
    st.title("Task Monitor")

    # Create tabs
    tab1, tab2 = st.tabs(["Task List", "Flower Monitor"])

    # Tab 1: Task List
    with tab1:
        st.subheader("Task List")

        # Refresh button
        if st.button("Refresh Task List"):
            st.rerun()

        # Get task list from API
        try:
            response = requests.get(API_ENDPOINTS["task_list"], timeout=5)
            tasks = response.json().get("tasks", [])

            if not tasks:
                st.info("No tasks found. Start a new task to see it here.")
            else:
                # Create a DataFrame for the tasks
                task_data = []
                for task in tasks:
                    task_data.append({
                        "Task ID": task.get("task_id", "Unknown"),
                        "Status": task.get("status", "Unknown"),
                        "Progress": task.get("progress", 0),
                        "Message": task.get("message", ""),
                        "Started": task.get("started_at", "Unknown"),
                        "Completed": task.get("completed_at", "Unknown"),
                    })

                # Display task table
                st.dataframe(pd.DataFrame(task_data))
        except Exception as e:
            st.error(f"Error getting task list: {str(e)}")

    # Tab 2: Flower Monitor
    with tab2:
        st.subheader("Flower Task Monitor")
        st.write("Flower is a web-based tool for monitoring Celery tasks.")

        # Display Flower link
        st.markdown(
            """
            <a href="http://localhost:5555" target="_blank">
                <button style="background-color: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                    🌸 Open Flower Monitor
                </button>
            </a>
            """,
            unsafe_allow_html=True,
        )
```

## 5. Deployment

### 5.1 Running the Application

The application can be run using:
- `run_app.py`: Python script to run both backend and frontend
- `run.sh`: Shell script to run both backend and frontend
- `run_celery.sh`: Shell script to run Celery worker
- `run_flower.sh`: Shell script to run Flower monitoring

### 5.2 Environment Setup

The application requires:
- Python 3.8+
- Redis server
- Required Python packages (specified in `requirements.txt`)

## 6. Extending the System

### 6.1 Adding New Analytics

To add a new analytics feature:
1. Create a new analyzer class in a new file in the `app/api/services/analyzers/` directory
2. Import the new analyzer in `app/api/services/analyzers/__init__.py`
3. Add a new endpoint in `app/api/routers/analytics.py`
4. Add a new request/response schema in `app/api/models/schemas.py`
5. Add a new page in the Streamlit frontend

### 6.2 Adding New ML Models

To add a new ML model:
1. Create a new model class in `app/api/services/ml_models.py`
2. Add training and prediction methods to the model class
3. Add a new endpoint in `app/api/routers/advanced.py`
4. Update the Advanced ML Models page in the Streamlit frontend

### 6.3 Adding New Report Types

To add a new report type:
1. Add new report generation methods in `app/api/services/report_generator.py`
2. Add a new endpoint in `app/api/routers/advanced.py`
3. Update the Custom Reports page in the Streamlit frontend

## 7. Common Issues and Troubleshooting

This section covers common issues that developers, especially junior developers, might encounter when working with the Call Flow Analytics System, along with solutions.

### 7.1 Python Path Issues

**Problem**: Import errors when running the application, such as `ModuleNotFoundError: No module named 'app'`.

**Solution**:
1. Make sure the Python path includes the project root directory:
   ```bash
   export PYTHONPATH=$PYTHONPATH:$(pwd)
   ```
2. Use the provided run scripts (`run.sh` or `run_app.py`) which set up the Python path correctly
3. If you're running components manually, make sure to set the Python path first:
   ```python
   import sys
   import os

   # Add project root to path
   project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
   if project_root not in sys.path:
       sys.path.insert(0, project_root)
   ```

### 7.2 Celery Connection Issues

**Problem**: Celery tasks are not being processed, or you see errors like `ConnectionError: Error 111 connecting to localhost:6379. Connection refused.`

**Solution**:
1. Make sure Redis is running:
   ```bash
   redis-server --daemonize yes
   ```
2. Check the Redis connection settings in `app/celery_app.py`
3. Ensure the Celery worker is running:
   ```bash
   ./run_celery.sh
   ```
4. Check Celery logs for errors:
   ```bash
   celery -A app.celery_app worker --loglevel=debug
   ```

### 7.3 Streamlit Session State Issues

**Problem**: Streamlit app loses state between interactions, or you see errors related to session state.

**Solution**:
1. Use `st.session_state` to store persistent data:
   ```python
   # Initialize session state
   if "data" not in st.session_state:
       st.session_state.data = None

   # Update session state
   st.session_state.data = new_data

   # Access session state
   if st.session_state.data is not None:
       # Use the data
       process_data(st.session_state.data)
   ```
2. Avoid using global variables in Streamlit apps
3. Remember that Streamlit reruns the entire script on each interaction

### 7.4 API Connection Issues

**Problem**: Frontend can't connect to the backend API, or you see errors like `ConnectionError: HTTPConnectionPool(host='127.0.0.1', port=8002): Max retries exceeded`.

**Solution**:
1. Make sure the FastAPI backend is running:
   ```bash
   uvicorn app.api.main:app --host 127.0.0.1 --port 8002 --reload
   ```
2. Check the API URL in `app/shared/config.py`
3. Ensure the ports match between the frontend and backend
4. Check for firewall or network issues if running on different machines

### 7.5 Data Processing Issues

**Problem**: Errors when processing Excel files, or data not showing up correctly.

**Solution**:
1. Check the Excel file format and sheet names
2. Make sure the required columns are present
3. Add debug logging to see what data is being processed:
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   logger = logging.getLogger(__name__)

   logger.debug(f"Processing file: {file_path}")
   logger.debug(f"Sheet names: {xls.sheet_names}")
   logger.debug(f"Columns in sheet: {df.columns.tolist()}")
   ```
4. Use pandas' built-in data validation:
   ```python
   # Check for required columns
   required_columns = ["Lead ID", "Phone", "Call Date"]
   missing_columns = [col for col in required_columns if col not in df.columns]
   if missing_columns:
       logger.error(f"Missing required columns: {missing_columns}")
       return False
   ```

### 7.6 Machine Learning Model Issues

**Problem**: ML models fail to train or make predictions.

**Solution**:
1. Check the input data format and make sure it matches what the model expects
2. Add more logging to see what's happening during training:
   ```python
   logger.debug(f"Training data shape: {df.shape}")
   logger.debug(f"Features: {X.columns.tolist()}")
   logger.debug(f"Model parameters: {model.get_params()}")
   ```
3. Start with simpler models before trying complex ones
4. Make sure you have enough data for training

### 7.7 Debugging Tips

1. **Use logging extensively**:
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   logger = logging.getLogger(__name__)

   logger.debug("Debug message")
   logger.info("Info message")
   logger.warning("Warning message")
   logger.error("Error message")
   ```

2. **Check API responses**:
   ```python
   response = requests.post(api_url, json=data)
   logger.debug(f"API Response: {response.status_code}")
   logger.debug(f"Response content: {response.text}")
   ```

3. **Use Streamlit's built-in debugging**:
   ```python
   st.write("Debug:", variable_to_debug)
   ```

4. **Use Python's debugger**:
   ```python
   import pdb
   pdb.set_trace()  # Code will pause here for interactive debugging
   ```

5. **Check Celery task status**:
   ```python
   task = celery_app.AsyncResult(task_id)
   print(f"Task state: {task.state}")
   print(f"Task result: {task.result}")
   ```

## 8. Conclusion

This technical documentation provides an overview of the Call Flow Analytics System implementation, with detailed examples and troubleshooting tips to help developers, especially junior developers, work with the system effectively.

For more detailed information, refer to the code comments and docstrings in the source code.

---

*For business and product information, please refer to the business documentation.*
