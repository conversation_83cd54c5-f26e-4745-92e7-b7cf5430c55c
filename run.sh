#!/bin/bash

# Set the PYTHONPATH to include all necessary directories
export PYTHONPATH=$PWD:$PWD/app:$PWD/app/frontend:$PWD/app/frontend/pages:$PYTHONPATH

# Start FastAPI backend
echo "Starting FastAPI backend..."
uvicorn app.api.main:app --host 127.0.0.1 --port 8002 --reload &
BACKEND_PID=$!

# Wait for backend to start
echo "Waiting for backend to start..."
sleep 5

# Start Streamlit frontend
echo "Starting Streamlit frontend..."
# Use the PYTHONPATH we set earlier
python run_streamlit.py &
FRONTEND_PID=$!

# Function to handle script termination
cleanup() {
    echo "Stopping services..."
    kill $BACKEND_PID
    kill $FRONTEND_PID
    exit 0
}

# Register the cleanup function for SIGINT and SIGTERM
trap cleanup SIGINT SIGTERM

# Keep the script running
echo "Services started. Press Ctrl+C to stop."
wait
