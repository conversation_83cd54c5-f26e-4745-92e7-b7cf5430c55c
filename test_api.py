import requests
import json
import pandas as pd
import time
import sys

# API base URL
API_URL = "http://127.0.0.1:8000"

def test_api_health():
    """Test API health endpoint"""
    try:
        response = requests.get(f"{API_URL}/health")
        if response.status_code == 200:
            print("✅ API health check passed")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API health check failed: {str(e)}")
        return False

def test_data_summary():
    """Test data summary endpoint"""
    try:
        response = requests.get(f"{API_URL}/data/summary")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ Data summary endpoint passed")
                print(f"   Summary: {json.dumps(data.get('data', {}), indent=2)[:100]}...")
                return True
            else:
                print(f"❌ Data summary endpoint failed: {data.get('message')}")
                return False
        else:
            print(f"❌ Data summary endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Data summary endpoint failed: {str(e)}")
        return False

def test_agent_performance():
    """Test agent performance endpoint"""
    try:
        payload = {
            "metrics": ["resolution_rate", "call_length", "call_volume"]
        }
        response = requests.post(f"{API_URL}/analytics/agent-performance", json=payload)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ Agent performance endpoint passed")
                print(f"   Charts: {len(data.get('charts', []))}")
                return True
            else:
                print(f"❌ Agent performance endpoint failed: {data.get('message')}")
                return False
        else:
            print(f"❌ Agent performance endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Agent performance endpoint failed: {str(e)}")
        return False

def test_call_volume_prediction():
    """Test call volume prediction endpoint"""
    try:
        payload = {
            "granularity": "daily"
        }
        response = requests.post(f"{API_URL}/analytics/call-volume-prediction", json=payload)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ Call volume prediction endpoint passed")
                print(f"   Forecast periods: {len(data.get('data', {}).get('forecast', []))}")
                return True
            else:
                print(f"❌ Call volume prediction endpoint failed: {data.get('message')}")
                return False
        else:
            print(f"❌ Call volume prediction endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Call volume prediction endpoint failed: {str(e)}")
        return False

def run_tests():
    """Run all tests"""
    print("🔍 Testing Call Flow Analytics API...")
    
    # Wait for API to start
    print("⏳ Waiting for API to start...")
    max_retries = 5
    for i in range(max_retries):
        if test_api_health():
            break
        if i < max_retries - 1:
            print(f"   Retrying in 2 seconds... ({i+1}/{max_retries})")
            time.sleep(2)
    
    # Run tests
    tests = [
        test_data_summary,
        test_agent_performance,
        test_call_volume_prediction
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    # Print summary
    print("\n📊 Test Summary:")
    print(f"   Passed: {results.count(True)}/{len(results)}")
    print(f"   Failed: {results.count(False)}/{len(results)}")
    
    return all(results)

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
