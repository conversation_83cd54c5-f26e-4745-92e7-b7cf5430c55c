"""
Test script for Celery tasks
"""
import os
import sys
import time

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the Celery app
from app.celery_app import celery_app

def test_call_volume_model():
    """Test the call volume model training task"""
    print("Sending task to Celery...")
    
    # Send the task
    task = celery_app.send_task(
        'train_call_volume_model',
        kwargs={
            'date_col': "Call Date",
            'granularity': "daily",
            'model_type': "ensemble",
            'filter_params': None
        }
    )
    
    print(f"Task sent with ID: {task.id}")
    print("Waiting for task to complete...")
    
    # Wait for the task to complete
    result = None
    for _ in range(10):  # Try for 10 seconds
        try:
            result = task.get(timeout=1)
            break
        except celery_app.backend.TimeoutError:
            print("Task still running...")
        except Exception as e:
            print(f"Error getting task result: {e}")
            break
    
    if result:
        print(f"Task completed with result: {result}")
    else:
        print("Task did not complete within the timeout period.")

if __name__ == '__main__':
    test_call_volume_model()
