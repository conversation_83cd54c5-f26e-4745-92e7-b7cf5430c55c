from app.celery_app import celery_app, update_task_progress
from app.api.services.ml_models import CallVolumeModel, AgentPerformanceModel
from app.api.routers.data import get_data_loader, filter_data
import pandas as pd
import logging
from typing import Dict, Any, Optional
import os
import sys
import traceback

# Set up logging with more detailed level
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Make sure the app module is in the Python path
app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# Log that this module is being loaded
logger.info("Loading tasks module")
logger.info(f"Python path: {sys.path}")

# Log the Celery app configuration
logger.info(f"Celery app: {celery_app}")
logger.info(f"Celery broker: {celery_app.conf.broker_url}")
logger.info(f"Celery backend: {celery_app.conf.result_backend}")


# Define the task with explicit name and queue
@celery_app.task(
    bind=True,
    name="train_call_volume_model",
    queue="default",
    max_retries=3,
    soft_time_limit=3600,  # 1 hour timeout
    time_limit=3600 * 2,  # 2 hour hard timeout
    ignore_result=False,
    track_started=True,
)
def train_call_volume_model(
    self,
    date_col: str = "Call Date",
    granularity: str = "daily",
    model_type: str = "ensemble",
    filter_params: Optional[Dict[str, Any]] = None,
):
    """
    Celery task to train call volume model

    Args:
        date_col: Name of the date column
        granularity: Time granularity ('hourly', 'daily', 'weekly', 'monthly')
        model_type: Type of model to train ('linear', 'rf', 'gbm', 'xgb', 'lgb', 'ensemble')
        filter_params: Optional filter parameters for data

    Returns:
        Training result dictionary
    """
    task_id = self.request.id
    logger.info(f"Starting call volume model training task {task_id}")
    update_task_progress(task_id, 0, "Starting model training")

    try:
        # Get data loader
        data_loader = get_data_loader()

        # Get data
        update_task_progress(task_id, 5, "Loading data")
        data = data_loader.get_data("all")

        # Filter data if filter parameters are provided
        if filter_params:
            update_task_progress(task_id, 10, "Filtering data")

            # Create a simple request object to use with filter_data
            class FilterRequest:
                pass

            filter_request = FilterRequest()
            for key, value in filter_params.items():
                setattr(filter_request, key, value)

            filtered_data = {
                key: filter_data(df, filter_request)
                for key, df in data.items()
                if not df.empty
            }
        else:
            filtered_data = data

        # Combine all data for training
        update_task_progress(task_id, 15, "Preparing data for training")
        df_combined = pd.DataFrame()
        for df in filtered_data.values():
            if not df.empty and "Call Date" in df.columns:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return {"success": False, "message": "No data available for model training"}

        # Initialize model
        update_task_progress(
            task_id, 20, f"Initializing {model_type} model for {granularity} prediction"
        )
        model = CallVolumeModel()

        # Define progress update function
        def progress_callback(task_id, progress, message):
            update_task_progress(task_id, progress, message)

        # Train model
        training_result = model.train(
            df_combined,
            date_col=date_col,
            granularity=granularity,
            model_type=model_type,
            task_id=task_id,
            update_progress=progress_callback,
        )

        if training_result["success"]:
            update_task_progress(task_id, 100, "Model training completed successfully")
            logger.info(
                f"Call volume model training completed successfully: {training_result['metrics']}"
            )
        else:
            error_message = training_result.get("message", "Unknown error")
            update_task_progress(
                task_id, 100, f"Model training failed: {error_message}"
            )
            logger.error(f"Call volume model training failed: {error_message}")

        return training_result

    except Exception as e:
        logger.error(f"Error in call volume model training task: {str(e)}")
        import traceback

        logger.error(traceback.format_exc())

        update_task_progress(task_id, 100, f"Error in model training: {str(e)}")
        return {"success": False, "message": f"Error in model training: {str(e)}"}


# Define the agent performance model task with explicit name and queue
@celery_app.task(
    bind=True,
    name="train_agent_performance_model",
    queue="default",
    max_retries=3,
    soft_time_limit=3600,  # 1 hour timeout
    time_limit=3600 * 2,  # 2 hour hard timeout
    ignore_result=False,
    track_started=True,
)
def train_agent_performance_model(self, filter_params: Optional[Dict[str, Any]] = None):
    """
    Celery task to train agent performance model

    Args:
        filter_params: Optional filter parameters for data

    Returns:
        Training result dictionary
    """
    task_id = self.request.id
    logger.info(f"Starting agent performance model training task {task_id}")
    update_task_progress(task_id, 0, "Starting model training")

    try:
        # Get data loader
        data_loader = get_data_loader()

        # Get data
        update_task_progress(task_id, 10, "Loading data")
        data = data_loader.get_data("all")

        # Filter data if filter parameters are provided
        if filter_params:
            update_task_progress(task_id, 20, "Filtering data")

            # Create a simple request object to use with filter_data
            class FilterRequest:
                pass

            filter_request = FilterRequest()
            for key, value in filter_params.items():
                setattr(filter_request, key, value)

            filtered_data = {
                key: filter_data(df, filter_request)
                for key, df in data.items()
                if not df.empty
            }
        else:
            filtered_data = data

        # Combine all data for training
        update_task_progress(task_id, 30, "Preparing data for training")
        df_combined = pd.DataFrame()
        for df in filtered_data.values():
            if not df.empty:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return {"success": False, "message": "No data available for model training"}

        # Initialize model
        update_task_progress(task_id, 40, "Initializing agent performance model")
        model = AgentPerformanceModel()

        # Train model
        update_task_progress(task_id, 50, "Training model")
        training_result = model.train(df_combined)

        if training_result["success"]:
            update_task_progress(task_id, 100, "Model training completed successfully")
            logger.info(f"Agent performance model training completed successfully")
        else:
            error_message = training_result.get("message", "Unknown error")
            update_task_progress(
                task_id, 100, f"Model training failed: {error_message}"
            )
            logger.error(f"Agent performance model training failed: {error_message}")

        return training_result

    except Exception as e:
        logger.error(f"Error in agent performance model training task: {str(e)}")
        import traceback

        logger.error(traceback.format_exc())

        update_task_progress(task_id, 100, f"Error in model training: {str(e)}")
        return {"success": False, "message": f"Error in model training: {str(e)}"}
