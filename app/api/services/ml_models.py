import pandas as pd
import numpy as np
import os
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union

# ML libraries
from sklearn.ensemble import (
    RandomForestRegressor,
    GradientBoostingRegressor,
    RandomForestClassifier,
)
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.svm import SVR, SVC
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit
from sklearn.metrics import (
    mean_squared_error,
    mean_absolute_error,
    r2_score,
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
)
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer

# Time series specific
import statsmodels.api as sm
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing

# XGBoost and LightGBM for better performance
import xgboost as xgb
import lightgbm as lgb

# For model persistence
from app.shared.config import MODELS_DIR

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BaseModel:
    """Base class for all ML models"""

    def __init__(self, model_name: str):
        """
        Initialize the model

        Args:
            model_name: Name of the model for saving/loading
        """
        self.model_name = model_name
        self.model = None
        self.preprocessor = None
        self.feature_names = None
        self.target_name = None
        self.model_path = os.path.join(MODELS_DIR, f"{model_name}.joblib")

    def save_model(self) -> bool:
        """
        Save the model to disk

        Returns:
            True if successful, False otherwise
        """
        try:
            if self.model is None:
                logger.warning(f"Cannot save {self.model_name}: model not trained")
                return False

            # Create directory if it doesn't exist
            os.makedirs(MODELS_DIR, exist_ok=True)

            # Save model data
            model_data = {
                "model": self.model,
                "preprocessor": self.preprocessor,
                "feature_names": self.feature_names,
                "target_name": self.target_name,
                "timestamp": datetime.now().isoformat(),
            }

            joblib.dump(model_data, self.model_path)
            logger.info(f"Model {self.model_name} saved to {self.model_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving model {self.model_name}: {str(e)}")
            return False

    def load_model(self) -> bool:
        """
        Load the model from disk

        Returns:
            True if successful, False otherwise
        """
        try:
            if not os.path.exists(self.model_path):
                logger.warning(f"Model file not found: {self.model_path}")
                return False

            # Load model data
            model_data = joblib.load(self.model_path)

            self.model = model_data.get("model")
            self.preprocessor = model_data.get("preprocessor")
            self.feature_names = model_data.get("feature_names")
            self.target_name = model_data.get("target_name")

            logger.info(f"Model {self.model_name} loaded from {self.model_path}")
            return True

        except Exception as e:
            logger.error(f"Error loading model {self.model_name}: {str(e)}")
            return False

    def preprocess_data(self, X: pd.DataFrame) -> np.ndarray:
        """
        Preprocess data using the fitted preprocessor

        Args:
            X: Input features

        Returns:
            Preprocessed features
        """
        if self.preprocessor is None:
            logger.warning("Preprocessor not fitted, returning original data")
            return X

        return self.preprocessor.transform(X)


class CallVolumeModel(BaseModel):
    """Advanced model for call volume prediction"""

    def __init__(self, model_name: str = "call_volume_model"):
        """Initialize the call volume model"""
        super().__init__(model_name)
        self.time_features = ["hour", "day", "weekday", "month", "is_weekend"]

    def prepare_features(
        self, df: pd.DataFrame, date_col: str = "Call Date"
    ) -> pd.DataFrame:
        """
        Prepare features for call volume prediction

        Args:
            df: DataFrame with call data
            date_col: Name of the date column

        Returns:
            DataFrame with prepared features
        """
        # Ensure date column is datetime
        df[date_col] = pd.to_datetime(df[date_col])

        # Extract time features
        features = pd.DataFrame()
        features["datetime"] = df[date_col]
        features["hour"] = df[date_col].dt.hour
        features["day"] = df[date_col].dt.day
        features["weekday"] = df[date_col].dt.dayofweek
        features["month"] = df[date_col].dt.month
        features["year"] = df[date_col].dt.year
        features["is_weekend"] = (features["weekday"] >= 5).astype(int)

        # Add cyclical encoding for time features
        features["hour_sin"] = np.sin(2 * np.pi * features["hour"] / 24)
        features["hour_cos"] = np.cos(2 * np.pi * features["hour"] / 24)
        features["day_sin"] = np.sin(2 * np.pi * features["day"] / 31)
        features["day_cos"] = np.cos(2 * np.pi * features["day"] / 31)
        features["month_sin"] = np.sin(2 * np.pi * features["month"] / 12)
        features["month_cos"] = np.cos(2 * np.pi * features["month"] / 12)
        features["weekday_sin"] = np.sin(2 * np.pi * features["weekday"] / 7)
        features["weekday_cos"] = np.cos(2 * np.pi * features["weekday"] / 7)

        return features

    def aggregate_by_time(
        self, df: pd.DataFrame, date_col: str = "Call Date", granularity: str = "hourly"
    ) -> pd.DataFrame:
        """
        Aggregate call data by time period

        Args:
            df: DataFrame with call data
            date_col: Name of the date column
            granularity: Time granularity ('hourly', 'daily', 'weekly', 'monthly')

        Returns:
            DataFrame with aggregated call volumes
        """
        # Ensure date column is datetime
        df[date_col] = pd.to_datetime(df[date_col])

        # Group by time period
        if granularity == "hourly":
            df["time_group"] = df[date_col].dt.floor("H")
        elif granularity == "daily":
            df["time_group"] = df[date_col].dt.floor("D")
        elif granularity == "weekly":
            df["time_group"] = df[date_col].dt.to_period("W").dt.start_time
        else:  # monthly
            df["time_group"] = df[date_col].dt.to_period("M").dt.start_time

        # Count calls by time period
        call_counts = df.groupby("time_group").size().reset_index(name="call_count")
        call_counts = call_counts.sort_values("time_group")

        # Add features
        features = self.prepare_features(call_counts, "time_group")
        call_counts = pd.concat(
            [call_counts, features.drop(["datetime"], axis=1)], axis=1
        )

        return call_counts

    def _get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance from the trained model

        Returns:
            Dictionary mapping feature names to importance scores
        """
        try:
            if self.model is None or self.feature_names is None:
                return {}

            # Extract the regressor from the pipeline
            if (
                hasattr(self.model, "named_steps")
                and "regressor" in self.model.named_steps
            ):
                regressor = self.model.named_steps["regressor"]

                # Different models have different ways to access feature importance
                if hasattr(regressor, "feature_importances_"):
                    # For tree-based models like RandomForest, GBM, XGBoost
                    importances = regressor.feature_importances_
                    return {
                        feature: float(importance)
                        for feature, importance in zip(self.feature_names, importances)
                    }
                elif hasattr(regressor, "coef_"):
                    # For linear models
                    importances = np.abs(regressor.coef_)
                    return {
                        feature: float(importance)
                        for feature, importance in zip(self.feature_names, importances)
                    }
                elif hasattr(regressor, "models") and hasattr(regressor, "predict"):
                    # For our custom ensemble model, average the feature importances
                    importances = {}
                    count = 0

                    for model_name, model_instance in regressor.models.items():
                        if hasattr(model_instance, "feature_importances_"):
                            if not importances:
                                importances = {
                                    feature: 0.0 for feature in self.feature_names
                                }

                            for i, feature in enumerate(self.feature_names):
                                importances[
                                    feature
                                ] += model_instance.feature_importances_[i]
                            count += 1

                    if count > 0:
                        for feature in importances:
                            importances[feature] /= count

                        return importances

            return {}
        except Exception as e:
            logger.warning(f"Error getting feature importance: {str(e)}")
            return {}

    def train(
        self,
        df: pd.DataFrame,
        date_col: str = "Call Date",
        granularity: str = "hourly",
        model_type: str = "ensemble",
        task_id: str = None,
        update_progress: callable = None,
    ) -> Dict[str, Any]:
        """
        Train the call volume prediction model

        Args:
            df: DataFrame with call data
            date_col: Name of the date column
            granularity: Time granularity ('hourly', 'daily', 'weekly', 'monthly')
            model_type: Type of model to train ('linear', 'rf', 'gbm', 'xgb', 'lgb', 'ensemble')
            task_id: Optional task ID for background training
            update_progress: Optional callback function to update progress

        Returns:
            Dictionary with training results
        """
        try:
            # Update progress if available
            if task_id and update_progress:
                update_progress(task_id, 5, "Validating input data")

            # Validate input data
            if df.empty:
                return {"success": False, "message": "Input data is empty"}

            # Check if date column exists
            if date_col not in df.columns:
                return {
                    "success": False,
                    "message": f"Date column '{date_col}' not found in input data",
                }

            # Update progress
            if task_id and update_progress:
                update_progress(task_id, 10, "Aggregating data by time period")

            # Aggregate data by time period
            call_counts = self.aggregate_by_time(df, date_col, granularity)

            if len(call_counts) < 10:
                return {
                    "success": False,
                    "message": f"Not enough data points for {granularity} prediction (need at least 10, got {len(call_counts)})",
                }

            # Update progress
            if task_id and update_progress:
                update_progress(task_id, 20, "Preparing features and target")

            # Prepare features and target
            feature_cols = [
                "hour",
                "day",
                "weekday",
                "month",
                "year",
                "is_weekend",
                "hour_sin",
                "hour_cos",
                "day_sin",
                "day_cos",
                "month_sin",
                "month_cos",
                "weekday_sin",
                "weekday_cos",
            ]

            # Keep only features that exist in the data
            feature_cols = [col for col in feature_cols if col in call_counts.columns]

            if not feature_cols:
                return {
                    "success": False,
                    "message": "No valid features found in the data",
                }

            if "call_count" not in call_counts.columns:
                return {
                    "success": False,
                    "message": "Target column 'call_count' not found in aggregated data",
                }

            X = call_counts[feature_cols]
            y = call_counts["call_count"]

            # Check if we have enough data for splitting
            if len(X) < 10:
                return {
                    "success": False,
                    "message": f"Not enough data points after feature extraction (need at least 10, got {len(X)})",
                }

            # Update progress
            if task_id and update_progress:
                update_progress(task_id, 30, "Splitting data for training and testing")

            # Split data for training and testing
            # Use time series split for time-based data
            try:
                tscv = TimeSeriesSplit(n_splits=min(5, len(X) // 2))
                splits = list(tscv.split(X))
                if not splits:
                    return {
                        "success": False,
                        "message": f"Could not create time series splits with {len(X)} data points",
                    }
                train_index, test_index = splits[-1]
            except Exception as split_error:
                logger.warning(
                    f"Error in time series split: {str(split_error)}. Falling back to simple train/test split."
                )
                # Fallback to simple train/test split
                train_size = int(len(X) * 0.8)
                train_index = list(range(train_size))
                test_index = list(range(train_size, len(X)))

            X_train, X_test = X.iloc[train_index], X.iloc[test_index]
            y_train, y_test = y.iloc[train_index], y.iloc[test_index]

            # Update progress
            if task_id and update_progress:
                update_progress(task_id, 40, f"Creating {model_type} model")

            # Create preprocessor
            numeric_features = feature_cols

            numeric_transformer = Pipeline(
                steps=[
                    ("imputer", SimpleImputer(strategy="median")),
                    ("scaler", StandardScaler()),
                ]
            )

            preprocessor = ColumnTransformer(
                transformers=[("num", numeric_transformer, numeric_features)]
            )

            # Train model based on type
            if model_type == "linear":
                model = Pipeline(
                    [("preprocessor", preprocessor), ("regressor", LinearRegression())]
                )
            elif model_type == "rf":
                model = Pipeline(
                    [
                        ("preprocessor", preprocessor),
                        (
                            "regressor",
                            RandomForestRegressor(n_estimators=100, random_state=42),
                        ),
                    ]
                )
            elif model_type == "gbm":
                model = Pipeline(
                    [
                        ("preprocessor", preprocessor),
                        (
                            "regressor",
                            GradientBoostingRegressor(
                                n_estimators=100, random_state=42
                            ),
                        ),
                    ]
                )
            elif model_type == "xgb":
                try:
                    model = Pipeline(
                        [
                            ("preprocessor", preprocessor),
                            (
                                "regressor",
                                xgb.XGBRegressor(n_estimators=100, random_state=42),
                            ),
                        ]
                    )
                except Exception as xgb_error:
                    logger.warning(
                        f"Error creating XGBoost model: {str(xgb_error)}. Falling back to GBM."
                    )
                    model = Pipeline(
                        [
                            ("preprocessor", preprocessor),
                            (
                                "regressor",
                                GradientBoostingRegressor(
                                    n_estimators=100, random_state=42
                                ),
                            ),
                        ]
                    )
            elif model_type == "lgb":
                try:
                    model = Pipeline(
                        [
                            ("preprocessor", preprocessor),
                            (
                                "regressor",
                                lgb.LGBMRegressor(n_estimators=100, random_state=42),
                            ),
                        ]
                    )
                except Exception as lgb_error:
                    logger.warning(
                        f"Error creating LightGBM model: {str(lgb_error)}. Falling back to GBM."
                    )
                    model = Pipeline(
                        [
                            ("preprocessor", preprocessor),
                            (
                                "regressor",
                                GradientBoostingRegressor(
                                    n_estimators=100, random_state=42
                                ),
                            ),
                        ]
                    )
            else:  # ensemble
                # Update progress
                if task_id and update_progress:
                    update_progress(task_id, 45, "Creating ensemble model")

                # Train multiple models and use voting
                models = {}

                # Try to add RandomForest
                try:
                    models["rf"] = RandomForestRegressor(
                        n_estimators=100, random_state=42
                    )
                except Exception as rf_error:
                    logger.warning(
                        f"Error creating RandomForest model: {str(rf_error)}"
                    )

                # Try to add GBM
                try:
                    models["gbm"] = GradientBoostingRegressor(
                        n_estimators=100, random_state=42
                    )
                except Exception as gbm_error:
                    logger.warning(f"Error creating GBM model: {str(gbm_error)}")

                # Try to add XGBoost
                try:
                    models["xgb"] = xgb.XGBRegressor(n_estimators=100, random_state=42)
                except Exception as xgb_error:
                    logger.warning(f"Error creating XGBoost model: {str(xgb_error)}")

                if not models:
                    # If no models could be created, fall back to a simple linear model
                    logger.warning(
                        "No ensemble models could be created. Falling back to linear regression."
                    )
                    model = Pipeline(
                        [
                            ("preprocessor", preprocessor),
                            ("regressor", LinearRegression()),
                        ]
                    )
                else:
                    # Preprocess data
                    X_train_prep = preprocessor.fit_transform(X_train)
                    X_test_prep = preprocessor.transform(X_test)

                    # Train each model
                    trained_models = {}
                    predictions = {}

                    model_count = len(models)
                    for i, (name, model_instance) in enumerate(models.items()):
                        # Update progress for each model
                        if task_id and update_progress:
                            progress = 50 + int((i / model_count) * 20)
                            update_progress(task_id, progress, f"Training {name} model")

                        try:
                            model_instance.fit(X_train_prep, y_train)
                            trained_models[name] = model_instance
                            predictions[name] = model_instance.predict(X_test_prep)
                        except Exception as train_error:
                            logger.warning(
                                f"Error training {name} model: {str(train_error)}"
                            )

                    if not trained_models:
                        # If no models could be trained, fall back to a simple linear model
                        logger.warning(
                            "No ensemble models could be trained. Falling back to linear regression."
                        )
                        model = Pipeline(
                            [
                                ("preprocessor", preprocessor),
                                ("regressor", LinearRegression()),
                            ]
                        )
                    else:
                        # Create ensemble model (simple average)
                        class EnsembleModel:
                            def __init__(self, models):
                                self.models = models

                            def fit(self, X, y):
                                # The models are already fitted, so this is just a placeholder
                                # to satisfy the scikit-learn API
                                return self

                            def predict(self, X):
                                preds = np.array(
                                    [model.predict(X) for model in self.models.values()]
                                )
                                return np.mean(preds, axis=0)

                        model = Pipeline(
                            [
                                ("preprocessor", preprocessor),
                                ("regressor", EnsembleModel(trained_models)),
                            ]
                        )

            # Update progress
            if task_id and update_progress:
                update_progress(task_id, 70, "Fitting model to training data")

            # Fit the model
            try:
                model.fit(X_train, y_train)
            except Exception as fit_error:
                return {
                    "success": False,
                    "message": f"Error fitting model: {str(fit_error)}",
                }

            # Update progress
            if task_id and update_progress:
                update_progress(task_id, 80, "Evaluating model performance")

            # Evaluate model
            try:
                y_pred = model.predict(X_test)

                # Calculate metrics
                mse = mean_squared_error(y_test, y_pred)
                rmse = np.sqrt(mse)
                mae = mean_absolute_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)
            except Exception as eval_error:
                return {
                    "success": False,
                    "message": f"Error evaluating model: {str(eval_error)}",
                }

            # Update progress
            if task_id and update_progress:
                update_progress(task_id, 90, "Saving model")

            # Save model attributes
            self.model = model
            self.preprocessor = preprocessor
            self.feature_names = feature_cols
            self.target_name = "call_count"

            # Save model to disk
            save_success = self.save_model()
            if not save_success:
                logger.warning(
                    "Model trained successfully but could not be saved to disk"
                )

            # Get feature importance if available
            feature_importance = self._get_feature_importance()

            # Update progress
            if task_id and update_progress:
                update_progress(task_id, 100, "Model training completed successfully")

            return {
                "success": True,
                "metrics": {"mse": mse, "rmse": rmse, "mae": mae, "r2": r2},
                "feature_importance": feature_importance,
                "data_points": len(call_counts),
                "features_used": feature_cols,
                "model_type": model_type,
                "granularity": granularity,
            }

        except Exception as e:
            logger.error(f"Error training call volume model: {str(e)}")
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"success": False, "message": f"Error training model: {str(e)}"}

    def predict(
        self,
        df: pd.DataFrame,
        date_col: str = "Call Date",
        granularity: str = "hourly",
        periods: int = 24,
    ) -> Dict[str, Any]:
        """
        Predict future call volumes

        Args:
            df: DataFrame with historical call data
            date_col: Name of the date column
            granularity: Time granularity ('hourly', 'daily', 'weekly', 'monthly')
            periods: Number of periods to forecast

        Returns:
            Dictionary with prediction results
        """
        try:
            # Check if model is trained or load from disk
            if self.model is None:
                if not self.load_model():
                    # Train model if not available
                    training_result = self.train(df, date_col, granularity)
                    if not training_result["success"]:
                        return training_result

            # Aggregate historical data
            historical_data = self.aggregate_by_time(df, date_col, granularity)

            if len(historical_data) < 5:
                return {
                    "success": False,
                    "message": f"Not enough historical data for {granularity} prediction (need at least 5 periods)",
                }

            # Generate future time periods
            last_date = historical_data["time_group"].max()

            if granularity == "hourly":
                future_dates = [
                    last_date + timedelta(hours=i + 1) for i in range(periods)
                ]
            elif granularity == "daily":
                future_dates = [
                    last_date + timedelta(days=i + 1) for i in range(periods)
                ]
            elif granularity == "weekly":
                future_dates = [
                    last_date + timedelta(weeks=i + 1) for i in range(periods)
                ]
            else:  # monthly
                future_dates = [
                    last_date.replace(
                        month=((last_date.month + i) % 12) or 12,
                        year=last_date.year + ((last_date.month + i - 1) // 12),
                    )
                    for i in range(1, periods + 1)
                ]

            # Create future features
            future_df = pd.DataFrame({"time_group": future_dates})
            future_features = self.prepare_features(future_df, "time_group")

            # Keep only the features used in training
            if self.feature_names:
                future_features = future_features[
                    [
                        col
                        for col in self.feature_names
                        if col in future_features.columns
                    ]
                ]

            # Make predictions
            future_volumes = self.model.predict(future_features)

            # Ensure non-negative predictions
            future_volumes = np.maximum(0, future_volumes)

            # Format results
            forecast = []
            for i, date in enumerate(future_dates):
                forecast.append(
                    {
                        "date": date.strftime("%Y-%m-%d %H:%M:%S"),
                        "call_volume": int(round(future_volumes[i])),
                    }
                )

            return {"success": True, "forecast": forecast, "granularity": granularity}

        except Exception as e:
            logger.error(f"Error predicting call volumes: {str(e)}")
            return {
                "success": False,
                "message": f"Error predicting call volumes: {str(e)}",
            }


class AgentPerformanceModel(BaseModel):
    """Advanced model for agent performance prediction"""

    def __init__(self, model_name: str = "agent_performance_model"):
        """Initialize the agent performance model"""
        super().__init__(model_name)

    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare features for agent performance prediction

        Args:
            df: DataFrame with call data

        Returns:
            DataFrame with prepared features
        """
        # Extract features
        features = pd.DataFrame()

        # Agent features
        if "User" in df.columns:
            # Get agent call counts
            agent_counts = (
                df.groupby("User").size().reset_index(name="agent_call_count")
            )
            features = pd.merge(df, agent_counts, on="User", how="left")

        # Call features
        if "Length" in df.columns:
            features["call_length"] = df["Length"]

        # Time features
        if "Call Date" in df.columns:
            df["Call Date"] = pd.to_datetime(df["Call Date"])
            features["hour"] = df["Call Date"].dt.hour
            features["weekday"] = df["Call Date"].dt.dayofweek
            features["is_weekend"] = (features["weekday"] >= 5).astype(int)

        # Campaign features
        if "Campaign" in df.columns:
            # One-hot encode campaigns
            campaign_dummies = pd.get_dummies(df["Campaign"], prefix="campaign")
            features = pd.concat([features, campaign_dummies], axis=1)

        # Language features
        if "Language" in df.columns:
            # One-hot encode languages
            language_dummies = pd.get_dummies(df["Language"], prefix="language")
            features = pd.concat([features, language_dummies], axis=1)

        return features

    def train(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Train the agent performance prediction model

        Args:
            df: DataFrame with call data

        Returns:
            Dictionary with training results
        """
        # Implementation will go here
        pass


# Add more advanced models as needed
class ChurnPredictionModel(BaseModel):
    """Advanced model for customer churn prediction"""

    def __init__(self, model_name: str = "churn_prediction_model"):
        """Initialize the churn prediction model"""
        super().__init__(model_name)

    def train(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Train the churn prediction model

        Args:
            df: DataFrame with call data

        Returns:
            Dictionary with training results
        """
        # Implementation will go here
        pass
