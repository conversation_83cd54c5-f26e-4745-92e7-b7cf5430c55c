import pandas as pd
import numpy as np
import os
import io
import base64
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union, BinaryIO
import json
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.figure import Figure
from matplotlib.backends.backend_pdf import PdfPages
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# For PDF generation
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak
from reportlab.lib.units import inch

# For Excel generation
import xlsxwriter

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ReportGenerator:
    """Class for generating custom reports"""
    
    def __init__(self, title: str = "Call Flow Analytics Report"):
        """
        Initialize the report generator
        
        Args:
            title: Report title
        """
        self.title = title
        self.sections = []
        self.charts = []
        self.tables = []
        self.text_blocks = []
        self.metadata = {
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'title': title
        }
    
    def add_section(self, title: str, content: Optional[str] = None) -> int:
        """
        Add a section to the report
        
        Args:
            title: Section title
            content: Optional section content
            
        Returns:
            Section index
        """
        section = {
            'title': title,
            'content': content,
            'items': []
        }
        
        self.sections.append(section)
        return len(self.sections) - 1
    
    def add_chart(self, section_index: int, chart_data: Dict[str, Any], 
                 title: Optional[str] = None, description: Optional[str] = None) -> None:
        """
        Add a chart to a section
        
        Args:
            section_index: Index of the section to add the chart to
            chart_data: Chart data in the standard format
            title: Optional chart title (overrides chart_data title)
            description: Optional chart description
        """
        if section_index < 0 or section_index >= len(self.sections):
            logger.warning(f"Invalid section index: {section_index}")
            return
        
        # Create a copy of chart data to avoid modifying the original
        chart = chart_data.copy()
        
        # Override title if provided
        if title:
            chart['title'] = title
        
        # Add description
        if description:
            chart['description'] = description
        
        # Add to section
        self.sections[section_index]['items'].append({
            'type': 'chart',
            'data': chart
        })
        
        # Add to charts list
        self.charts.append(chart)
    
    def add_table(self, section_index: int, data: Union[pd.DataFrame, List[Dict[str, Any]]], 
                 title: Optional[str] = None, description: Optional[str] = None) -> None:
        """
        Add a table to a section
        
        Args:
            section_index: Index of the section to add the table to
            data: Table data as DataFrame or list of dictionaries
            title: Optional table title
            description: Optional table description
        """
        if section_index < 0 or section_index >= len(self.sections):
            logger.warning(f"Invalid section index: {section_index}")
            return
        
        # Convert to DataFrame if needed
        if not isinstance(data, pd.DataFrame):
            data = pd.DataFrame(data)
        
        # Create table data
        table = {
            'title': title,
            'description': description,
            'data': data
        }
        
        # Add to section
        self.sections[section_index]['items'].append({
            'type': 'table',
            'data': table
        })
        
        # Add to tables list
        self.tables.append(table)
    
    def add_text(self, section_index: int, text: str, 
                heading: Optional[str] = None) -> None:
        """
        Add text to a section
        
        Args:
            section_index: Index of the section to add the text to
            text: Text content
            heading: Optional heading
        """
        if section_index < 0 or section_index >= len(self.sections):
            logger.warning(f"Invalid section index: {section_index}")
            return
        
        # Create text block
        text_block = {
            'heading': heading,
            'text': text
        }
        
        # Add to section
        self.sections[section_index]['items'].append({
            'type': 'text',
            'data': text_block
        })
        
        # Add to text blocks list
        self.text_blocks.append(text_block)
    
    def add_metadata(self, key: str, value: Any) -> None:
        """
        Add metadata to the report
        
        Args:
            key: Metadata key
            value: Metadata value
        """
        self.metadata[key] = value
    
    def _render_chart_matplotlib(self, chart_data: Dict[str, Any], figsize: Tuple[int, int] = (8, 5)) -> Figure:
        """
        Render a chart using matplotlib
        
        Args:
            chart_data: Chart data
            figsize: Figure size
            
        Returns:
            Matplotlib figure
        """
        chart_type = chart_data.get('chart_type', '')
        title = chart_data.get('title', '')
        x_label = chart_data.get('x_label', '')
        y_label = chart_data.get('y_label', '')
        data = chart_data.get('data', {})
        
        fig, ax = plt.subplots(figsize=figsize)
        
        if chart_type == 'bar':
            ax.bar(data.get('x', []), data.get('y', []))
        
        elif chart_type == 'line':
            if 'series' in data:
                # Multiple series
                df = pd.DataFrame({
                    'x': data.get('x', []),
                    'y': data.get('y', []),
                    'series': data.get('series', [])
                })
                
                for series_name, group in df.groupby('series'):
                    ax.plot(group['x'], group['y'], label=series_name)
                
                ax.legend()
            else:
                # Single series
                ax.plot(data.get('x', []), data.get('y', []))
        
        elif chart_type == 'pie':
            ax.pie(data.get('values', []), labels=data.get('labels', []), autopct='%1.1f%%')
        
        elif chart_type == 'scatter':
            ax.scatter(data.get('x', []), data.get('y', []))
        
        elif chart_type == 'heatmap':
            sns.heatmap(data.get('z', []), ax=ax, 
                       xticklabels=data.get('x', []),
                       yticklabels=data.get('y', []),
                       cmap='YlGnBu', annot=True, fmt='.0f')
        
        # Set title and labels
        ax.set_title(title)
        ax.set_xlabel(x_label)
        ax.set_ylabel(y_label)
        
        # Rotate x-axis labels if there are many
        if chart_type in ['bar', 'line'] and len(data.get('x', [])) > 5:
            plt.xticks(rotation=45, ha='right')
        
        plt.tight_layout()
        
        return fig
    
    def generate_pdf(self, output_path: str) -> bool:
        """
        Generate a PDF report
        
        Args:
            output_path: Path to save the PDF
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create PDF document
            doc = SimpleDocTemplate(output_path, pagesize=letter)
            styles = getSampleStyleSheet()
            
            # Create custom styles
            styles.add(ParagraphStyle(
                name='Title',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=12
            ))
            
            styles.add(ParagraphStyle(
                name='Section',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=8
            ))
            
            styles.add(ParagraphStyle(
                name='Subsection',
                parent=styles['Heading3'],
                fontSize=12,
                spaceAfter=6
            ))
            
            # Create content elements
            elements = []
            
            # Add title
            elements.append(Paragraph(self.title, styles['Title']))
            elements.append(Spacer(1, 0.25*inch))
            
            # Add metadata
            metadata_text = f"Generated: {self.metadata['generated_at']}"
            if 'date_range' in self.metadata:
                metadata_text += f" | Date Range: {self.metadata['date_range']}"
            
            elements.append(Paragraph(metadata_text, styles['Italic']))
            elements.append(Spacer(1, 0.25*inch))
            
            # Add sections
            for section in self.sections:
                # Add section title
                elements.append(Paragraph(section['title'], styles['Section']))
                
                # Add section content if available
                if section['content']:
                    elements.append(Paragraph(section['content'], styles['Normal']))
                
                elements.append(Spacer(1, 0.1*inch))
                
                # Add section items
                for item in section['items']:
                    item_type = item['type']
                    item_data = item['data']
                    
                    if item_type == 'chart':
                        # Render chart
                        fig = self._render_chart_matplotlib(item_data)
                        
                        # Save chart to buffer
                        img_buffer = io.BytesIO()
                        fig.savefig(img_buffer, format='png', dpi=300)
                        img_buffer.seek(0)
                        
                        # Add chart to PDF
                        img = Image(img_buffer, width=6*inch, height=4*inch)
                        elements.append(img)
                        
                        # Add chart title if available
                        if 'title' in item_data:
                            elements.append(Paragraph(item_data['title'], styles['Subsection']))
                        
                        # Add chart description if available
                        if 'description' in item_data:
                            elements.append(Paragraph(item_data['description'], styles['Normal']))
                        
                        plt.close(fig)
                    
                    elif item_type == 'table':
                        # Add table title if available
                        if item_data.get('title'):
                            elements.append(Paragraph(item_data['title'], styles['Subsection']))
                        
                        # Add table description if available
                        if item_data.get('description'):
                            elements.append(Paragraph(item_data['description'], styles['Normal']))
                        
                        # Convert DataFrame to list of lists
                        df = item_data['data']
                        table_data = [df.columns.tolist()] + df.values.tolist()
                        
                        # Create table
                        table = Table(table_data, repeatRows=1)
                        
                        # Add table style
                        table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        
                        elements.append(table)
                    
                    elif item_type == 'text':
                        # Add text heading if available
                        if item_data.get('heading'):
                            elements.append(Paragraph(item_data['heading'], styles['Subsection']))
                        
                        # Add text content
                        elements.append(Paragraph(item_data['text'], styles['Normal']))
                    
                    elements.append(Spacer(1, 0.2*inch))
                
                # Add page break after each section (except the last one)
                if section != self.sections[-1]:
                    elements.append(PageBreak())
            
            # Build PDF
            doc.build(elements)
            
            logger.info(f"PDF report generated: {output_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error generating PDF report: {str(e)}")
            return False
    
    def generate_excel(self, output_path: str) -> bool:
        """
        Generate an Excel report
        
        Args:
            output_path: Path to save the Excel file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create Excel workbook
            workbook = xlsxwriter.Workbook(output_path)
            
            # Create summary worksheet
            summary_sheet = workbook.add_worksheet('Summary')
            
            # Add title
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            summary_sheet.merge_range('A1:H1', self.title, title_format)
            
            # Add metadata
            metadata_format = workbook.add_format({
                'italic': True,
                'align': 'center'
            })
            
            metadata_text = f"Generated: {self.metadata['generated_at']}"
            if 'date_range' in self.metadata:
                metadata_text += f" | Date Range: {self.metadata['date_range']}"
            
            summary_sheet.merge_range('A2:H2', metadata_text, metadata_format)
            
            # Add sections to summary
            section_format = workbook.add_format({
                'bold': True,
                'font_size': 12
            })
            
            summary_sheet.write(4, 0, 'Report Sections:', section_format)
            
            for i, section in enumerate(self.sections):
                summary_sheet.write(5 + i, 1, section['title'])
            
            # Create worksheet for each section
            for section_index, section in enumerate(self.sections):
                # Create worksheet
                sheet_name = section['title'][:31]  # Excel limits sheet names to 31 chars
                sheet = workbook.add_worksheet(sheet_name)
                
                # Add section title
                sheet.merge_range('A1:H1', section['title'], title_format)
                
                # Add section content if available
                row = 2
                if section['content']:
                    sheet.merge_range(row, 0, row, 7, section['content'])
                    row += 1
                
                # Add section items
                for item in section['items']:
                    item_type = item['type']
                    item_data = item['data']
                    
                    # Add spacing
                    row += 1
                    
                    if item_type == 'table':
                        # Add table title if available
                        if item_data.get('title'):
                            header_format = workbook.add_format({
                                'bold': True,
                                'font_size': 12
                            })
                            sheet.write(row, 0, item_data['title'], header_format)
                            row += 1
                        
                        # Add table description if available
                        if item_data.get('description'):
                            sheet.merge_range(row, 0, row, 7, item_data['description'])
                            row += 1
                        
                        # Get DataFrame
                        df = item_data['data']
                        
                        # Write headers
                        header_format = workbook.add_format({
                            'bold': True,
                            'bg_color': '#D3D3D3',
                            'border': 1
                        })
                        
                        for col_num, value in enumerate(df.columns.values):
                            sheet.write(row, col_num, value, header_format)
                        
                        # Write data
                        data_format = workbook.add_format({
                            'border': 1
                        })
                        
                        for r_idx, data_row in enumerate(df.values):
                            for c_idx, value in enumerate(data_row):
                                sheet.write(row + 1 + r_idx, c_idx, value, data_format)
                        
                        # Update row
                        row += len(df) + 2
                    
                    elif item_type == 'text':
                        # Add text heading if available
                        if item_data.get('heading'):
                            header_format = workbook.add_format({
                                'bold': True,
                                'font_size': 12
                            })
                            sheet.write(row, 0, item_data['heading'], header_format)
                            row += 1
                        
                        # Add text content
                        sheet.merge_range(row, 0, row, 7, item_data['text'])
                        row += 2
            
            # Create data worksheet with all tables
            if self.tables:
                data_sheet = workbook.add_worksheet('Data Tables')
                
                # Add title
                data_sheet.merge_range('A1:H1', 'Data Tables', title_format)
                
                row = 3
                for table in self.tables:
                    # Add table title if available
                    if table.get('title'):
                        header_format = workbook.add_format({
                            'bold': True,
                            'font_size': 12
                        })
                        data_sheet.write(row, 0, table['title'], header_format)
                        row += 1
                    
                    # Get DataFrame
                    df = table['data']
                    
                    # Write headers
                    header_format = workbook.add_format({
                        'bold': True,
                        'bg_color': '#D3D3D3',
                        'border': 1
                    })
                    
                    for col_num, value in enumerate(df.columns.values):
                        data_sheet.write(row, col_num, value, header_format)
                    
                    # Write data
                    data_format = workbook.add_format({
                        'border': 1
                    })
                    
                    for r_idx, data_row in enumerate(df.values):
                        for c_idx, value in enumerate(data_row):
                            data_sheet.write(row + 1 + r_idx, c_idx, value, data_format)
                    
                    # Update row
                    row += len(df) + 3
            
            # Close workbook
            workbook.close()
            
            logger.info(f"Excel report generated: {output_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error generating Excel report: {str(e)}")
            return False
    
    def get_report_data(self) -> Dict[str, Any]:
        """
        Get report data as a dictionary
        
        Returns:
            Report data dictionary
        """
        return {
            'title': self.title,
            'metadata': self.metadata,
            'sections': self.sections
        }
