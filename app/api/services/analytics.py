# import pandas as pd
# import numpy as np
# from typing import Dict, List, Any, Optional, Tuple
# import logging
# from datetime import datetime, timedelta
# import matplotlib.pyplot as plt
# import seaborn as sns
# from sklearn.linear_model import LinearRegression
# from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
# from sklearn.model_selection import train_test_split
# import json
# import os
# from app.shared.config import MODELS_DIR
# from app.api.models.schemas import ChartData
# from app.shared.utils import clean_nan_for_json
# from app.api.services.analyzers.base import BaseAnalyzer

# # Set up logging
# logging.basicConfig(level=logging.INFO)
# logger = logging.getLogger(__name__)


# # BaseAnalyzer has been moved to app/api/services/analyzers/base.py


# # AgentPerformanceAnalyzer has been moved to app/api/services/analyzers/agent.py


# # CallVolumePredictor has been moved to app/api/services/analyzers/call_volume.py


# # Implement other analyzer classes following the same pattern
# class StaffingOptimizer(BaseAnalyzer):
#     """Optimizer for staffing levels"""

#     def optimize(
#         self,
#         data: Dict[str, pd.DataFrame],
#         target_service_level: float,
#         max_wait_time: int,
#     ) -> Dict[str, Any]:
#         """
#         Optimize staffing levels

#         Args:
#             data: Dictionary of DataFrames
#             target_service_level: Target service level (e.g., 0.8 for 80%)
#             max_wait_time: Maximum acceptable wait time in seconds

#         Returns:
#             Optimization result
#         """
#         result_data = {}
#         charts = []

#         # Combine all data for staffing analysis
#         df_combined = pd.DataFrame()
#         for key, df in data.items():
#             if not df.empty and "Call Date" in df.columns:
#                 df_combined = pd.concat([df_combined, df])

#         if df_combined.empty:
#             return self._format_result(
#                 {"error": "No data available for staffing optimization"}, []
#             )

#         # Ensure Call Date is datetime
#         df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

#         # Add hour and weekday
#         df_combined["Hour"] = df_combined["Call Date"].dt.hour
#         df_combined["Weekday"] = df_combined[
#             "Call Date"
#         ].dt.dayofweek  # 0=Monday, 6=Sunday
#         df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()

#         # Group by hour and weekday to get call volumes
#         hourly_volumes = (
#             df_combined.groupby(["Weekday", "Hour"])
#             .size()
#             .reset_index(name="call_volume")
#         )

#         # Calculate required staff based on Erlang C formula (simplified)
#         # For each hour and weekday, estimate required staff
#         hourly_volumes["avg_handle_time"] = 0
#         if "Length" in df_combined.columns:
#             # Calculate average handle time for each hour and weekday
#             handle_times = (
#                 df_combined.groupby(["Weekday", "Hour"])["Length"].mean().reset_index()
#             )
#             hourly_volumes = hourly_volumes.merge(
#                 handle_times, on=["Weekday", "Hour"], how="left"
#             )
#             hourly_volumes.rename(columns={"Length": "avg_handle_time"}, inplace=True)
#         else:
#             # Use a default handle time of 180 seconds
#             hourly_volumes["avg_handle_time"] = 180

#         # Simple staffing calculation (this is a simplified version of Erlang C)
#         # In a real implementation, you would use a proper Erlang C calculator
#         hourly_volumes["required_staff"] = np.ceil(
#             hourly_volumes["call_volume"]
#             * hourly_volumes["avg_handle_time"]
#             / 3600
#             / (1 - target_service_level)
#         )

#         # Ensure minimum staffing level
#         hourly_volumes["required_staff"] = hourly_volumes["required_staff"].apply(
#             lambda x: max(1, int(x))
#         )

#         # Add weekday names for better readability
#         weekday_names = {
#             0: "Monday",
#             1: "Tuesday",
#             2: "Wednesday",
#             3: "Thursday",
#             4: "Friday",
#             5: "Saturday",
#             6: "Sunday",
#         }
#         hourly_volumes["WeekdayName"] = hourly_volumes["Weekday"].map(weekday_names)

#         # Prepare result data
#         result_data["hourly_staffing"] = hourly_volumes.to_dict(orient="records")

#         # Create a pivot table for visualization
#         staff_pivot = hourly_volumes.pivot(
#             index="Hour", columns="WeekdayName", values="required_staff"
#         )

#         # Fill NaN values with 0
#         staff_pivot = staff_pivot.fillna(0)

#         # Create heatmap chart data
#         charts.append(
#             self._prepare_chart_data(
#                 chart_type="heatmap",
#                 title="Required Staff by Hour and Day",
#                 x_label="Day of Week",
#                 y_label="Hour of Day",
#                 data={
#                     "x": staff_pivot.columns.tolist(),
#                     "y": staff_pivot.index.tolist(),
#                     "z": staff_pivot.values.tolist(),
#                 },
#             )
#         )

#         # Create bar chart for average staff by day
#         daily_staff = (
#             hourly_volumes.groupby("WeekdayName")["required_staff"].mean().reset_index()
#         )
#         daily_staff = daily_staff.sort_values(
#             "WeekdayName",
#             key=lambda x: pd.Categorical(
#                 x,
#                 categories=[
#                     "Monday",
#                     "Tuesday",
#                     "Wednesday",
#                     "Thursday",
#                     "Friday",
#                     "Saturday",
#                     "Sunday",
#                 ],
#             ),
#         )

#         charts.append(
#             self._prepare_chart_data(
#                 chart_type="bar",
#                 title="Average Required Staff by Day",
#                 x_label="Day of Week",
#                 y_label="Average Required Staff",
#                 data={
#                     "x": daily_staff["WeekdayName"].tolist(),
#                     "y": daily_staff["required_staff"].tolist(),
#                 },
#             )
#         )

#         # Create bar chart for average staff by hour
#         hourly_staff = (
#             hourly_volumes.groupby("Hour")["required_staff"].mean().reset_index()
#         )

#         charts.append(
#             self._prepare_chart_data(
#                 chart_type="bar",
#                 title="Average Required Staff by Hour",
#                 x_label="Hour of Day",
#                 y_label="Average Required Staff",
#                 data={
#                     "x": hourly_staff["Hour"].tolist(),
#                     "y": hourly_staff["required_staff"].tolist(),
#                 },
#             )
#         )

#         # Calculate total weekly staff hours
#         total_staff_hours = hourly_volumes["required_staff"].sum()
#         result_data["total_staff_hours"] = int(total_staff_hours)
#         result_data["avg_daily_staff_hours"] = int(total_staff_hours / 7)

#         # Calculate peak staffing requirements
#         peak_hour = hourly_volumes.loc[hourly_volumes["required_staff"].idxmax()]
#         result_data["peak_staffing"] = {
#             "weekday": peak_hour["WeekdayName"],
#             "hour": int(peak_hour["Hour"]),
#             "required_staff": int(peak_hour["required_staff"]),
#             "call_volume": int(peak_hour["call_volume"]),
#         }

#         return self._format_result(result_data, charts)


# class CampaignAnalyzer(BaseAnalyzer):
#     """Analyzer for campaign performance"""

#     def analyze(
#         self, data: Dict[str, pd.DataFrame], metrics: List[str]
#     ) -> Dict[str, Any]:
#         """
#         Analyze campaign performance

#         Args:
#             data: Dictionary of DataFrames
#             metrics: List of metrics to analyze

#         Returns:
#             Analysis result
#         """
#         result_data = {}
#         charts = []

#         # Combine all data for campaign analysis
#         df_combined = pd.DataFrame()
#         for key, df in data.items():
#             if not df.empty and "Campaign" in df.columns:
#                 df_combined = pd.concat([df_combined, df])

#         if df_combined.empty:
#             return self._format_result(
#                 {"error": "No data available for campaign analysis"}, []
#             )

#         # Ensure Call Date is datetime if it exists
#         if "Call Date" in df_combined.columns:
#             df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])
#             df_combined["Hour"] = df_combined["Call Date"].dt.hour
#             df_combined["Weekday"] = df_combined["Call Date"].dt.dayofweek
#             df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()
#             df_combined["Date"] = df_combined["Call Date"].dt.date

#         # Group by campaign
#         campaign_groups = df_combined.groupby("Campaign")

#         # Calculate metrics by campaign
#         campaign_metrics = []

#         for campaign, group in campaign_groups:
#             campaign_data = {"Campaign": campaign}

#             # Call volume
#             if "call_volume" in metrics:
#                 campaign_data["call_volume"] = len(group)

#             # Call length
#             if "call_length" in metrics and "Length" in group.columns:
#                 campaign_data["avg_call_length"] = group["Length"].mean()

#             # Success rate (approximated by Status)
#             if "success_rate" in metrics and "Status" in group.columns:
#                 # Assuming certain statuses indicate success
#                 success_statuses = ["ANSWERED", "COMPLETED", "RESOLVED"]
#                 success_count = group[group["Status"].isin(success_statuses)].shape[0]
#                 campaign_data["success_rate"] = (
#                     success_count / len(group) if len(group) > 0 else 0
#                 )
#                 campaign_data["success_count"] = success_count
#                 campaign_data["total_calls"] = len(group)

#             # Language detection
#             if "Language" in group.columns:
#                 # Count by language
#                 language_counts = group["Language"].value_counts().to_dict()
#                 campaign_data["languages"] = language_counts

#                 # Primary language
#                 primary_language = (
#                     group["Language"].value_counts().idxmax()
#                     if not group.empty
#                     else "Unknown"
#                 )
#                 campaign_data["primary_language"] = primary_language

#             # Add to results
#             campaign_metrics.append(campaign_data)

#         # Sort by call volume
#         campaign_metrics = sorted(
#             campaign_metrics, key=lambda x: x.get("call_volume", 0), reverse=True
#         )

#         # Prepare result data
#         result_data["campaign_metrics"] = campaign_metrics

#         # Create charts

#         # Call volume by campaign
#         if "call_volume" in metrics:
#             campaign_volumes = pd.DataFrame(campaign_metrics)

#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Call Volume by Campaign",
#                     x_label="Campaign",
#                     y_label="Number of Calls",
#                     data={
#                         "x": campaign_volumes["Campaign"].tolist(),
#                         "y": campaign_volumes["call_volume"].tolist(),
#                     },
#                 )
#             )

#         # Average call length by campaign
#         if "call_length" in metrics and "avg_call_length" in campaign_volumes.columns:
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Average Call Length by Campaign",
#                     x_label="Campaign",
#                     y_label="Average Length (seconds)",
#                     data={
#                         "x": campaign_volumes["Campaign"].tolist(),
#                         "y": campaign_volumes["avg_call_length"].tolist(),
#                     },
#                 )
#             )

#         # Success rate by campaign
#         if "success_rate" in metrics and "success_rate" in campaign_volumes.columns:
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Success Rate by Campaign",
#                     x_label="Campaign",
#                     y_label="Success Rate",
#                     data={
#                         "x": campaign_volumes["Campaign"].tolist(),
#                         "y": campaign_volumes["success_rate"].tolist(),
#                     },
#                 )
#             )

#         # Call volume by campaign and day of week
#         if "call_volume" in metrics and "Weekday" in df_combined.columns:
#             campaign_weekday = (
#                 df_combined.groupby(["Campaign", "WeekdayName"])
#                 .size()
#                 .reset_index(name="call_count")
#             )

#             # Pivot for better visualization
#             weekday_pivot = campaign_weekday.pivot(
#                 index="Campaign", columns="WeekdayName", values="call_count"
#             )

#             # Fill NaN values with 0
#             weekday_pivot = weekday_pivot.fillna(0)

#             # Reorder columns for days of week
#             weekday_order = [
#                 "Monday",
#                 "Tuesday",
#                 "Wednesday",
#                 "Thursday",
#                 "Friday",
#                 "Saturday",
#                 "Sunday",
#             ]
#             weekday_pivot = weekday_pivot.reindex(columns=weekday_order, fill_value=0)

#             # Create heatmap chart
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="heatmap",
#                     title="Call Volume by Campaign and Day of Week",
#                     x_label="Day of Week",
#                     y_label="Campaign",
#                     data={
#                         "x": weekday_pivot.columns.tolist(),
#                         "y": weekday_pivot.index.tolist(),
#                         "z": weekday_pivot.values.tolist(),
#                     },
#                 )
#             )

#         # Call volume by campaign and hour of day
#         if "call_volume" in metrics and "Hour" in df_combined.columns:
#             campaign_hour = (
#                 df_combined.groupby(["Campaign", "Hour"])
#                 .size()
#                 .reset_index(name="call_count")
#             )

#             # Pivot for better visualization
#             hour_pivot = campaign_hour.pivot(
#                 index="Campaign", columns="Hour", values="call_count"
#             )

#             # Fill NaN values with 0
#             hour_pivot = hour_pivot.fillna(0)

#             # Create heatmap chart
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="heatmap",
#                     title="Call Volume by Campaign and Hour of Day",
#                     x_label="Hour of Day",
#                     y_label="Campaign",
#                     data={
#                         "x": hour_pivot.columns.tolist(),
#                         "y": hour_pivot.index.tolist(),
#                         "z": hour_pivot.values.tolist(),
#                     },
#                 )
#             )

#         return self._format_result(result_data, charts)


# class LanguageAnalyzer(BaseAnalyzer):
#     """Analyzer for language-specific performance"""

#     def analyze(
#         self, data: Dict[str, pd.DataFrame], languages: List[str]
#     ) -> Dict[str, Any]:
#         """
#         Analyze performance across languages

#         Args:
#             data: Dictionary of DataFrames
#             languages: List of languages to analyze

#         Returns:
#             Analysis result
#         """
#         result_data = {}
#         charts = []

#         # Combine all data for language analysis
#         df_combined = pd.DataFrame()
#         for key, df in data.items():
#             if not df.empty:
#                 # If Language column doesn't exist, try to detect from Campaign
#                 if "Language" not in df.columns and "Campaign" in df.columns:
#                     df["Language"] = df["Campaign"].apply(
#                         lambda x: "Arabic" if "Arabic" in str(x) else "English"
#                     )

#                 if "Language" in df.columns:
#                     df_combined = pd.concat([df_combined, df])

#         if df_combined.empty:
#             return self._format_result(
#                 {"error": "No data available for language analysis"}, []
#             )

#         # Filter by specified languages if provided
#         if languages and len(languages) > 0 and "All" not in languages:
#             df_combined = df_combined[df_combined["Language"].isin(languages)]

#             if df_combined.empty:
#                 return self._format_result(
#                     {
#                         "error": f"No data available for specified languages: {', '.join(languages)}"
#                     },
#                     [],
#                 )

#         # Ensure Call Date is datetime if it exists
#         if "Call Date" in df_combined.columns:
#             df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])
#             df_combined["Hour"] = df_combined["Call Date"].dt.hour
#             df_combined["Weekday"] = df_combined["Call Date"].dt.dayofweek
#             df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()
#             df_combined["Date"] = df_combined["Call Date"].dt.date

#         # Group by language
#         language_groups = df_combined.groupby("Language")

#         # Calculate metrics by language
#         language_metrics = []

#         for language, group in language_groups:
#             language_data = {"Language": language}

#             # Call volume
#             language_data["call_volume"] = len(group)

#             # Call length
#             if "Length" in group.columns:
#                 language_data["avg_call_length"] = group["Length"].mean()

#             # Resolution rate (approximated by Status)
#             if "Status" in group.columns:
#                 # Assuming certain statuses indicate resolution
#                 resolved_statuses = ["ANSWERED", "COMPLETED", "RESOLVED"]
#                 resolved_calls = group[group["Status"].isin(resolved_statuses)].shape[0]
#                 language_data["resolution_rate"] = (
#                     resolved_calls / len(group) if len(group) > 0 else 0
#                 )
#                 language_data["resolved_calls"] = resolved_calls
#                 language_data["total_calls"] = len(group)

#             # Top campaigns
#             if "Campaign" in group.columns:
#                 campaign_counts = group["Campaign"].value_counts().head(5).to_dict()
#                 language_data["top_campaigns"] = campaign_counts

#             # Top agents
#             if "User" in group.columns:
#                 agent_counts = group["User"].value_counts().head(5).to_dict()
#                 language_data["top_agents"] = agent_counts

#             # Add to results
#             language_metrics.append(language_data)

#         # Prepare result data
#         result_data["language_metrics"] = language_metrics

#         # Create charts

#         # Call volume by language
#         language_volumes = pd.DataFrame(language_metrics)

#         charts.append(
#             self._prepare_chart_data(
#                 chart_type="pie",
#                 title="Call Volume by Language",
#                 x_label="",
#                 y_label="",
#                 data={
#                     "labels": language_volumes["Language"].tolist(),
#                     "values": language_volumes["call_volume"].tolist(),
#                 },
#             )
#         )

#         # Average call length by language
#         if "avg_call_length" in language_volumes.columns:
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Average Call Length by Language",
#                     x_label="Language",
#                     y_label="Average Length (seconds)",
#                     data={
#                         "x": language_volumes["Language"].tolist(),
#                         "y": language_volumes["avg_call_length"].tolist(),
#                     },
#                 )
#             )

#         # Resolution rate by language
#         if "resolution_rate" in language_volumes.columns:
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Resolution Rate by Language",
#                     x_label="Language",
#                     y_label="Resolution Rate",
#                     data={
#                         "x": language_volumes["Language"].tolist(),
#                         "y": language_volumes["resolution_rate"].tolist(),
#                     },
#                 )
#             )

#         # Call volume by language and hour of day
#         if "Hour" in df_combined.columns:
#             language_hour = (
#                 df_combined.groupby(["Language", "Hour"])
#                 .size()
#                 .reset_index(name="call_count")
#             )

#             # Create line chart for hourly distribution by language
#             hour_data = {}
#             for lang in language_hour["Language"].unique():
#                 lang_data = language_hour[language_hour["Language"] == lang]
#                 hour_data[lang] = {
#                     "x": lang_data["Hour"].tolist(),
#                     "y": lang_data["call_count"].tolist(),
#                 }

#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="line",
#                     title="Call Volume by Hour and Language",
#                     x_label="Hour of Day",
#                     y_label="Number of Calls",
#                     data={"series": list(hour_data.keys()), "data": hour_data},
#                 )
#             )

#         # Call volume by language and day of week
#         if "WeekdayName" in df_combined.columns:
#             language_weekday = (
#                 df_combined.groupby(["Language", "WeekdayName"])
#                 .size()
#                 .reset_index(name="call_count")
#             )

#             # Pivot for better visualization
#             weekday_pivot = language_weekday.pivot(
#                 index="Language", columns="WeekdayName", values="call_count"
#             )

#             # Fill NaN values with 0
#             weekday_pivot = weekday_pivot.fillna(0)

#             # Reorder columns for days of week
#             weekday_order = [
#                 "Monday",
#                 "Tuesday",
#                 "Wednesday",
#                 "Thursday",
#                 "Friday",
#                 "Saturday",
#                 "Sunday",
#             ]
#             weekday_pivot = weekday_pivot.reindex(columns=weekday_order, fill_value=0)

#             # Create heatmap chart
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="heatmap",
#                     title="Call Volume by Language and Day of Week",
#                     x_label="Day of Week",
#                     y_label="Language",
#                     data={
#                         "x": weekday_pivot.columns.tolist(),
#                         "y": weekday_pivot.index.tolist(),
#                         "z": weekday_pivot.values.tolist(),
#                     },
#                 )
#             )

#         return self._format_result(result_data, charts)


# # Define remaining analyzer classes
# class MissedCallAnalyzer(BaseAnalyzer):
#     """Analyzer for missed calls"""

#     def analyze(
#         self, data: Dict[str, pd.DataFrame], include_callbacks: bool = True
#     ) -> Dict[str, Any]:
#         """
#         Analyze missed calls

#         Args:
#             data: Dictionary of DataFrames
#             include_callbacks: Whether to include callback analysis

#         Returns:
#             Analysis result
#         """
#         result_data = {}
#         charts = []

#         # Get missed call data
#         if "missed" not in data or data["missed"].empty:
#             return self._format_result({"error": "No missed call data available"}, [])

#         df_missed = data["missed"].copy()

#         # Ensure Call Date is datetime
#         if "Call Date" in df_missed.columns:
#             df_missed["Call Date"] = pd.to_datetime(df_missed["Call Date"])
#             df_missed["Hour"] = df_missed["Call Date"].dt.hour
#             df_missed["Weekday"] = df_missed["Call Date"].dt.dayofweek
#             df_missed["WeekdayName"] = df_missed["Call Date"].dt.day_name()
#             df_missed["Date"] = df_missed["Call Date"].dt.date

#         # Basic missed call metrics
#         total_missed = len(df_missed)
#         result_data["total_missed_calls"] = total_missed

#         # Missed calls by hour
#         if "Hour" in df_missed.columns:
#             hourly_missed = (
#                 df_missed.groupby("Hour").size().reset_index(name="missed_count")
#             )
#             result_data["hourly_missed"] = hourly_missed.to_dict(orient="records")

#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Missed Calls by Hour of Day",
#                     x_label="Hour of Day",
#                     y_label="Number of Missed Calls",
#                     data={
#                         "x": hourly_missed["Hour"].tolist(),
#                         "y": hourly_missed["missed_count"].tolist(),
#                     },
#                 )
#             )

#         # Missed calls by weekday
#         if "WeekdayName" in df_missed.columns:
#             weekday_missed = (
#                 df_missed.groupby("WeekdayName").size().reset_index(name="missed_count")
#             )

#             # Sort by day of week
#             weekday_order = [
#                 "Monday",
#                 "Tuesday",
#                 "Wednesday",
#                 "Thursday",
#                 "Friday",
#                 "Saturday",
#                 "Sunday",
#             ]
#             weekday_missed["sort_order"] = weekday_missed["WeekdayName"].apply(
#                 lambda x: weekday_order.index(x)
#             )
#             weekday_missed = weekday_missed.sort_values("sort_order")
#             weekday_missed = weekday_missed.drop("sort_order", axis=1)

#             result_data["weekday_missed"] = weekday_missed.to_dict(orient="records")

#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Missed Calls by Day of Week",
#                     x_label="Day of Week",
#                     y_label="Number of Missed Calls",
#                     data={
#                         "x": weekday_missed["WeekdayName"].tolist(),
#                         "y": weekday_missed["missed_count"].tolist(),
#                     },
#                 )
#             )

#         # Missed calls by campaign
#         if "Campaign" in df_missed.columns:
#             campaign_missed = (
#                 df_missed.groupby("Campaign").size().reset_index(name="missed_count")
#             )
#             campaign_missed = campaign_missed.sort_values(
#                 "missed_count", ascending=False
#             )
#             result_data["campaign_missed"] = campaign_missed.to_dict(orient="records")

#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Missed Calls by Campaign",
#                     x_label="Campaign",
#                     y_label="Number of Missed Calls",
#                     data={
#                         "x": campaign_missed["Campaign"].tolist(),
#                         "y": campaign_missed["missed_count"].tolist(),
#                     },
#                 )
#             )

#         # Callback analysis
#         if include_callbacks and "First Callback Time" in df_missed.columns:
#             # Calculate callback rate
#             df_missed["Has Callback"] = df_missed["First Callback Time"].notna()
#             callback_count = df_missed["Has Callback"].sum()
#             callback_rate = callback_count / total_missed if total_missed > 0 else 0

#             result_data["callback_metrics"] = {
#                 "callback_count": int(callback_count),
#                 "callback_rate": callback_rate,
#             }

#             # Calculate time to first callback
#             df_with_callback = df_missed[df_missed["Has Callback"]].copy()
#             if not df_with_callback.empty:
#                 df_with_callback["First Callback Time"] = pd.to_datetime(
#                     df_with_callback["First Callback Time"]
#                 )
#                 df_with_callback["Time to Callback"] = (
#                     df_with_callback["First Callback Time"]
#                     - df_with_callback["Call Date"]
#                 ).dt.total_seconds() / 60  # in minutes

#                 avg_callback_time = df_with_callback["Time to Callback"].mean()
#                 median_callback_time = df_with_callback["Time to Callback"].median()

#                 result_data["callback_metrics"][
#                     "avg_callback_time_minutes"
#                 ] = avg_callback_time
#                 result_data["callback_metrics"][
#                     "median_callback_time_minutes"
#                 ] = median_callback_time

#                 # Distribution of callback times
#                 callback_bins = [0, 15, 30, 60, 120, 240, 480, 1440]  # in minutes
#                 callback_labels = [
#                     "0-15m",
#                     "15-30m",
#                     "30-60m",
#                     "1-2h",
#                     "2-4h",
#                     "4-8h",
#                     "8-24h",
#                 ]

#                 df_with_callback["Callback Time Bin"] = pd.cut(
#                     df_with_callback["Time to Callback"],
#                     bins=callback_bins,
#                     labels=callback_labels,
#                     include_lowest=True,
#                 )

#                 callback_distribution = (
#                     df_with_callback["Callback Time Bin"].value_counts().reset_index()
#                 )
#                 callback_distribution.columns = ["time_range", "count"]
#                 callback_distribution = callback_distribution.sort_values("time_range")

#                 result_data["callback_distribution"] = callback_distribution.to_dict(
#                     orient="records"
#                 )

#                 charts.append(
#                     self._prepare_chart_data(
#                         chart_type="bar",
#                         title="Distribution of Time to First Callback",
#                         x_label="Time Range",
#                         y_label="Number of Callbacks",
#                         data={
#                             "x": callback_distribution["time_range"].tolist(),
#                             "y": callback_distribution["count"].tolist(),
#                         },
#                     )
#                 )

#             # Connected callback rate
#             if "Connected Callback Time" in df_missed.columns:
#                 df_missed["Has Connected Callback"] = df_missed[
#                     "Connected Callback Time"
#                 ].notna()
#                 connected_count = df_missed["Has Connected Callback"].sum()
#                 connected_rate = (
#                     connected_count / callback_count if callback_count > 0 else 0
#                 )

#                 result_data["callback_metrics"]["connected_count"] = int(
#                     connected_count
#                 )
#                 result_data["callback_metrics"]["connected_rate"] = connected_rate

#                 # Create pie chart for callback outcomes
#                 charts.append(
#                     self._prepare_chart_data(
#                         chart_type="pie",
#                         title="Callback Outcomes",
#                         x_label="",
#                         y_label="",
#                         data={
#                             "labels": ["Connected", "Not Connected", "No Callback"],
#                             "values": [
#                                 int(connected_count),
#                                 int(callback_count - connected_count),
#                                 int(total_missed - callback_count),
#                             ],
#                         },
#                     )
#                 )

#         # SMS analysis
#         if "SMS Status" in df_missed.columns:
#             sms_status = df_missed["SMS Status"].value_counts().reset_index()
#             sms_status.columns = ["status", "count"]
#             result_data["sms_status"] = sms_status.to_dict(orient="records")

#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="pie",
#                     title="SMS Status for Missed Calls",
#                     x_label="",
#                     y_label="",
#                     data={
#                         "labels": sms_status["status"].tolist(),
#                         "values": sms_status["count"].tolist(),
#                     },
#                 )
#             )

#         return self._format_result(result_data, charts)


# class CustomerJourneyAnalyzer(BaseAnalyzer):
#     """Analyzer for customer journeys"""

#     def analyze(
#         self,
#         data: Dict[str, pd.DataFrame],
#         phone_numbers: Optional[List[str]] = None,
#         max_interactions: int = 10,
#     ) -> Dict[str, Any]:
#         """
#         Analyze customer journeys across multiple interactions

#         Args:
#             data: Dictionary of DataFrames
#             phone_numbers: Optional list of phone numbers to analyze
#             max_interactions: Maximum number of interactions to include

#         Returns:
#             Analysis result
#         """
#         result_data = {}
#         charts = []

#         # Combine all data for customer journey analysis
#         df_combined = pd.DataFrame()
#         for key, df in data.items():
#             if not df.empty and "Phone" in df.columns:
#                 df_combined = pd.concat([df_combined, df])

#         if df_combined.empty:
#             return self._format_result(
#                 {"error": "No data available for customer journey analysis"}, []
#             )

#         # Ensure Call Date is datetime
#         if "Call Date" in df_combined.columns:
#             df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

#         # Filter by specific phone numbers if provided
#         if phone_numbers and len(phone_numbers) > 0:
#             df_combined = df_combined[df_combined["Phone"].isin(phone_numbers)]

#             if df_combined.empty:
#                 return self._format_result(
#                     {"error": "No data available for the specified phone numbers"}, []
#                 )

#         # Group by phone number
#         phone_groups = df_combined.groupby("Phone")

#         # Calculate interaction counts
#         interaction_counts = phone_groups.size().reset_index(name="interaction_count")
#         interaction_counts = interaction_counts.sort_values(
#             "interaction_count", ascending=False
#         )

#         # Calculate distribution of interaction counts
#         interaction_distribution = (
#             interaction_counts["interaction_count"].value_counts().reset_index()
#         )
#         interaction_distribution.columns = ["interactions", "customer_count"]
#         interaction_distribution = interaction_distribution.sort_values("interactions")

#         result_data["interaction_distribution"] = interaction_distribution.to_dict(
#             orient="records"
#         )

#         # Create chart for interaction distribution
#         charts.append(
#             self._prepare_chart_data(
#                 chart_type="bar",
#                 title="Distribution of Customer Interactions",
#                 x_label="Number of Interactions",
#                 y_label="Number of Customers",
#                 data={
#                     "x": interaction_distribution["interactions"].tolist(),
#                     "y": interaction_distribution["customer_count"].tolist(),
#                 },
#             )
#         )

#         # Analyze customer journeys for top customers or specified phone numbers
#         if phone_numbers and len(phone_numbers) > 0:
#             phones_to_analyze = phone_numbers
#         else:
#             # Take top customers by interaction count
#             phones_to_analyze = interaction_counts.head(10)["Phone"].tolist()

#         # Limit to max_interactions
#         phones_to_analyze = phones_to_analyze[:max_interactions]

#         # Analyze journeys for selected customers
#         customer_journeys = []

#         for phone in phones_to_analyze:
#             customer_data = df_combined[df_combined["Phone"] == phone].copy()

#             # Sort by call date
#             if "Call Date" in customer_data.columns:
#                 customer_data = customer_data.sort_values("Call Date")

#             # Extract journey information
#             journey = {
#                 "phone": phone,
#                 "total_interactions": len(customer_data),
#                 "first_interaction": (
#                     customer_data["Call Date"].min().strftime("%Y-%m-%d %H:%M:%S")
#                     if "Call Date" in customer_data.columns
#                     else None
#                 ),
#                 "last_interaction": (
#                     customer_data["Call Date"].max().strftime("%Y-%m-%d %H:%M:%S")
#                     if "Call Date" in customer_data.columns
#                     else None
#                 ),
#                 "interactions": [],
#             }

#             # Add each interaction
#             for _, row in customer_data.iterrows():
#                 interaction = {
#                     "call_type": row.get("Call Type", "Unknown"),
#                     "date": (
#                         row["Call Date"].strftime("%Y-%m-%d %H:%M:%S")
#                         if "Call Date" in row
#                         else None
#                     ),
#                     "campaign": row.get("Campaign", "Unknown"),
#                     "status": row.get("Status", "Unknown"),
#                     "user": row.get("User", "Unknown"),
#                     "length": (
#                         int(row["Length"])
#                         if "Length" in row and pd.notna(row["Length"])
#                         else None
#                     ),
#                 }
#                 journey["interactions"].append(interaction)

#             customer_journeys.append(journey)

#         result_data["customer_journeys"] = customer_journeys

#         # Calculate time between interactions
#         if len(customer_journeys) > 0 and "Call Date" in df_combined.columns:
#             time_between_interactions = []

#             for phone in phones_to_analyze:
#                 customer_data = df_combined[df_combined["Phone"] == phone].copy()

#                 if len(customer_data) > 1:
#                     # Sort by call date
#                     customer_data = customer_data.sort_values("Call Date")

#                     # Calculate time differences
#                     customer_data["Next Call Date"] = customer_data["Call Date"].shift(
#                         -1
#                     )
#                     customer_data["Time to Next Call"] = (
#                         customer_data["Next Call Date"] - customer_data["Call Date"]
#                     ).dt.total_seconds() / 3600  # in hours

#                     # Add to results
#                     for _, row in customer_data.dropna(
#                         subset=["Time to Next Call"]
#                     ).iterrows():
#                         time_between_interactions.append(
#                             {
#                                 "phone": phone,
#                                 "from_date": row["Call Date"].strftime(
#                                     "%Y-%m-%d %H:%M:%S"
#                                 ),
#                                 "to_date": row["Next Call Date"].strftime(
#                                     "%Y-%m-%d %H:%M:%S"
#                                 ),
#                                 "hours_between": row["Time to Next Call"],
#                             }
#                         )

#             if time_between_interactions:
#                 result_data["time_between_interactions"] = time_between_interactions

#                 # Calculate average time between interactions
#                 hours_between = [
#                     item["hours_between"] for item in time_between_interactions
#                 ]
#                 avg_hours_between = (
#                     sum(hours_between) / len(hours_between) if hours_between else 0
#                 )

#                 result_data["avg_hours_between_interactions"] = avg_hours_between

#                 # Create histogram of time between interactions
#                 time_bins = [0, 1, 6, 24, 72, 168, 720]  # in hours
#                 time_labels = ["<1h", "1-6h", "6-24h", "1-3d", "3-7d", ">7d"]

#                 time_hist = (
#                     pd.cut(
#                         pd.Series(hours_between),
#                         bins=time_bins,
#                         labels=time_labels,
#                         include_lowest=True,
#                     )
#                     .value_counts()
#                     .reset_index()
#                 )

#                 time_hist.columns = ["time_range", "count"]
#                 time_hist = time_hist.sort_values("time_range")

#                 charts.append(
#                     self._prepare_chart_data(
#                         chart_type="bar",
#                         title="Time Between Customer Interactions",
#                         x_label="Time Range",
#                         y_label="Number of Interactions",
#                         data={
#                             "x": time_hist["time_range"].tolist(),
#                             "y": time_hist["count"].tolist(),
#                         },
#                     )
#                 )

#         # Analyze common paths
#         if "Campaign" in df_combined.columns:
#             # Get sequences of campaigns for each customer
#             campaign_sequences = []

#             for phone, group in phone_groups:
#                 if len(group) > 1:
#                     # Sort by call date
#                     if "Call Date" in group.columns:
#                         group = group.sort_values("Call Date")

#                     # Get campaign sequence
#                     sequence = group["Campaign"].tolist()

#                     # Only include sequences with at least 2 campaigns
#                     if len(sequence) >= 2:
#                         campaign_sequences.append("->".join(sequence))

#             # Count common sequences
#             if campaign_sequences:
#                 sequence_counts = (
#                     pd.Series(campaign_sequences).value_counts().head(10).reset_index()
#                 )
#                 sequence_counts.columns = ["sequence", "count"]

#                 result_data["common_campaign_sequences"] = sequence_counts.to_dict(
#                     orient="records"
#                 )

#                 # Create chart for common sequences
#                 charts.append(
#                     self._prepare_chart_data(
#                         chart_type="bar",
#                         title="Common Campaign Sequences",
#                         x_label="Campaign Sequence",
#                         y_label="Frequency",
#                         data={
#                             "x": sequence_counts["sequence"].tolist(),
#                             "y": sequence_counts["count"].tolist(),
#                         },
#                     )
#                 )

#         return self._format_result(result_data, charts)


# class FCRAnalyzer(BaseAnalyzer):
#     """Analyzer for First Call Resolution"""

#     def analyze(
#         self, data: Dict[str, pd.DataFrame], resolution_window: int = 7
#     ) -> Dict[str, Any]:
#         """
#         Analyze First Call Resolution (FCR) rates

#         Args:
#             data: Dictionary of DataFrames
#             resolution_window: Number of days to consider for resolution window

#         Returns:
#             Analysis result
#         """
#         result_data = {}
#         charts = []

#         # Combine all data for FCR analysis
#         df_combined = pd.DataFrame()
#         for _, df in data.items():
#             if not df.empty and "Phone" in df.columns and "Call Date" in df.columns:
#                 df_combined = pd.concat([df_combined, df])

#         if df_combined.empty:
#             return self._format_result(
#                 {"error": "No data available for FCR analysis"}, []
#             )

#         # Ensure Call Date is datetime
#         df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

#         # Add date components
#         df_combined["Date"] = df_combined["Call Date"].dt.date
#         df_combined["Hour"] = df_combined["Call Date"].dt.hour
#         df_combined["Weekday"] = df_combined["Call Date"].dt.dayofweek
#         df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()
#         df_combined["Month"] = df_combined["Call Date"].dt.month

#         # Group by phone number and sort by date
#         phone_groups = df_combined.groupby("Phone")

#         # Calculate FCR metrics
#         fcr_data = []
#         repeat_calls = []

#         for phone, group in phone_groups:
#             # Sort by call date
#             group = group.sort_values("Call Date")

#             if len(group) > 1:
#                 # Calculate time differences between calls
#                 group["Next Call Date"] = group["Call Date"].shift(-1)
#                 group["Days to Next Call"] = (
#                     group["Next Call Date"] - group["Call Date"]
#                 ).dt.total_seconds() / (24 * 3600)

#                 # Identify calls that required follow-up within the resolution window
#                 group["Required Followup"] = (
#                     group["Days to Next Call"] <= resolution_window
#                 ) & (group["Days to Next Call"] > 0)

#                 # Add to FCR data
#                 for i, row in group.iterrows():
#                     if pd.notna(row.get("Days to Next Call")):
#                         call_data = {
#                             "phone": phone,
#                             "call_date": row["Call Date"].strftime("%Y-%m-%d %H:%M:%S"),
#                             "campaign": row.get("Campaign", "Unknown"),
#                             "status": row.get("Status", "Unknown"),
#                             "user": row.get("User", "Unknown"),
#                             "required_followup": bool(row["Required Followup"]),
#                             "days_to_next_call": (
#                                 row["Days to Next Call"]
#                                 if pd.notna(row["Days to Next Call"])
#                                 else None
#                             ),
#                         }
#                         fcr_data.append(call_data)

#                         # Add to repeat calls if it required follow-up
#                         if bool(row["Required Followup"]):
#                             repeat_calls.append(
#                                 {
#                                     "phone": phone,
#                                     "first_call_date": row["Call Date"].strftime(
#                                         "%Y-%m-%d %H:%M:%S"
#                                     ),
#                                     "next_call_date": (
#                                         row["Next Call Date"].strftime(
#                                             "%Y-%m-%d %H:%M:%S"
#                                         )
#                                         if pd.notna(row["Next Call Date"])
#                                         else None
#                                     ),
#                                     "days_between": row["Days to Next Call"],
#                                     "first_call_campaign": row.get(
#                                         "Campaign", "Unknown"
#                                     ),
#                                     "first_call_user": row.get("User", "Unknown"),
#                                 }
#                             )

#         # Calculate FCR rate
#         if fcr_data:
#             total_calls = len(fcr_data)
#             resolved_first_call = sum(
#                 1 for call in fcr_data if not call["required_followup"]
#             )
#             fcr_rate = resolved_first_call / total_calls if total_calls > 0 else 0

#             result_data["fcr_metrics"] = {
#                 "total_calls": total_calls,
#                 "resolved_first_call": resolved_first_call,
#                 "fcr_rate": fcr_rate,
#                 "resolution_window_days": resolution_window,
#             }

#             # Create chart for FCR rate
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="pie",
#                     title="First Call Resolution Rate",
#                     x_label="",
#                     y_label="",
#                     data={
#                         "labels": ["Resolved on First Call", "Required Follow-up"],
#                         "values": [
#                             resolved_first_call,
#                             total_calls - resolved_first_call,
#                         ],
#                     },
#                 )
#             )

#         # FCR by campaign
#         if fcr_data and "Campaign" in df_combined.columns:
#             campaign_fcr = {}

#             for call in fcr_data:
#                 campaign = call["campaign"]
#                 if campaign not in campaign_fcr:
#                     campaign_fcr[campaign] = {"total": 0, "resolved": 0}

#                 campaign_fcr[campaign]["total"] += 1
#                 if not call["required_followup"]:
#                     campaign_fcr[campaign]["resolved"] += 1

#             # Calculate FCR rate by campaign
#             campaign_fcr_rates = []
#             for campaign, counts in campaign_fcr.items():
#                 if counts["total"] >= 5:  # Only include campaigns with sufficient data
#                     campaign_fcr_rates.append(
#                         {
#                             "campaign": campaign,
#                             "total_calls": counts["total"],
#                             "resolved_first_call": counts["resolved"],
#                             "fcr_rate": (
#                                 counts["resolved"] / counts["total"]
#                                 if counts["total"] > 0
#                                 else 0
#                             ),
#                         }
#                     )

#             # Sort by FCR rate
#             campaign_fcr_rates = sorted(
#                 campaign_fcr_rates, key=lambda x: x["fcr_rate"], reverse=True
#             )
#             result_data["campaign_fcr"] = campaign_fcr_rates

#             # Create chart for FCR by campaign
#             if campaign_fcr_rates:
#                 charts.append(
#                     self._prepare_chart_data(
#                         chart_type="bar",
#                         title="FCR Rate by Campaign",
#                         x_label="Campaign",
#                         y_label="FCR Rate",
#                         data={
#                             "x": [item["campaign"] for item in campaign_fcr_rates],
#                             "y": [item["fcr_rate"] for item in campaign_fcr_rates],
#                         },
#                     )
#                 )

#         # FCR by agent
#         if fcr_data and "User" in df_combined.columns:
#             agent_fcr = {}

#             for call in fcr_data:
#                 agent = call["user"]
#                 if agent not in agent_fcr:
#                     agent_fcr[agent] = {"total": 0, "resolved": 0}

#                 agent_fcr[agent]["total"] += 1
#                 if not call["required_followup"]:
#                     agent_fcr[agent]["resolved"] += 1

#             # Calculate FCR rate by agent
#             agent_fcr_rates = []
#             for agent, counts in agent_fcr.items():
#                 if counts["total"] >= 5:  # Only include agents with sufficient data
#                     agent_fcr_rates.append(
#                         {
#                             "agent": agent,
#                             "total_calls": counts["total"],
#                             "resolved_first_call": counts["resolved"],
#                             "fcr_rate": (
#                                 counts["resolved"] / counts["total"]
#                                 if counts["total"] > 0
#                                 else 0
#                             ),
#                         }
#                     )

#             # Sort by FCR rate
#             agent_fcr_rates = sorted(
#                 agent_fcr_rates, key=lambda x: x["fcr_rate"], reverse=True
#             )
#             result_data["agent_fcr"] = agent_fcr_rates

#             # Create chart for FCR by agent
#             if agent_fcr_rates:
#                 charts.append(
#                     self._prepare_chart_data(
#                         chart_type="bar",
#                         title="FCR Rate by Agent",
#                         x_label="Agent",
#                         y_label="FCR Rate",
#                         data={
#                             "x": [item["agent"] for item in agent_fcr_rates],
#                             "y": [item["fcr_rate"] for item in agent_fcr_rates],
#                         },
#                     )
#                 )

#         # Time to follow-up distribution
#         if repeat_calls:
#             days_to_followup = [
#                 call["days_between"]
#                 for call in repeat_calls
#                 if call["days_between"] is not None
#             ]

#             if days_to_followup:
#                 # Calculate statistics
#                 avg_days = sum(days_to_followup) / len(days_to_followup)
#                 result_data["followup_metrics"] = {
#                     "total_followups": len(days_to_followup),
#                     "avg_days_to_followup": avg_days,
#                 }

#                 # Create histogram of follow-up times
#                 day_bins = [0, 1, 2, 3, 5, 7, 14, 30]
#                 day_labels = [
#                     "Same day",
#                     "1 day",
#                     "2 days",
#                     "3-4 days",
#                     "5-6 days",
#                     "1-2 weeks",
#                     "2-4 weeks",
#                 ]

#                 day_hist = (
#                     pd.cut(
#                         pd.Series(days_to_followup),
#                         bins=day_bins,
#                         labels=day_labels,
#                         include_lowest=True,
#                     )
#                     .value_counts()
#                     .reset_index()
#                 )

#                 day_hist.columns = ["time_range", "count"]
#                 day_hist = day_hist.sort_values("time_range")

#                 result_data["followup_distribution"] = day_hist.to_dict(
#                     orient="records"
#                 )

#                 charts.append(
#                     self._prepare_chart_data(
#                         chart_type="bar",
#                         title="Time to Follow-up Distribution",
#                         x_label="Time Range",
#                         y_label="Number of Follow-ups",
#                         data={
#                             "x": day_hist["time_range"].tolist(),
#                             "y": day_hist["count"].tolist(),
#                         },
#                     )
#                 )

#         return self._format_result(result_data, charts)


# class SentimentAnalyzer(BaseAnalyzer):
#     """Analyzer for sentiment analysis"""

#     def analyze(
#         self, data: Dict[str, pd.DataFrame], include_text_analysis: bool = False
#     ) -> Dict[str, Any]:
#         """
#         Analyze customer sentiment based on call patterns

#         Args:
#             data: Dictionary of DataFrames
#             include_text_analysis: Whether to include text analysis (not implemented yet)

#         Returns:
#             Analysis result
#         """
#         result_data = {}
#         charts = []

#         # Combine all data for sentiment analysis
#         df_combined = pd.DataFrame()
#         for _, df in data.items():
#             if not df.empty:
#                 df_combined = pd.concat([df_combined, df])

#         if df_combined.empty:
#             return self._format_result(
#                 {"error": "No data available for sentiment analysis"}, []
#             )

#         # Ensure Call Date is datetime if it exists
#         if "Call Date" in df_combined.columns:
#             df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

#         # Since we don't have actual sentiment scores, we'll use proxy metrics:
#         # 1. Call length (shorter calls might indicate better resolution)
#         # 2. Call status (certain statuses might indicate positive outcomes)
#         # 3. Repeat calls (fewer repeat calls might indicate better resolution)
#         # 4. Missed calls (fewer missed calls might indicate better customer experience)

#         # Calculate sentiment metrics

#         # 1. Call length analysis
#         if "Length" in df_combined.columns:
#             # Remove outliers
#             q1 = df_combined["Length"].quantile(0.25)
#             q3 = df_combined["Length"].quantile(0.75)
#             iqr = q3 - q1
#             length_min = max(0, q1 - 1.5 * iqr)
#             length_max = q3 + 1.5 * iqr

#             df_length = df_combined[
#                 (df_combined["Length"] >= length_min)
#                 & (df_combined["Length"] <= length_max)
#             ]

#             # Calculate statistics
#             avg_length = df_length["Length"].mean()
#             median_length = df_length["Length"].median()

#             result_data["call_length"] = {
#                 "average": avg_length,
#                 "median": median_length,
#                 "min": df_length["Length"].min(),
#                 "max": df_length["Length"].max(),
#             }

#             # Create histogram of call lengths
#             length_bins = [0, 60, 120, 180, 300, 600, 1200]
#             length_labels = ["<1m", "1-2m", "2-3m", "3-5m", "5-10m", ">10m"]

#             length_hist = (
#                 pd.cut(
#                     df_length["Length"],
#                     bins=length_bins,
#                     labels=length_labels,
#                     include_lowest=True,
#                 )
#                 .value_counts()
#                 .reset_index()
#             )

#             length_hist.columns = ["duration", "count"]
#             length_hist = length_hist.sort_values("duration")

#             result_data["call_length_distribution"] = length_hist.to_dict(
#                 orient="records"
#             )

#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Call Duration Distribution",
#                     x_label="Duration",
#                     y_label="Number of Calls",
#                     data={
#                         "x": length_hist["duration"].tolist(),
#                         "y": length_hist["count"].tolist(),
#                     },
#                 )
#             )

#             # Call length by campaign
#             if "Campaign" in df_combined.columns:
#                 campaign_length = (
#                     df_length.groupby("Campaign")["Length"].mean().reset_index()
#                 )
#                 campaign_length = campaign_length.sort_values("Length")

#                 result_data["campaign_avg_length"] = campaign_length.to_dict(
#                     orient="records"
#                 )

#                 charts.append(
#                     self._prepare_chart_data(
#                         chart_type="bar",
#                         title="Average Call Duration by Campaign",
#                         x_label="Campaign",
#                         y_label="Average Duration (seconds)",
#                         data={
#                             "x": campaign_length["Campaign"].tolist(),
#                             "y": campaign_length["Length"].tolist(),
#                         },
#                     )
#                 )

#         # 2. Call status analysis
#         if "Status" in df_combined.columns:
#             status_counts = df_combined["Status"].value_counts().reset_index()
#             status_counts.columns = ["status", "count"]

#             # Categorize statuses as positive, neutral, or negative
#             positive_statuses = ["ANSWERED", "COMPLETED", "RESOLVED"]
#             negative_statuses = ["FAILED", "BUSY", "NO ANSWER", "CANCELED", "MISSED"]

#             status_counts["sentiment"] = status_counts["status"].apply(
#                 lambda x: (
#                     "Positive"
#                     if x in positive_statuses
#                     else ("Negative" if x in negative_statuses else "Neutral")
#                 )
#             )

#             # Group by sentiment
#             sentiment_counts = (
#                 status_counts.groupby("sentiment")["count"].sum().reset_index()
#             )

#             result_data["status_sentiment"] = {
#                 "by_status": status_counts.to_dict(orient="records"),
#                 "by_sentiment": sentiment_counts.to_dict(orient="records"),
#             }

#             # Create pie chart for sentiment distribution
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="pie",
#                     title="Call Outcome Sentiment Distribution",
#                     x_label="",
#                     y_label="",
#                     data={
#                         "labels": sentiment_counts["sentiment"].tolist(),
#                         "values": sentiment_counts["count"].tolist(),
#                     },
#                 )
#             )

#         # 3. Repeat call analysis (if Phone is available)
#         if "Phone" in df_combined.columns:
#             # Count calls per phone number
#             call_counts = (
#                 df_combined.groupby("Phone").size().reset_index(name="call_count")
#             )

#             # Calculate repeat call metrics
#             total_customers = len(call_counts)
#             repeat_customers = len(call_counts[call_counts["call_count"] > 1])
#             repeat_rate = (
#                 repeat_customers / total_customers if total_customers > 0 else 0
#             )

#             result_data["repeat_calls"] = {
#                 "total_customers": total_customers,
#                 "repeat_customers": repeat_customers,
#                 "repeat_rate": repeat_rate,
#                 "avg_calls_per_customer": call_counts["call_count"].mean(),
#             }

#             # Create histogram of calls per customer
#             call_count_hist = call_counts["call_count"].value_counts().reset_index()
#             call_count_hist.columns = ["calls_per_customer", "customer_count"]
#             call_count_hist = call_count_hist.sort_values("calls_per_customer")

#             # Limit to reasonable range for visualization
#             call_count_hist = call_count_hist[
#                 call_count_hist["calls_per_customer"] <= 10
#             ]

#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Calls per Customer",
#                     x_label="Number of Calls",
#                     y_label="Number of Customers",
#                     data={
#                         "x": call_count_hist["calls_per_customer"].tolist(),
#                         "y": call_count_hist["customer_count"].tolist(),
#                     },
#                 )
#             )

#         # 4. Call type sentiment (inbound, outbound, missed)
#         if "Call Type" in df_combined.columns:
#             call_type_counts = df_combined["Call Type"].value_counts().reset_index()
#             call_type_counts.columns = ["call_type", "count"]

#             # Assign sentiment scores to call types
#             call_type_sentiment = {
#                 "Inbound": 0,  # Neutral
#                 "Outbound": 0.5,  # Slightly positive (proactive)
#                 "Missed Call": -0.5,  # Slightly negative
#             }

#             call_type_counts["sentiment_score"] = call_type_counts["call_type"].map(
#                 lambda x: call_type_sentiment.get(x, 0)
#             )

#             # Calculate weighted sentiment score
#             total_calls = call_type_counts["count"].sum()
#             weighted_sentiment = (
#                 sum(call_type_counts["sentiment_score"] * call_type_counts["count"])
#                 / total_calls
#                 if total_calls > 0
#                 else 0
#             )

#             result_data["call_type_sentiment"] = {
#                 "by_type": call_type_counts.to_dict(orient="records"),
#                 "weighted_sentiment": weighted_sentiment,
#             }

#             # Create pie chart for call type distribution
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="pie",
#                     title="Call Type Distribution",
#                     x_label="",
#                     y_label="",
#                     data={
#                         "labels": call_type_counts["call_type"].tolist(),
#                         "values": call_type_counts["count"].tolist(),
#                     },
#                 )
#             )

#         # Calculate overall sentiment score (simplified)
#         sentiment_score = 0
#         sentiment_components = {}

#         # Component 1: Status sentiment
#         if "status_sentiment" in result_data:
#             sentiment_by_type = {
#                 item["sentiment"]: item["count"]
#                 for item in result_data["status_sentiment"]["by_sentiment"]
#             }
#             total = sum(sentiment_by_type.values())

#             if total > 0:
#                 # Calculate weighted score (-1 to 1 scale)
#                 status_score = (
#                     sentiment_by_type.get("Positive", 0) * 1
#                     + sentiment_by_type.get("Neutral", 0) * 0
#                     + sentiment_by_type.get("Negative", 0) * -1
#                 ) / total

#                 sentiment_components["status_score"] = status_score

#         # Component 2: Repeat call rate
#         if "repeat_calls" in result_data:
#             # Convert repeat rate to a score (-1 to 1 scale, where lower repeat rate is better)
#             repeat_score = 1 - (2 * result_data["repeat_calls"]["repeat_rate"])
#             sentiment_components["repeat_score"] = repeat_score

#         # Component 3: Call type sentiment
#         if "call_type_sentiment" in result_data:
#             sentiment_components["call_type_score"] = result_data[
#                 "call_type_sentiment"
#             ]["weighted_sentiment"]

#         # Calculate overall score if we have components
#         if sentiment_components:
#             # Equal weighting for simplicity
#             sentiment_score = sum(sentiment_components.values()) / len(
#                 sentiment_components
#             )

#             # Convert to a 0-100 scale for easier interpretation
#             normalized_score = (sentiment_score + 1) * 50

#             result_data["overall_sentiment"] = {
#                 "score": normalized_score,
#                 "components": sentiment_components,
#             }

#             # Create gauge chart for overall sentiment
#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="gauge",
#                     title="Overall Customer Sentiment Score",
#                     x_label="",
#                     y_label="",
#                     data={
#                         "value": normalized_score,
#                         "min": 0,
#                         "max": 100,
#                         "thresholds": [
#                             {"value": 33, "color": "red"},
#                             {"value": 66, "color": "yellow"},
#                             {"value": 100, "color": "green"},
#                         ],
#                     },
#                 )
#             )

#         return self._format_result(result_data, charts)


# class AgentSpecializationAnalyzer(BaseAnalyzer):
#     """Analyzer for agent specialization"""

#     def analyze(
#         self, data: Dict[str, pd.DataFrame], min_calls: int = 10
#     ) -> Dict[str, Any]:
#         """
#         Analyze agent specialization based on performance across different campaigns and call types

#         Args:
#             data: Dictionary of DataFrames
#             min_calls: Minimum number of calls to consider for specialization analysis

#         Returns:
#             Analysis result
#         """
#         result_data = {}
#         charts = []

#         # Combine all data for agent specialization analysis
#         df_combined = pd.DataFrame()
#         for _, df in data.items():
#             if not df.empty and "User" in df.columns:
#                 df_combined = pd.concat([df_combined, df])

#         if df_combined.empty:
#             return self._format_result(
#                 {"error": "No data available for agent specialization analysis"}, []
#             )

#         # Ensure Call Date is datetime if it exists
#         if "Call Date" in df_combined.columns:
#             df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

#         # Get list of agents
#         agents = df_combined["User"].unique().tolist()

#         # Calculate agent specialization metrics
#         agent_specializations = []

#         for agent in agents:
#             agent_data = df_combined[df_combined["User"] == agent]

#             # Skip agents with too few calls
#             if len(agent_data) < min_calls:
#                 continue

#             specialization = {"agent": agent, "total_calls": len(agent_data)}

#             # Campaign specialization
#             if "Campaign" in agent_data.columns:
#                 campaign_counts = agent_data["Campaign"].value_counts()
#                 top_campaigns = campaign_counts.head(3).to_dict()

#                 # Calculate campaign concentration (higher means more specialized)
#                 campaign_concentration = (
#                     (campaign_counts.max() / len(agent_data))
#                     if len(agent_data) > 0
#                     else 0
#                 )

#                 specialization["top_campaigns"] = top_campaigns
#                 specialization["campaign_concentration"] = campaign_concentration

#                 # Calculate campaign performance if we have status information
#                 if "Status" in agent_data.columns:
#                     # Assuming certain statuses indicate success
#                     success_statuses = ["ANSWERED", "COMPLETED", "RESOLVED"]

#                     campaign_performance = []
#                     for campaign, count in top_campaigns.items():
#                         campaign_data = agent_data[agent_data["Campaign"] == campaign]
#                         success_count = campaign_data[
#                             campaign_data["Status"].isin(success_statuses)
#                         ].shape[0]
#                         success_rate = success_count / count if count > 0 else 0

#                         campaign_performance.append(
#                             {
#                                 "campaign": campaign,
#                                 "calls": count,
#                                 "success_count": success_count,
#                                 "success_rate": success_rate,
#                             }
#                         )

#                     specialization["campaign_performance"] = campaign_performance

#             # Call type specialization
#             if "Call Type" in agent_data.columns:
#                 call_type_counts = agent_data["Call Type"].value_counts()
#                 top_call_types = call_type_counts.head(3).to_dict()

#                 # Calculate call type concentration
#                 call_type_concentration = (
#                     (call_type_counts.max() / len(agent_data))
#                     if len(agent_data) > 0
#                     else 0
#                 )

#                 specialization["top_call_types"] = top_call_types
#                 specialization["call_type_concentration"] = call_type_concentration

#             # Language specialization
#             if "Language" in agent_data.columns:
#                 language_counts = agent_data["Language"].value_counts()
#                 top_languages = language_counts.head(2).to_dict()

#                 # Calculate language concentration
#                 language_concentration = (
#                     (language_counts.max() / len(agent_data))
#                     if len(agent_data) > 0
#                     else 0
#                 )

#                 specialization["top_languages"] = top_languages
#                 specialization["language_concentration"] = language_concentration

#             # Call length metrics
#             if "Length" in agent_data.columns:
#                 specialization["avg_call_length"] = agent_data["Length"].mean()

#                 # Calculate call length by campaign
#                 if "Campaign" in agent_data.columns:
#                     campaign_length = (
#                         agent_data.groupby("Campaign")["Length"].mean().to_dict()
#                     )
#                     specialization["campaign_avg_length"] = campaign_length

#             # Calculate overall specialization score
#             specialization_components = []

#             if "campaign_concentration" in specialization:
#                 specialization_components.append(
#                     specialization["campaign_concentration"]
#                 )

#             if "call_type_concentration" in specialization:
#                 specialization_components.append(
#                     specialization["call_type_concentration"]
#                 )

#             if "language_concentration" in specialization:
#                 specialization_components.append(
#                     specialization["language_concentration"]
#                 )

#             if specialization_components:
#                 # Average of concentration metrics (0-1 scale)
#                 specialization["specialization_score"] = sum(
#                     specialization_components
#                 ) / len(specialization_components)

#             agent_specializations.append(specialization)

#         # Sort by specialization score
#         if agent_specializations and "specialization_score" in agent_specializations[0]:
#             agent_specializations = sorted(
#                 agent_specializations,
#                 key=lambda x: x["specialization_score"],
#                 reverse=True,
#             )

#         result_data["agent_specializations"] = agent_specializations

#         # Create charts

#         # Agent specialization scores
#         if agent_specializations and "specialization_score" in agent_specializations[0]:
#             spec_scores = pd.DataFrame(agent_specializations)[
#                 ["agent", "specialization_score"]
#             ]

#             charts.append(
#                 self._prepare_chart_data(
#                     chart_type="bar",
#                     title="Agent Specialization Scores",
#                     x_label="Agent",
#                     y_label="Specialization Score",
#                     data={
#                         "x": spec_scores["agent"].tolist(),
#                         "y": spec_scores["specialization_score"].tolist(),
#                     },
#                 )
#             )

#         # Campaign distribution for top agents
#         if agent_specializations and "top_campaigns" in agent_specializations[0]:
#             # Take top 5 agents by specialization score
#             top_agents = agent_specializations[:5]

#             for agent_spec in top_agents:
#                 if "top_campaigns" in agent_spec:
#                     campaigns = list(agent_spec["top_campaigns"].keys())
#                     counts = list(agent_spec["top_campaigns"].values())

#                     charts.append(
#                         self._prepare_chart_data(
#                             chart_type="pie",
#                             title=f"Campaign Distribution for {agent_spec['agent']}",
#                             x_label="",
#                             y_label="",
#                             data={"labels": campaigns, "values": counts},
#                         )
#                     )

#         # Recommended agent assignments
#         if "Campaign" in df_combined.columns and "Status" in df_combined.columns:
#             # Assuming certain statuses indicate success
#             success_statuses = ["ANSWERED", "COMPLETED", "RESOLVED"]

#             # Calculate success rate by agent and campaign
#             campaign_success = []

#             for agent in agents:
#                 agent_data = df_combined[df_combined["User"] == agent]

#                 for campaign in agent_data["Campaign"].unique():
#                     campaign_data = agent_data[agent_data["Campaign"] == campaign]

#                     # Only consider if enough calls
#                     if len(campaign_data) >= min_calls:
#                         success_count = campaign_data[
#                             campaign_data["Status"].isin(success_statuses)
#                         ].shape[0]
#                         success_rate = success_count / len(campaign_data)

#                         campaign_success.append(
#                             {
#                                 "agent": agent,
#                                 "campaign": campaign,
#                                 "calls": len(campaign_data),
#                                 "success_rate": success_rate,
#                             }
#                         )

#             # Find best agent for each campaign
#             if campaign_success:
#                 campaign_success_df = pd.DataFrame(campaign_success)
#                 best_agents = campaign_success_df.loc[
#                     campaign_success_df.groupby("campaign")["success_rate"].idxmax()
#                 ]

#                 result_data["recommended_assignments"] = best_agents.to_dict(
#                     orient="records"
#                 )

#                 # Create chart for recommended assignments
#                 charts.append(
#                     self._prepare_chart_data(
#                         chart_type="bar",
#                         title="Recommended Agent Assignments by Campaign",
#                         x_label="Campaign",
#                         y_label="Success Rate",
#                         data={
#                             "x": best_agents["campaign"].tolist(),
#                             "y": best_agents["success_rate"].tolist(),
#                             "text": best_agents["agent"].tolist(),
#                         },
#                     )
#                 )

#         return self._format_result(result_data, charts)


# class ConversionRateOptimizer(BaseAnalyzer):
#     """Optimizer for conversion rates"""

#     pass


# class QueueOptimizer(BaseAnalyzer):
#     """Optimizer for queue management"""

#     pass


# class SeasonalTrendAnalyzer(BaseAnalyzer):
#     """Analyzer for seasonal trends"""

#     pass


# class GeographicAnalyzer(BaseAnalyzer):
#     """Analyzer for geographic insights"""

#     pass


# class CallQualityScorer(BaseAnalyzer):
#     """Scorer for call quality"""

#     pass


# class ABTestingAnalyzer(BaseAnalyzer):
#     """Analyzer for A/B testing"""

#     pass


# class ChurnPredictor(BaseAnalyzer):
#     """Predictor for customer churn"""

#     pass


# class CrossSellingDetector(BaseAnalyzer):
#     """Detector for cross-selling opportunities"""

#     pass


# class AnomalyDetector(BaseAnalyzer):
#     """Detector for anomalies"""

#     pass


# class CallbackOptimizer(BaseAnalyzer):
#     """Optimizer for callbacks"""

#     pass


# class KnowledgeBaseEnhancer(BaseAnalyzer):
#     """Enhancer for knowledge base"""

#     pass
