import asyncio
import uuid
import logging
import time
from typing import Dict, Any, List, Callable, Optional, Awaitable
from datetime import datetime
import traceback
import threading
from concurrent.futures import ThreadPoolExecutor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskStatus:
    """Task status constants"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

class Task:
    """Class representing a background task"""
    
    def __init__(self, task_id: str, task_type: str, params: Dict[str, Any]):
        """
        Initialize a task
        
        Args:
            task_id: Unique identifier for the task
            task_type: Type of task (e.g., 'train_model', 'generate_report')
            params: Parameters for the task
        """
        self.task_id = task_id
        self.task_type = task_type
        self.params = params
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.result = None
        self.error = None
        self.progress = 0
        self.progress_message = "Task created"
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert task to dictionary
        
        Returns:
            Dictionary representation of the task
        """
        return {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "progress": self.progress,
            "progress_message": self.progress_message,
            "result": self.result,
            "error": self.error,
            "params": {k: str(v) if isinstance(v, (bytes, bytearray)) else v for k, v in self.params.items()}
        }

class TaskManager:
    """Manager for background tasks"""
    
    _instance = None
    
    def __new__(cls):
        """Singleton pattern to ensure only one task manager exists"""
        if cls._instance is None:
            cls._instance = super(TaskManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the task manager"""
        if self._initialized:
            return
            
        self.tasks: Dict[str, Task] = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        self._initialized = True
        
    def create_task(self, task_type: str, params: Dict[str, Any]) -> str:
        """
        Create a new task
        
        Args:
            task_type: Type of task
            params: Parameters for the task
            
        Returns:
            Task ID
        """
        task_id = str(uuid.uuid4())
        task = Task(task_id, task_type, params)
        self.tasks[task_id] = task
        return task_id
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        Get a task by ID
        
        Args:
            task_id: Task ID
            
        Returns:
            Task object or None if not found
        """
        return self.tasks.get(task_id)
    
    def get_tasks(self, task_type: Optional[str] = None, status: Optional[str] = None) -> List[Task]:
        """
        Get tasks filtered by type and/or status
        
        Args:
            task_type: Optional task type filter
            status: Optional status filter
            
        Returns:
            List of matching tasks
        """
        tasks = list(self.tasks.values())
        
        if task_type:
            tasks = [task for task in tasks if task.task_type == task_type]
            
        if status:
            tasks = [task for task in tasks if task.status == status]
            
        return sorted(tasks, key=lambda t: t.created_at, reverse=True)
    
    def run_task(self, task_id: str, func: Callable, *args, **kwargs) -> None:
        """
        Run a task in the background
        
        Args:
            task_id: Task ID
            func: Function to run
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
        """
        task = self.get_task(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return
            
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        task.progress = 0
        task.progress_message = "Task started"
        
        def task_wrapper():
            try:
                # Run the task function
                result = func(*args, **kwargs)
                
                # Update task status
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                task.progress = 100
                task.progress_message = "Task completed successfully"
                task.result = result
                
                logger.info(f"Task {task_id} completed successfully")
                
            except Exception as e:
                # Handle task failure
                error_message = str(e)
                error_traceback = traceback.format_exc()
                
                task.status = TaskStatus.FAILED
                task.completed_at = datetime.now()
                task.error = {
                    "message": error_message,
                    "traceback": error_traceback
                }
                task.progress_message = f"Task failed: {error_message}"
                
                logger.error(f"Task {task_id} failed: {error_message}")
                logger.error(error_traceback)
        
        # Submit task to thread pool
        self.executor.submit(task_wrapper)
    
    def update_progress(self, task_id: str, progress: int, message: str = None) -> bool:
        """
        Update task progress
        
        Args:
            task_id: Task ID
            progress: Progress percentage (0-100)
            message: Optional progress message
            
        Returns:
            True if successful, False otherwise
        """
        task = self.get_task(task_id)
        if not task:
            return False
            
        task.progress = min(max(0, progress), 100)
        if message:
            task.progress_message = message
            
        return True
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a task (not fully implemented - would need to handle actual cancellation)
        
        Args:
            task_id: Task ID
            
        Returns:
            True if successful, False otherwise
        """
        task = self.get_task(task_id)
        if not task or task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            return False
            
        task.status = TaskStatus.FAILED
        task.completed_at = datetime.now()
        task.error = {
            "message": "Task cancelled by user",
            "traceback": None
        }
        task.progress_message = "Task cancelled by user"
        
        return True
    
    def cleanup_old_tasks(self, max_age_hours: int = 24) -> int:
        """
        Remove old completed or failed tasks
        
        Args:
            max_age_hours: Maximum age in hours
            
        Returns:
            Number of tasks removed
        """
        now = datetime.now()
        task_ids_to_remove = []
        
        for task_id, task in self.tasks.items():
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                if task.completed_at and (now - task.completed_at).total_seconds() > max_age_hours * 3600:
                    task_ids_to_remove.append(task_id)
        
        for task_id in task_ids_to_remove:
            del self.tasks[task_id]
            
        return len(task_ids_to_remove)

# Global task manager instance
task_manager = TaskManager()
