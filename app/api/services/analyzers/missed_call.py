"""
Missed call analyzer for call flow analytics.
"""

import pandas as pd
from typing import Dict, Any
import logging
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MissedCallAnalyzer(BaseAnalyzer):
    """Analyzer for missed calls"""

    def analyze(
        self, data: Dict[str, pd.DataFrame], include_callbacks: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze missed calls

        Args:
            data: Dictionary of DataFrames
            include_callbacks: Whether to include callback analysis

        Returns:
            Analysis result
        """
        result_data = {}
        charts = []

        # Get missed call data
        if "missed" not in data or data["missed"] is None or data["missed"].empty:
            return self._format_result({"error": "No missed call data available"}, [])

        df_missed = data["missed"].copy()

        # Ensure Call Date is datetime
        if "Call Date" in df_missed.columns:
            df_missed["Call Date"] = pd.to_datetime(df_missed["Call Date"])
            df_missed["Hour"] = df_missed["Call Date"].dt.hour
            df_missed["Weekday"] = df_missed["Call Date"].dt.dayofweek
            df_missed["WeekdayName"] = df_missed["Call Date"].dt.day_name()
            df_missed["Date"] = df_missed["Call Date"].dt.date

        # Basic missed call metrics
        total_missed = len(df_missed)
        result_data["total_missed_calls"] = total_missed

        # Missed calls by hour
        if "Hour" in df_missed.columns:
            hourly_missed = (
                df_missed.groupby("Hour").size().reset_index(name="missed_count")
            )
            result_data["hourly_missed"] = hourly_missed.to_dict(orient="records")

            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Missed Calls by Hour of Day",
                    x_label="Hour of Day",
                    y_label="Number of Missed Calls",
                    data={
                        "x": hourly_missed["Hour"].tolist(),
                        "y": hourly_missed["missed_count"].tolist(),
                    },
                )
            )

        # Missed calls by weekday
        if "WeekdayName" in df_missed.columns:
            weekday_missed = (
                df_missed.groupby("WeekdayName").size().reset_index(name="missed_count")
            )

            # Sort by day of week
            weekday_order = [
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
                "Sunday",
            ]
            weekday_missed["sort_order"] = weekday_missed["WeekdayName"].apply(
                lambda x: weekday_order.index(x)
            )
            weekday_missed = weekday_missed.sort_values("sort_order")
            weekday_missed = weekday_missed.drop("sort_order", axis=1)

            result_data["weekday_missed"] = weekday_missed.to_dict(orient="records")

            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Missed Calls by Day of Week",
                    x_label="Day of Week",
                    y_label="Number of Missed Calls",
                    data={
                        "x": weekday_missed["WeekdayName"].tolist(),
                        "y": weekday_missed["missed_count"].tolist(),
                    },
                )
            )

        # Missed calls by campaign
        if "Campaign" in df_missed.columns:
            campaign_missed = (
                df_missed.groupby("Campaign").size().reset_index(name="missed_count")
            )
            campaign_missed = campaign_missed.sort_values(
                "missed_count", ascending=False
            )
            result_data["campaign_missed"] = campaign_missed.to_dict(orient="records")

            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Missed Calls by Campaign",
                    x_label="Campaign",
                    y_label="Number of Missed Calls",
                    data={
                        "x": campaign_missed["Campaign"].tolist(),
                        "y": campaign_missed["missed_count"].tolist(),
                    },
                )
            )

        # Callback analysis
        if include_callbacks and "First Callback Time" in df_missed.columns:
            # Calculate callback rate
            df_missed["Has Callback"] = df_missed["First Callback Time"].notna()
            callback_count = df_missed["Has Callback"].sum()
            callback_rate = callback_count / total_missed if total_missed > 0 else 0

            result_data["callback_metrics"] = {
                "callback_count": int(callback_count),
                "callback_rate": callback_rate,
            }

            # Calculate time to first callback
            df_with_callback = df_missed[df_missed["Has Callback"]].copy()
            if not df_with_callback.empty:
                df_with_callback["First Callback Time"] = pd.to_datetime(
                    df_with_callback["First Callback Time"]
                )
                df_with_callback["Time to Callback"] = (
                    df_with_callback["First Callback Time"]
                    - df_with_callback["Call Date"]
                ).dt.total_seconds() / 60  # in minutes

                avg_callback_time = df_with_callback["Time to Callback"].mean()
                median_callback_time = df_with_callback["Time to Callback"].median()

                result_data["callback_metrics"][
                    "avg_callback_time_minutes"
                ] = avg_callback_time
                result_data["callback_metrics"][
                    "median_callback_time_minutes"
                ] = median_callback_time

                # Distribution of callback times
                callback_bins = [0, 15, 30, 60, 120, 240, 480, 1440]  # in minutes
                callback_labels = [
                    "0-15m",
                    "15-30m",
                    "30-60m",
                    "1-2h",
                    "2-4h",
                    "4-8h",
                    "8-24h",
                ]

                df_with_callback["Callback Time Bin"] = pd.cut(
                    df_with_callback["Time to Callback"],
                    bins=callback_bins,
                    labels=callback_labels,
                    include_lowest=True,
                )

                callback_distribution = (
                    df_with_callback["Callback Time Bin"].value_counts().reset_index()
                )
                callback_distribution.columns = ["time_range", "count"]
                callback_distribution = callback_distribution.sort_values("time_range")

                result_data["callback_distribution"] = callback_distribution.to_dict(
                    orient="records"
                )

                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Distribution of Time to First Callback",
                        x_label="Time Range",
                        y_label="Number of Callbacks",
                        data={
                            "x": callback_distribution["time_range"].tolist(),
                            "y": callback_distribution["count"].tolist(),
                        },
                    )
                )

            # Connected callback rate
            if "Connected Callback Time" in df_missed.columns:
                df_missed["Has Connected Callback"] = df_missed[
                    "Connected Callback Time"
                ].notna()
                connected_count = df_missed["Has Connected Callback"].sum()
                connected_rate = (
                    connected_count / callback_count if callback_count > 0 else 0
                )

                result_data["callback_metrics"]["connected_count"] = int(
                    connected_count
                )
                result_data["callback_metrics"]["connected_rate"] = connected_rate

                # Create pie chart for callback outcomes
                charts.append(
                    self._prepare_chart_data(
                        chart_type="pie",
                        title="Callback Outcomes",
                        x_label="",
                        y_label="",
                        data={
                            "labels": ["Connected", "Not Connected", "No Callback"],
                            "values": [
                                int(connected_count),
                                int(callback_count - connected_count),
                                int(total_missed - callback_count),
                            ],
                        },
                    )
                )

        # SMS analysis
        if "SMS Status" in df_missed.columns:
            sms_status = df_missed["SMS Status"].value_counts().reset_index()
            sms_status.columns = ["status", "count"]
            result_data["sms_status"] = sms_status.to_dict(orient="records")

            charts.append(
                self._prepare_chart_data(
                    chart_type="pie",
                    title="SMS Status for Missed Calls",
                    x_label="",
                    y_label="",
                    data={
                        "labels": sms_status["status"].tolist(),
                        "values": sms_status["count"].tolist(),
                    },
                )
            )

        return self._format_result(result_data, charts)
