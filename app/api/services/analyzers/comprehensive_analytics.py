"""
Comprehensive Call Flow Analytics Module

This module provides comprehensive analysis of call flow data including:
- Average call time, total duration, standard deviation
- Day-wise and date-wise filtering
- Language, campaign, agent, and call type segregation
- Customer satisfaction analysis
- Statistical analysis with percentiles and outlier detection
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
from .base import BaseAnalyzer

logger = logging.getLogger(__name__)


class ComprehensiveAnalyzer(BaseAnalyzer):
    """Comprehensive analyzer for call flow data with all requested metrics and filters"""

    def analyze(
        self,
        data: Dict[str, pd.DataFrame],
        request_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of call flow data

        Args:
            data: Dictionary of DataFrames (inbound, outbound, missed, combined)
            request_params: Analysis parameters from request

        Returns:
            Comprehensive analysis results with charts and recommendations
        """
        try:
            # Initialize results structure
            result = {"data": {}, "charts": [], "recommendations": [], "summary": {}}

            # Combine all data for comprehensive analysis
            df_combined = self._combine_data(data)

            if df_combined.empty:
                return self._format_result(
                    {"error": "No data available for comprehensive analysis"}, []
                )

            # Apply filters based on request parameters
            df_filtered = self._apply_filters(df_combined, request_params or {})

            if df_filtered.empty:
                return self._format_result(
                    {"error": "No data available after applying filters"}, []
                )

            # Core metrics analysis
            result["data"]["core_metrics"] = self._analyze_core_metrics(df_filtered)
            result["charts"].extend(
                self._create_core_metrics_charts(result["data"]["core_metrics"])
            )

            # Time-based analysis
            result["data"]["time_analysis"] = self._analyze_time_patterns(df_filtered)
            result["charts"].extend(
                self._create_time_analysis_charts(result["data"]["time_analysis"])
            )

            # Segmentation analysis
            result["data"]["segmentation"] = self._analyze_segmentation(df_filtered)
            result["charts"].extend(
                self._create_segmentation_charts(result["data"]["segmentation"])
            )

            # Statistical analysis
            result["data"]["statistics"] = self._analyze_statistics(df_filtered)
            result["charts"].extend(
                self._create_statistical_charts(result["data"]["statistics"])
            )

            # Customer satisfaction analysis (if available)
            satisfaction_data = self._analyze_satisfaction(df_filtered, request_params)
            if satisfaction_data:
                result["data"]["satisfaction"] = satisfaction_data
                result["charts"].extend(
                    self._create_satisfaction_charts(satisfaction_data)
                )

            # Generate summary and recommendations
            result["summary"] = self._generate_summary(result["data"])
            result["recommendations"] = self._generate_recommendations(result["data"])

            return self._format_result(result["data"], result["charts"])

        except Exception as e:
            logger.error(f"Error in comprehensive analysis: {str(e)}")
            return self._format_result({"error": f"Analysis failed: {str(e)}"}, [])

    def _combine_data(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Combine all data sources into a single DataFrame"""
        combined_df = pd.DataFrame()

        for data_type, df in data.items():
            if df is not None and not df.empty and data_type != "combined":
                # Add data type column
                df_copy = df.copy()
                df_copy["call_type"] = data_type
                combined_df = pd.concat([combined_df, df_copy], ignore_index=True)

        # Ensure required columns exist and are properly formatted
        if "Call Date" in combined_df.columns:
            combined_df["Call Date"] = pd.to_datetime(
                combined_df["Call Date"], errors="coerce"
            )
            combined_df["Hour"] = combined_df["Call Date"].dt.hour
            combined_df["Weekday"] = combined_df["Call Date"].dt.day_name()
            combined_df["Date"] = combined_df["Call Date"].dt.date
            combined_df["Month"] = combined_df["Call Date"].dt.month
            combined_df["Year"] = combined_df["Call Date"].dt.year
            combined_df["Day"] = combined_df["Call Date"].dt.day

        # Ensure Language column exists
        if "Campaign" in combined_df.columns and "Language" not in combined_df.columns:
            combined_df["Language"] = combined_df["Campaign"].apply(
                lambda x: "Arabic" if "Arabic" in str(x) else "English"
            )

        # Ensure Length column is numeric
        if "Length" in combined_df.columns:
            combined_df["Length"] = pd.to_numeric(
                combined_df["Length"], errors="coerce"
            )

        return combined_df

    def _apply_filters(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """Apply filters based on request parameters"""
        filtered_df = df.copy()

        # Date range filtering
        if params.get("start_date") and "Call Date" in filtered_df.columns:
            start_date = pd.to_datetime(params["start_date"])
            filtered_df = filtered_df[filtered_df["Call Date"] >= start_date]

        if params.get("end_date") and "Call Date" in filtered_df.columns:
            end_date = pd.to_datetime(params["end_date"])
            filtered_df = filtered_df[filtered_df["Call Date"] <= end_date]

        # Day of week filtering
        if params.get("filter_by_day") and "Weekday" in filtered_df.columns:
            filtered_df = filtered_df[
                filtered_df["Weekday"].isin(params["filter_by_day"])
            ]

        # Language filtering
        if params.get("filter_by_language") and "Language" in filtered_df.columns:
            filtered_df = filtered_df[
                filtered_df["Language"].isin(params["filter_by_language"])
            ]

        # Campaign filtering
        if params.get("filter_by_campaign") and "Campaign" in filtered_df.columns:
            filtered_df = filtered_df[
                filtered_df["Campaign"].isin(params["filter_by_campaign"])
            ]

        # Agent filtering
        if params.get("filter_by_agent") and "User" in filtered_df.columns:
            filtered_df = filtered_df[
                filtered_df["User"].isin(params["filter_by_agent"])
            ]

        # Call type filtering
        if params.get("filter_by_call_type") and "call_type" in filtered_df.columns:
            filtered_df = filtered_df[
                filtered_df["call_type"].isin(params["filter_by_call_type"])
            ]

        return filtered_df

    def _analyze_core_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze core metrics: avg call time, total duration, standard deviation"""
        metrics = {}

        if "Length" in df.columns:
            # Remove null values and convert to numeric
            call_lengths = pd.to_numeric(df["Length"], errors="coerce").dropna()

            if not call_lengths.empty:
                metrics["average_call_time"] = {
                    "seconds": float(call_lengths.mean()),
                    "minutes": float(call_lengths.mean() / 60),
                    "formatted": f"{int(call_lengths.mean() // 60)}m {int(call_lengths.mean() % 60)}s",
                }

                metrics["total_duration"] = {
                    "seconds": float(call_lengths.sum()),
                    "minutes": float(call_lengths.sum() / 60),
                    "hours": float(call_lengths.sum() / 3600),
                    "formatted": f"{int(call_lengths.sum() // 3600)}h {int((call_lengths.sum() % 3600) // 60)}m",
                }

                metrics["standard_deviation"] = {
                    "seconds": float(call_lengths.std()),
                    "minutes": float(call_lengths.std() / 60),
                    "formatted": f"{int(call_lengths.std() // 60)}m {int(call_lengths.std() % 60)}s",
                }

                metrics["call_volume"] = {
                    "total_calls": len(df),
                    "answered_calls": len(call_lengths),
                    "missed_calls": len(df) - len(call_lengths),
                    "answer_rate": (
                        float(len(call_lengths) / len(df)) if len(df) > 0 else 0
                    ),
                }
            else:
                metrics["error"] = "No valid call length data available"
        else:
            metrics["error"] = "Length column not found in data"

        return metrics

    def _analyze_time_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze time-based patterns"""
        patterns = {}

        if "Call Date" in df.columns:
            # Hourly patterns
            if "Hour" in df.columns:
                hourly_volume = df.groupby("Hour").size().to_dict()
                patterns["hourly_volume"] = hourly_volume

                if "Length" in df.columns:
                    hourly_avg_duration = df.groupby("Hour")["Length"].mean().to_dict()
                    patterns["hourly_avg_duration"] = {
                        hour: float(duration)
                        for hour, duration in hourly_avg_duration.items()
                    }

            # Daily patterns
            if "Weekday" in df.columns:
                daily_volume = df.groupby("Weekday").size().to_dict()
                patterns["daily_volume"] = daily_volume

                if "Length" in df.columns:
                    daily_avg_duration = (
                        df.groupby("Weekday")["Length"].mean().to_dict()
                    )
                    patterns["daily_avg_duration"] = {
                        day: float(duration)
                        for day, duration in daily_avg_duration.items()
                    }

            # Date-wise trends
            if "Date" in df.columns:
                date_volume = df.groupby("Date").size().to_dict()
                patterns["date_volume"] = {
                    str(date): volume for date, volume in date_volume.items()
                }

        return patterns

    def _analyze_segmentation(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze data by different segments"""
        segmentation = {}

        # Language segmentation
        if "Language" in df.columns:
            lang_stats = self._calculate_segment_stats(df, "Language")
            segmentation["by_language"] = lang_stats

        # Campaign segmentation
        if "Campaign" in df.columns:
            campaign_stats = self._calculate_segment_stats(df, "Campaign")
            segmentation["by_campaign"] = campaign_stats

        # Agent segmentation
        if "User" in df.columns:
            agent_stats = self._calculate_segment_stats(df, "User")
            segmentation["by_agent"] = agent_stats

        # Call type segmentation
        if "call_type" in df.columns:
            type_stats = self._calculate_segment_stats(df, "call_type")
            segmentation["by_call_type"] = type_stats

        return segmentation

    def _calculate_segment_stats(
        self, df: pd.DataFrame, segment_column: str
    ) -> Dict[str, Any]:
        """Calculate statistics for a specific segment"""
        stats = {}

        for segment_value in df[segment_column].unique():
            if pd.isna(segment_value):
                continue

            segment_df = df[df[segment_column] == segment_value]

            segment_stats = {
                "call_count": len(segment_df),
                "percentage": (
                    float(len(segment_df) / len(df) * 100) if len(df) > 0 else 0
                ),
            }

            if "Length" in segment_df.columns:
                call_lengths = pd.to_numeric(
                    segment_df["Length"], errors="coerce"
                ).dropna()
                if not call_lengths.empty:
                    segment_stats.update(
                        {
                            "avg_duration": float(call_lengths.mean()),
                            "total_duration": float(call_lengths.sum()),
                            "std_duration": float(call_lengths.std()),
                            "min_duration": float(call_lengths.min()),
                            "max_duration": float(call_lengths.max()),
                        }
                    )

            stats[str(segment_value)] = segment_stats

        return stats

    def _analyze_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Perform statistical analysis including percentiles and outlier detection"""
        statistics = {}

        if "Length" in df.columns:
            call_lengths = pd.to_numeric(df["Length"], errors="coerce").dropna()

            if not call_lengths.empty:
                # Percentile analysis
                percentiles = [25, 50, 75, 90, 95, 99]
                percentile_values = {}
                for p in percentiles:
                    percentile_values[f"p{p}"] = float(np.percentile(call_lengths, p))

                statistics["percentiles"] = percentile_values

                # Outlier analysis using IQR method
                Q1 = np.percentile(call_lengths, 25)
                Q3 = np.percentile(call_lengths, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers = call_lengths[
                    (call_lengths < lower_bound) | (call_lengths > upper_bound)
                ]

                statistics["outliers"] = {
                    "count": len(outliers),
                    "percentage": float(len(outliers) / len(call_lengths) * 100),
                    "lower_bound": float(lower_bound),
                    "upper_bound": float(upper_bound),
                    "outlier_values": outliers.tolist()[
                        :50
                    ],  # Limit to first 50 outliers
                }

                # Distribution analysis
                statistics["distribution"] = {
                    "mean": float(call_lengths.mean()),
                    "median": float(call_lengths.median()),
                    "mode": (
                        float(call_lengths.mode().iloc[0])
                        if not call_lengths.mode().empty
                        else None
                    ),
                    "skewness": float(call_lengths.skew()),
                    "kurtosis": float(call_lengths.kurtosis()),
                }

        return statistics

    def _analyze_satisfaction(
        self, df: pd.DataFrame, params: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Analyze customer satisfaction if data is available"""
        satisfaction_field = params.get("satisfaction_field")

        if not satisfaction_field or satisfaction_field not in df.columns:
            # Try to find satisfaction-related columns
            satisfaction_columns = [
                col
                for col in df.columns
                if any(
                    keyword in col.lower()
                    for keyword in [
                        "satisfaction",
                        "rating",
                        "score",
                        "feedback",
                        "csat",
                    ]
                )
            ]

            if satisfaction_columns:
                satisfaction_field = satisfaction_columns[0]
            else:
                return None

        satisfaction_data = df[satisfaction_field].dropna()

        if satisfaction_data.empty:
            return None

        # Analyze satisfaction scores
        satisfaction_stats = {
            "field_name": satisfaction_field,
            "total_responses": len(satisfaction_data),
            "average_score": float(satisfaction_data.mean()),
            "median_score": float(satisfaction_data.median()),
            "std_score": float(satisfaction_data.std()),
            "min_score": float(satisfaction_data.min()),
            "max_score": float(satisfaction_data.max()),
        }

        # Score distribution
        score_distribution = satisfaction_data.value_counts().sort_index().to_dict()
        satisfaction_stats["score_distribution"] = {
            str(score): int(count) for score, count in score_distribution.items()
        }

        # Satisfaction by segments
        if "Language" in df.columns:
            lang_satisfaction = (
                df.groupby("Language")[satisfaction_field].mean().to_dict()
            )
            satisfaction_stats["by_language"] = {
                lang: float(score) for lang, score in lang_satisfaction.items()
            }

        if "Campaign" in df.columns:
            campaign_satisfaction = (
                df.groupby("Campaign")[satisfaction_field].mean().to_dict()
            )
            satisfaction_stats["by_campaign"] = {
                campaign: float(score)
                for campaign, score in campaign_satisfaction.items()
            }

        return satisfaction_stats

    def _create_core_metrics_charts(
        self, core_metrics: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Create charts for core metrics"""
        charts = []

        if "call_volume" in core_metrics:
            volume_data = core_metrics["call_volume"]
            charts.append(
                self._prepare_chart_data(
                    chart_type="pie",
                    title="Call Volume Distribution",
                    x_label="Call Type",
                    y_label="Count",
                    data={
                        "labels": ["Answered Calls", "Missed Calls"],
                        "values": [
                            volume_data["answered_calls"],
                            volume_data["missed_calls"],
                        ],
                    },
                )
            )

        return charts

    def _create_time_analysis_charts(
        self, time_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Create charts for time-based analysis"""
        charts = []

        if "hourly_volume" in time_analysis:
            hourly_data = time_analysis["hourly_volume"]
            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Call Volume by Hour",
                    x_label="Hour of Day",
                    y_label="Number of Calls",
                    data={
                        "x": list(hourly_data.keys()),
                        "y": list(hourly_data.values()),
                    },
                )
            )

        if "daily_volume" in time_analysis:
            daily_data = time_analysis["daily_volume"]
            # Order days properly
            day_order = [
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
                "Sunday",
            ]
            ordered_days = [day for day in day_order if day in daily_data]
            ordered_values = [daily_data[day] for day in ordered_days]

            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Call Volume by Day of Week",
                    x_label="Day of Week",
                    y_label="Number of Calls",
                    data={"x": ordered_days, "y": ordered_values},
                )
            )

        return charts

    def _create_segmentation_charts(
        self, segmentation: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Create charts for segmentation analysis"""
        charts = []

        if "by_language" in segmentation:
            lang_data = segmentation["by_language"]
            charts.append(
                self._prepare_chart_data(
                    chart_type="pie",
                    title="Call Distribution by Language",
                    x_label="Language",
                    y_label="Percentage",
                    data={
                        "labels": list(lang_data.keys()),
                        "values": [data["call_count"] for data in lang_data.values()],
                    },
                )
            )

        if "by_call_type" in segmentation:
            type_data = segmentation["by_call_type"]
            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Call Distribution by Type",
                    x_label="Call Type",
                    y_label="Number of Calls",
                    data={
                        "x": list(type_data.keys()),
                        "y": [data["call_count"] for data in type_data.values()],
                    },
                )
            )

        return charts

    def _create_statistical_charts(
        self, statistics: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Create charts for statistical analysis"""
        charts = []

        if "percentiles" in statistics:
            percentile_data = statistics["percentiles"]
            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Call Duration Percentiles",
                    x_label="Percentile",
                    y_label="Duration (seconds)",
                    data={
                        "x": list(percentile_data.keys()),
                        "y": list(percentile_data.values()),
                    },
                )
            )

        return charts

    def _create_satisfaction_charts(
        self, satisfaction: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Create charts for satisfaction analysis"""
        charts = []

        if "score_distribution" in satisfaction:
            score_data = satisfaction["score_distribution"]
            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Customer Satisfaction Score Distribution",
                    x_label="Satisfaction Score",
                    y_label="Number of Responses",
                    data={"x": list(score_data.keys()), "y": list(score_data.values())},
                )
            )

        if "by_language" in satisfaction:
            lang_satisfaction = satisfaction["by_language"]
            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Average Satisfaction by Language",
                    x_label="Language",
                    y_label="Average Satisfaction Score",
                    data={
                        "x": list(lang_satisfaction.keys()),
                        "y": list(lang_satisfaction.values()),
                    },
                )
            )

        return charts

    def _generate_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate executive summary of the analysis"""
        summary = {}

        if "core_metrics" in data:
            core = data["core_metrics"]
            summary["key_metrics"] = {
                "total_calls": core.get("call_volume", {}).get("total_calls", 0),
                "avg_call_duration": core.get("average_call_time", {}).get(
                    "formatted", "N/A"
                ),
                "total_talk_time": core.get("total_duration", {}).get(
                    "formatted", "N/A"
                ),
                "answer_rate": f"{core.get('call_volume', {}).get('answer_rate', 0) * 100:.1f}%",
            }

        if "segmentation" in data and "by_language" in data["segmentation"]:
            lang_data = data["segmentation"]["by_language"]
            summary["language_breakdown"] = {
                lang: f"{stats['percentage']:.1f}%" for lang, stats in lang_data.items()
            }

        return summary

    def _generate_recommendations(self, data: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on analysis"""
        recommendations = []

        # Analyze core metrics for recommendations
        if "core_metrics" in data:
            core = data["core_metrics"]
            answer_rate = core.get("call_volume", {}).get("answer_rate", 0)

            if answer_rate < 0.8:
                recommendations.append(
                    f"Call answer rate is {answer_rate*100:.1f}%. Consider increasing staffing "
                    "or optimizing call routing to improve customer experience."
                )

        # Analyze time patterns for recommendations
        if "time_analysis" in data and "hourly_volume" in data["time_analysis"]:
            hourly_data = data["time_analysis"]["hourly_volume"]
            peak_hour = max(hourly_data, key=hourly_data.get)
            recommendations.append(
                f"Peak call volume occurs at {peak_hour}:00. Consider scheduling more agents "
                "during this time to handle increased demand."
            )

        # Analyze language distribution for recommendations
        if "segmentation" in data and "by_language" in data["segmentation"]:
            lang_data = data["segmentation"]["by_language"]
            if "Arabic" in lang_data and "English" in lang_data:
                arabic_pct = lang_data["Arabic"]["percentage"]
                if arabic_pct > 30:
                    recommendations.append(
                        f"Arabic calls represent {arabic_pct:.1f}% of total volume. "
                        "Ensure adequate Arabic-speaking staff coverage."
                    )

        # Statistical analysis recommendations
        if "statistics" in data and "outliers" in data["statistics"]:
            outlier_pct = data["statistics"]["outliers"]["percentage"]
            if outlier_pct > 10:
                recommendations.append(
                    f"{outlier_pct:.1f}% of calls are outliers in duration. "
                    "Review these calls for quality assurance and training opportunities."
                )

        return recommendations
