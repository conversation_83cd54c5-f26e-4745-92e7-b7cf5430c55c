"""
Call volume prediction analyzer for call flow analytics.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any
import logging
from datetime import timedelta
from sklearn.linear_model import LinearRegression
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CallVolumePredictor(BaseAnalyzer):
    """Predictor for call volumes"""

    def predict(
        self, data: Dict[str, pd.DataFrame], granularity: str
    ) -> Dict[str, Any]:
        """
        Predict call volumes

        Args:
            data: Dictionary of DataFrames
            granularity: Time granularity for prediction (hourly, daily, weekly, monthly)

        Returns:
            Prediction result
        """
        result_data = {}
        charts = []

        # Combine all data for call volume analysis
        df_combined = pd.DataFrame()
        for key, df in data.items():
            if df is not None and not df.empty and "Call Date" in df.columns:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return self._format_result(
                {"error": "No data available for call volume prediction"}, []
            )

        # Ensure Call Date is datetime
        df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

        # Group by time period based on granularity
        if granularity == "hourly":
            df_combined["time_group"] = df_combined["Call Date"].dt.floor("H")
            group_format = "%Y-%m-%d %H:00"
            forecast_periods = 24  # Forecast next 24 hours
            time_unit = "hour"
        elif granularity == "daily":
            df_combined["time_group"] = df_combined["Call Date"].dt.floor("D")
            group_format = "%Y-%m-%d"
            forecast_periods = 7  # Forecast next 7 days
            time_unit = "day"
        elif granularity == "weekly":
            df_combined["time_group"] = (
                df_combined["Call Date"].dt.to_period("W").dt.start_time
            )
            group_format = "%Y-%m-%d"
            forecast_periods = 4  # Forecast next 4 weeks
            time_unit = "week"
        else:  # monthly
            df_combined["time_group"] = (
                df_combined["Call Date"].dt.to_period("M").dt.start_time
            )
            group_format = "%Y-%m"
            forecast_periods = 3  # Forecast next 3 months
            time_unit = "month"

        # Count calls by time period
        call_counts = (
            df_combined.groupby("time_group").size().reset_index(name="call_count")
        )
        call_counts["time_str"] = call_counts["time_group"].dt.strftime(group_format)

        # Prepare data for time series forecasting
        call_counts = call_counts.sort_values("time_group")
        call_counts["time_index"] = range(len(call_counts))

        # Simple linear regression for forecasting
        X = call_counts[["time_index"]].values
        y = call_counts["call_count"].values

        model = LinearRegression()
        model.fit(X, y)

        # Generate forecast periods
        last_time_index = call_counts["time_index"].max()
        forecast_indices = np.array(
            range(last_time_index + 1, last_time_index + 1 + forecast_periods)
        ).reshape(-1, 1)
        forecast_values = model.predict(forecast_indices)

        # Generate forecast dates
        last_date = call_counts["time_group"].max()
        if granularity == "hourly":
            forecast_dates = [
                last_date + timedelta(hours=i + 1) for i in range(forecast_periods)
            ]
        elif granularity == "daily":
            forecast_dates = [
                last_date + timedelta(days=i + 1) for i in range(forecast_periods)
            ]
        elif granularity == "weekly":
            forecast_dates = [
                last_date + timedelta(weeks=i + 1) for i in range(forecast_periods)
            ]
        else:  # monthly
            forecast_dates = [
                last_date.replace(
                    month=((last_date.month + i) % 12) or 12,
                    year=last_date.year + ((last_date.month + i - 1) // 12),
                )
                for i in range(1, forecast_periods + 1)
            ]

        # Format forecast dates
        forecast_date_strs = [d.strftime(group_format) for d in forecast_dates]

        # Prepare result data
        result_data["historical"] = call_counts[["time_str", "call_count"]].to_dict(
            orient="records"
        )
        result_data["forecast"] = [
            {"time_str": date_str, "call_count": max(0, int(round(value)))}
            for date_str, value in zip(forecast_date_strs, forecast_values)
        ]

        # Create chart for historical and forecast data
        charts.append(
            self._prepare_chart_data(
                chart_type="line",
                title=f"Call Volume by {granularity.capitalize()}",
                x_label=f"Time ({time_unit})",
                y_label="Number of Calls",
                data={
                    "x": call_counts["time_str"].tolist() + forecast_date_strs,
                    "y": call_counts["call_count"].tolist()
                    + [max(0, int(round(v))) for v in forecast_values],
                    "series": ["Historical"] * len(call_counts)
                    + ["Forecast"] * len(forecast_date_strs),
                },
            )
        )

        return self._format_result(result_data, charts)
