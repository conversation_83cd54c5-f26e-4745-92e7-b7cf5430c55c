"""
Customer journey analyzer for call flow analytics.
"""

import pandas as pd
from typing import Dict, List, Any, Optional
import logging
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CustomerJourneyAnalyzer(BaseAnalyzer):
    """Analyzer for customer journeys"""

    def analyze(
        self,
        data: Dict[str, pd.DataFrame],
        phone_numbers: Optional[List[str]] = None,
        max_interactions: int = 10,
    ) -> Dict[str, Any]:
        """
        Analyze customer journeys across multiple interactions

        Args:
            data: Dictionary of DataFrames
            phone_numbers: Optional list of phone numbers to analyze
            max_interactions: Maximum number of interactions to include

        Returns:
            Analysis result
        """
        result_data = {}
        charts = []

        # Combine all data for customer journey analysis
        df_combined = pd.DataFrame()
        for key, df in data.items():
            if df is not None and not df.empty and "Phone" in df.columns:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return self._format_result(
                {"error": "No data available for customer journey analysis"}, []
            )

        # Ensure Call Date is datetime
        if "Call Date" in df_combined.columns:
            df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

        # Filter by specific phone numbers if provided
        if phone_numbers and len(phone_numbers) > 0:
            df_combined = df_combined[df_combined["Phone"].isin(phone_numbers)]

            if df_combined.empty:
                return self._format_result(
                    {"error": "No data available for the specified phone numbers"}, []
                )

        # Group by phone number
        phone_groups = df_combined.groupby("Phone")

        # Calculate interaction counts
        interaction_counts = phone_groups.size().reset_index(name="interaction_count")
        interaction_counts = interaction_counts.sort_values(
            "interaction_count", ascending=False
        )

        # Calculate distribution of interaction counts
        interaction_distribution = (
            interaction_counts["interaction_count"].value_counts().reset_index()
        )
        interaction_distribution.columns = ["interactions", "customer_count"]
        interaction_distribution = interaction_distribution.sort_values("interactions")

        result_data["interaction_distribution"] = interaction_distribution.to_dict(
            orient="records"
        )

        # Create chart for interaction distribution
        charts.append(
            self._prepare_chart_data(
                chart_type="bar",
                title="Distribution of Customer Interactions",
                x_label="Number of Interactions",
                y_label="Number of Customers",
                data={
                    "x": interaction_distribution["interactions"].tolist(),
                    "y": interaction_distribution["customer_count"].tolist(),
                },
            )
        )

        # Analyze customer journeys for top customers or specified phone numbers
        if phone_numbers and len(phone_numbers) > 0:
            phones_to_analyze = phone_numbers
        else:
            # Take top customers by interaction count
            phones_to_analyze = interaction_counts.head(10)["Phone"].tolist()

        # Limit to max_interactions
        phones_to_analyze = phones_to_analyze[:max_interactions]

        # Analyze journeys for selected customers
        customer_journeys = []

        for phone in phones_to_analyze:
            customer_data = df_combined[df_combined["Phone"] == phone].copy()

            # Sort by call date
            if "Call Date" in customer_data.columns:
                customer_data = customer_data.sort_values("Call Date")

            # Extract journey information
            journey = {
                "phone": phone,
                "total_interactions": len(customer_data),
                "first_interaction": (
                    customer_data["Call Date"].min().strftime("%Y-%m-%d %H:%M:%S")
                    if "Call Date" in customer_data.columns
                    else None
                ),
                "last_interaction": (
                    customer_data["Call Date"].max().strftime("%Y-%m-%d %H:%M:%S")
                    if "Call Date" in customer_data.columns
                    else None
                ),
                "interactions": [],
            }

            # Add each interaction
            for _, row in customer_data.iterrows():
                interaction = {
                    "call_type": row.get("Call Type", "Unknown"),
                    "date": (
                        row["Call Date"].strftime("%Y-%m-%d %H:%M:%S")
                        if "Call Date" in row
                        else None
                    ),
                    "campaign": row.get("Campaign", "Unknown"),
                    "status": row.get("Status", "Unknown"),
                    "user": row.get("User", "Unknown"),
                    "length": (
                        int(row["Length"])
                        if "Length" in row and pd.notna(row["Length"])
                        else None
                    ),
                }
                journey["interactions"].append(interaction)

            customer_journeys.append(journey)

        result_data["customer_journeys"] = customer_journeys

        # Calculate time between interactions
        if len(customer_journeys) > 0 and "Call Date" in df_combined.columns:
            time_between_interactions = []

            for phone in phones_to_analyze:
                customer_data = df_combined[df_combined["Phone"] == phone].copy()

                if len(customer_data) > 1:
                    # Sort by call date
                    customer_data = customer_data.sort_values("Call Date")

                    # Calculate time differences
                    customer_data["Next Call Date"] = customer_data["Call Date"].shift(
                        -1
                    )
                    customer_data["Time to Next Call"] = (
                        customer_data["Next Call Date"] - customer_data["Call Date"]
                    ).dt.total_seconds() / 3600  # in hours

                    # Add to results
                    for _, row in customer_data.dropna(
                        subset=["Time to Next Call"]
                    ).iterrows():
                        time_between_interactions.append(
                            {
                                "phone": phone,
                                "from_date": row["Call Date"].strftime(
                                    "%Y-%m-%d %H:%M:%S"
                                ),
                                "to_date": row["Next Call Date"].strftime(
                                    "%Y-%m-%d %H:%M:%S"
                                ),
                                "hours_between": row["Time to Next Call"],
                            }
                        )

            if time_between_interactions:
                result_data["time_between_interactions"] = time_between_interactions

                # Calculate average time between interactions
                hours_between = [
                    item["hours_between"] for item in time_between_interactions
                ]
                avg_hours_between = (
                    sum(hours_between) / len(hours_between) if hours_between else 0
                )

                result_data["avg_hours_between_interactions"] = avg_hours_between

                # Create histogram of time between interactions
                time_bins = [0, 1, 6, 24, 72, 168, 720]  # in hours
                time_labels = ["<1h", "1-6h", "6-24h", "1-3d", "3-7d", ">7d"]

                time_hist = (
                    pd.cut(
                        pd.Series(hours_between),
                        bins=time_bins,
                        labels=time_labels,
                        include_lowest=True,
                    )
                    .value_counts()
                    .reset_index()
                )

                time_hist.columns = ["time_range", "count"]
                time_hist = time_hist.sort_values("time_range")

                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Time Between Customer Interactions",
                        x_label="Time Range",
                        y_label="Number of Interactions",
                        data={
                            "x": time_hist["time_range"].tolist(),
                            "y": time_hist["count"].tolist(),
                        },
                    )
                )

        # Analyze common paths
        if "Campaign" in df_combined.columns:
            # Get sequences of campaigns for each customer
            campaign_sequences = []

            for phone, group in phone_groups:
                if len(group) > 1:
                    # Sort by call date
                    if "Call Date" in group.columns:
                        group = group.sort_values("Call Date")

                    # Get campaign sequence
                    sequence = group["Campaign"].tolist()

                    # Only include sequences with at least 2 campaigns
                    if len(sequence) >= 2:
                        campaign_sequences.append("->".join(sequence))

            # Count common sequences
            if campaign_sequences:
                sequence_counts = (
                    pd.Series(campaign_sequences).value_counts().head(10).reset_index()
                )
                sequence_counts.columns = ["sequence", "count"]

                result_data["common_campaign_sequences"] = sequence_counts.to_dict(
                    orient="records"
                )

                # Create chart for common sequences
                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Common Campaign Sequences",
                        x_label="Campaign Sequence",
                        y_label="Frequency",
                        data={
                            "x": sequence_counts["sequence"].tolist(),
                            "y": sequence_counts["count"].tolist(),
                        },
                    )
                )

        return self._format_result(result_data, charts)
