"""
Analyzer classes for call flow analytics.
This package contains all the analyzer classes used for analyzing call flow data.
"""

# Import all analyzer classes to make them available from the package
from app.api.services.analyzers.base import BaseAnalyzer
from app.api.services.analyzers.agent import (
    AgentPerformanceAnalyzer,
    AgentSpecializationAnalyzer,
)
from app.api.services.analyzers.call_volume import CallVolumePredictor
from app.api.services.analyzers.staffing import StaffingOptimizer
from app.api.services.analyzers.campaign import CampaignAnalyzer
from app.api.services.analyzers.language import LanguageAnalyzer
from app.api.services.analyzers.missed_call import MissedCallAnalyzer
from app.api.services.analyzers.enhanced_missed_call import EnhancedMissedCallAnalyzer
from app.api.services.analyzers.enhanced_forecaster import EnhancedTimeSeriesForecaster
from app.api.services.analyzers.customer_journey import CustomerJourneyAnalyzer
from app.api.services.analyzers.fcr import FCRAnalyzer
from app.api.services.analyzers.sentiment import SentimentAnalyzer
from app.api.services.analyzers.conversion import ConversionRateOptimizer
from app.api.services.analyzers.queue import QueueOptimizer
from app.api.services.analyzers.seasonal import SeasonalTrendAnalyzer
from app.api.services.analyzers.geographic import GeographicAnalyzer
from app.api.services.analyzers.call_quality import CallQualityScorer
from app.api.services.analyzers.ab_testing import ABTestingAnalyzer
from app.api.services.analyzers.churn import ChurnPredictor
from app.api.services.analyzers.cross_selling import CrossSellingDetector
from app.api.services.analyzers.anomaly import AnomalyDetector
from app.api.services.analyzers.callback import CallbackOptimizer
from app.api.services.analyzers.knowledge_base import KnowledgeBaseEnhancer
from app.api.services.analyzers.call_analytics import CallAnalyticsRecommender
from app.api.services.analyzers.comprehensive_analytics import ComprehensiveAnalyzer

# Export all classes
__all__ = [
    "BaseAnalyzer",
    "AgentPerformanceAnalyzer",
    "AgentSpecializationAnalyzer",
    "CallVolumePredictor",
    "StaffingOptimizer",
    "CampaignAnalyzer",
    "LanguageAnalyzer",
    "MissedCallAnalyzer",
    "EnhancedMissedCallAnalyzer",
    "EnhancedTimeSeriesForecaster",
    "CustomerJourneyAnalyzer",
    "FCRAnalyzer",
    "SentimentAnalyzer",
    "ConversionRateOptimizer",
    "QueueOptimizer",
    "SeasonalTrendAnalyzer",
    "GeographicAnalyzer",
    "CallQualityScorer",
    "ABTestingAnalyzer",
    "ChurnPredictor",
    "CrossSellingDetector",
    "AnomalyDetector",
    "CallbackOptimizer",
    "KnowledgeBaseEnhancer",
    "CallAnalyticsRecommender",
    "ComprehensiveAnalyzer",
]
