"""
Base analyzer class for call flow analytics.
"""
import logging
from typing import Dict, List, Any
from app.api.models.schemas import ChartData
from app.shared.utils import clean_nan_for_json

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BaseAnalyzer:
    """Base class for all analyzers"""

    def __init__(self):
        """Initialize the analyzer"""
        pass

    def _prepare_chart_data(
        self,
        chart_type: str,
        title: str,
        x_label: str,
        y_label: str,
        data: Dict[str, Any],
    ) -> ChartData:
        """
        Prepare chart data in a standardized format

        Args:
            chart_type: Type of chart (bar, line, pie, etc.)
            title: Chart title
            x_label: X-axis label
            y_label: Y-axis label
            data: Chart data

        Returns:
            ChartData object with NaN values cleaned
        """
        # Clean NaN values from chart data
        cleaned_data = clean_nan_for_json(data)

        return {
            "chart_type": chart_type,
            "title": title,
            "x_label": x_label,
            "y_label": y_label,
            "data": cleaned_data,
        }

    def _format_result(
        self, data: Dict[str, Any], charts: List[ChartData]
    ) -> Dict[str, Any]:
        """
        Format analysis result

        Args:
            data: Analysis data
            charts: List of chart data

        Returns:
            Formatted result with NaN values cleaned
        """
        # Clean NaN values from data and charts
        cleaned_data = clean_nan_for_json(data)
        cleaned_charts = clean_nan_for_json(charts)

        return {"data": cleaned_data, "charts": cleaned_charts}
