"""
First Call Resolution (FCR) analyzer for call flow analytics.
"""

import pandas as pd
from typing import Dict, Any
import logging
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FCRAnalyzer(BaseAnalyzer):
    """Analyzer for First Call Resolution (FCR)"""

    def analyze(
        self, data: Dict[str, pd.DataFrame], resolution_window: int = 7
    ) -> Dict[str, Any]:
        """
        Analyze First Call Resolution (FCR) rates

        Args:
            data: Dictionary of DataFrames
            resolution_window: Number of days to consider for resolution window

        Returns:
            Analysis result
        """
        result_data = {}
        charts = []

        # Combine all data for FCR analysis
        df_combined = pd.DataFrame()
        for _, df in data.items():
            if not df.empty and "Phone" in df.columns and "Call Date" in df.columns:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return self._format_result(
                {"error": "No data available for FCR analysis"}, []
            )

        # Ensure Call Date is datetime
        df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

        # Add date components
        df_combined["Date"] = df_combined["Call Date"].dt.date
        df_combined["Hour"] = df_combined["Call Date"].dt.hour
        df_combined["Weekday"] = df_combined["Call Date"].dt.dayofweek
        df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()
        df_combined["Month"] = df_combined["Call Date"].dt.month

        # Group by phone number and sort by date
        phone_groups = df_combined.groupby("Phone")

        # Calculate FCR metrics
        fcr_data = []
        repeat_calls = []

        for phone, group in phone_groups:
            # Sort by call date
            group = group.sort_values("Call Date")

            if len(group) > 1:
                # Calculate time differences between calls
                group["Next Call Date"] = group["Call Date"].shift(-1)
                group["Days to Next Call"] = (
                    group["Next Call Date"] - group["Call Date"]
                ).dt.total_seconds() / (24 * 3600)

                # Identify calls that required follow-up within the resolution window
                group["Required Followup"] = (
                    group["Days to Next Call"] <= resolution_window
                ) & (group["Days to Next Call"] > 0)

                # Add to FCR data
                for i, row in group.iterrows():
                    if pd.notna(row.get("Days to Next Call")):
                        call_data = {
                            "phone": phone,
                            "call_date": row["Call Date"].strftime("%Y-%m-%d %H:%M:%S"),
                            "campaign": row.get("Campaign", "Unknown"),
                            "status": row.get("Status", "Unknown"),
                            "user": row.get("User", "Unknown"),
                            "required_followup": bool(row["Required Followup"]),
                            "days_to_next_call": (
                                row["Days to Next Call"]
                                if pd.notna(row["Days to Next Call"])
                                else None
                            ),
                        }
                        fcr_data.append(call_data)

                        # Add to repeat calls if it required follow-up
                        if bool(row["Required Followup"]):
                            repeat_calls.append(
                                {
                                    "phone": phone,
                                    "first_call_date": row["Call Date"].strftime(
                                        "%Y-%m-%d %H:%M:%S"
                                    ),
                                    "next_call_date": (
                                        row["Next Call Date"].strftime(
                                            "%Y-%m-%d %H:%M:%S"
                                        )
                                        if pd.notna(row["Next Call Date"])
                                        else None
                                    ),
                                    "days_between": row["Days to Next Call"],
                                    "first_call_campaign": row.get(
                                        "Campaign", "Unknown"
                                    ),
                                    "first_call_user": row.get("User", "Unknown"),
                                }
                            )

        # Calculate FCR rate
        if fcr_data:
            total_calls = len(fcr_data)
            resolved_first_call = sum(
                1 for call in fcr_data if not call["required_followup"]
            )
            fcr_rate = resolved_first_call / total_calls if total_calls > 0 else 0

            result_data["fcr_metrics"] = {
                "total_calls": total_calls,
                "resolved_first_call": resolved_first_call,
                "fcr_rate": fcr_rate,
                "resolution_window_days": resolution_window,
            }

            # Create chart for FCR rate
            charts.append(
                self._prepare_chart_data(
                    chart_type="pie",
                    title="First Call Resolution Rate",
                    x_label="",
                    y_label="",
                    data={
                        "labels": ["Resolved on First Call", "Required Follow-up"],
                        "values": [
                            resolved_first_call,
                            total_calls - resolved_first_call,
                        ],
                    },
                )
            )

        # FCR by campaign
        if fcr_data and "Campaign" in df_combined.columns:
            campaign_fcr = {}

            for call in fcr_data:
                campaign = call["campaign"]
                if campaign not in campaign_fcr:
                    campaign_fcr[campaign] = {"total": 0, "resolved": 0}

                campaign_fcr[campaign]["total"] += 1
                if not call["required_followup"]:
                    campaign_fcr[campaign]["resolved"] += 1

            # Calculate FCR rate by campaign
            campaign_fcr_rates = []
            for campaign, counts in campaign_fcr.items():
                if counts["total"] >= 5:  # Only include campaigns with sufficient data
                    campaign_fcr_rates.append(
                        {
                            "campaign": campaign,
                            "total_calls": counts["total"],
                            "resolved_first_call": counts["resolved"],
                            "fcr_rate": (
                                counts["resolved"] / counts["total"]
                                if counts["total"] > 0
                                else 0
                            ),
                        }
                    )

            # Sort by FCR rate
            campaign_fcr_rates = sorted(
                campaign_fcr_rates, key=lambda x: x["fcr_rate"], reverse=True
            )
            result_data["campaign_fcr"] = campaign_fcr_rates

            # Create chart for FCR by campaign
            if campaign_fcr_rates:
                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="FCR Rate by Campaign",
                        x_label="Campaign",
                        y_label="FCR Rate",
                        data={
                            "x": [item["campaign"] for item in campaign_fcr_rates],
                            "y": [item["fcr_rate"] for item in campaign_fcr_rates],
                        },
                    )
                )

        # FCR by agent
        if fcr_data and "User" in df_combined.columns:
            agent_fcr = {}

            for call in fcr_data:
                agent = call["user"]
                if agent not in agent_fcr:
                    agent_fcr[agent] = {"total": 0, "resolved": 0}

                agent_fcr[agent]["total"] += 1
                if not call["required_followup"]:
                    agent_fcr[agent]["resolved"] += 1

            # Calculate FCR rate by agent
            agent_fcr_rates = []
            for agent, counts in agent_fcr.items():
                if counts["total"] >= 5:  # Only include agents with sufficient data
                    agent_fcr_rates.append(
                        {
                            "agent": agent,
                            "total_calls": counts["total"],
                            "resolved_first_call": counts["resolved"],
                            "fcr_rate": (
                                counts["resolved"] / counts["total"]
                                if counts["total"] > 0
                                else 0
                            ),
                        }
                    )

            # Sort by FCR rate
            agent_fcr_rates = sorted(
                agent_fcr_rates, key=lambda x: x["fcr_rate"], reverse=True
            )
            result_data["agent_fcr"] = agent_fcr_rates

            # Create chart for FCR by agent
            if agent_fcr_rates:
                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="FCR Rate by Agent",
                        x_label="Agent",
                        y_label="FCR Rate",
                        data={
                            "x": [item["agent"] for item in agent_fcr_rates],
                            "y": [item["fcr_rate"] for item in agent_fcr_rates],
                        },
                    )
                )

        # Time to follow-up distribution
        if repeat_calls:
            days_to_followup = [
                call["days_between"]
                for call in repeat_calls
                if call["days_between"] is not None
            ]

            if days_to_followup:
                # Calculate statistics
                avg_days = sum(days_to_followup) / len(days_to_followup)
                result_data["followup_metrics"] = {
                    "total_followups": len(days_to_followup),
                    "avg_days_to_followup": avg_days,
                }

                # Create histogram of follow-up times
                day_bins = [0, 1, 2, 3, 5, 7, 14, 30]
                day_labels = [
                    "Same day",
                    "1 day",
                    "2 days",
                    "3-4 days",
                    "5-6 days",
                    "1-2 weeks",
                    "2-4 weeks",
                ]

                day_hist = (
                    pd.cut(
                        pd.Series(days_to_followup),
                        bins=day_bins,
                        labels=day_labels,
                        include_lowest=True,
                    )
                    .value_counts()
                    .reset_index()
                )

                day_hist.columns = ["time_range", "count"]
                day_hist = day_hist.sort_values("time_range")

                result_data["followup_distribution"] = day_hist.to_dict(
                    orient="records"
                )

                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Time to Follow-up Distribution",
                        x_label="Time Range",
                        y_label="Number of Follow-ups",
                        data={
                            "x": day_hist["time_range"].tolist(),
                            "y": day_hist["count"].tolist(),
                        },
                    )
                )

        return self._format_result(result_data, charts)
