"""
Advanced forecasting methods for time series data.
This module implements Prophet, XGBoost, Random Forest, and LSTM forecasting methods.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
import warnings

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress warnings
warnings.filterwarnings("ignore")


def forecast_with_prophet(
    time_series: pd.Series, forecast_periods: int, granularity: str
) -> Tuple[np.ndarray, str]:
    """
    Forecast time series using Facebook Prophet

    Args:
        time_series: Time series data
        forecast_periods: Number of periods to forecast
        granularity: Time granularity (hourly, daily, weekly)

    Returns:
        Tuple of (forecast values, method used)
    """
    try:
        # Import Prophet here to avoid dependency issues if not installed
        from prophet import Prophet
        
        # Convert time series to DataFrame for Prophet
        df = pd.DataFrame({"ds": time_series.index, "y": time_series.values})
        
        # Set appropriate frequency
        if granularity == "hourly":
            freq = "H"
        elif granularity == "weekly":
            freq = "W"
        else:  # daily is default
            freq = "D"
            
        # Create and fit model
        model = Prophet(
            daily_seasonality=(granularity == "hourly"),
            weekly_seasonality=(granularity != "weekly"),
            yearly_seasonality=True,
            seasonality_mode="multiplicative"
        )
        
        # Add additional seasonality for hourly data
        if granularity == "hourly":
            model.add_seasonality(name="hourly", period=24, fourier_order=5)
            
        # Fit the model
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            model.fit(df)
            
        # Create future dataframe
        future = model.make_future_dataframe(periods=forecast_periods, freq=freq)
        
        # Make forecast
        forecast = model.predict(future)
        
        # Extract forecast values
        forecast_values = forecast.tail(forecast_periods)["yhat"].values
        
        # Ensure non-negative values
        forecast_values = np.maximum(0, forecast_values)
        
        return forecast_values, "prophet"
        
    except Exception as e:
        logger.error(f"Prophet forecasting error: {str(e)}")
        raise


def forecast_with_xgboost(
    time_series: pd.Series, forecast_periods: int, granularity: str
) -> Tuple[np.ndarray, str]:
    """
    Forecast time series using XGBoost

    Args:
        time_series: Time series data
        forecast_periods: Number of periods to forecast
        granularity: Time granularity (hourly, daily, weekly)

    Returns:
        Tuple of (forecast values, method used)
    """
    try:
        import xgboost as xgb
        
        # Create features from the time series
        df = create_time_features(time_series, granularity)
        
        # Define features and target
        X = df.drop("y", axis=1)
        y = df["y"]
        
        # Train XGBoost model
        model = xgb.XGBRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=3,
            random_state=42
        )
        model.fit(X, y)
        
        # Create future features
        future_df = create_future_features(time_series, forecast_periods, granularity)
        
        # Make predictions
        forecast_values = model.predict(future_df)
        
        # Ensure non-negative values
        forecast_values = np.maximum(0, forecast_values)
        
        return forecast_values, "xgboost"
        
    except Exception as e:
        logger.error(f"XGBoost forecasting error: {str(e)}")
        raise


def forecast_with_random_forest(
    time_series: pd.Series, forecast_periods: int, granularity: str
) -> Tuple[np.ndarray, str]:
    """
    Forecast time series using Random Forest

    Args:
        time_series: Time series data
        forecast_periods: Number of periods to forecast
        granularity: Time granularity (hourly, daily, weekly)

    Returns:
        Tuple of (forecast values, method used)
    """
    try:
        from sklearn.ensemble import RandomForestRegressor
        
        # Create features from the time series
        df = create_time_features(time_series, granularity)
        
        # Define features and target
        X = df.drop("y", axis=1)
        y = df["y"]
        
        # Train Random Forest model
        model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42
        )
        model.fit(X, y)
        
        # Create future features
        future_df = create_future_features(time_series, forecast_periods, granularity)
        
        # Make predictions
        forecast_values = model.predict(future_df)
        
        # Ensure non-negative values
        forecast_values = np.maximum(0, forecast_values)
        
        return forecast_values, "random_forest"
        
    except Exception as e:
        logger.error(f"Random Forest forecasting error: {str(e)}")
        raise


def forecast_with_lstm(
    time_series: pd.Series, forecast_periods: int, granularity: str
) -> Tuple[np.ndarray, str]:
    """
    Forecast time series using LSTM (Long Short-Term Memory)

    Args:
        time_series: Time series data
        forecast_periods: Number of periods to forecast
        granularity: Time granularity (hourly, daily, weekly)

    Returns:
        Tuple of (forecast values, method used)
    """
    try:
        # Import TensorFlow and Keras here to avoid dependency issues if not installed
        import tensorflow as tf
        from tensorflow.keras.models import Sequential
        from tensorflow.keras.layers import LSTM, Dense
        from tensorflow.keras.optimizers import Adam
        from sklearn.preprocessing import MinMaxScaler
        
        # Set random seeds for reproducibility
        tf.random.set_seed(42)
        np.random.seed(42)
        
        # Scale the data
        scaler = MinMaxScaler(feature_range=(0, 1))
        scaled_data = scaler.fit_transform(time_series.values.reshape(-1, 1))
        
        # Create sequences for LSTM
        X, y = create_sequences(scaled_data, seq_length=12)
        
        # Build LSTM model
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(X.shape[1], 1)),
            LSTM(50),
            Dense(1)
        ])
        
        # Compile model
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
        
        # Train model
        model.fit(X, y, epochs=50, batch_size=32, verbose=0)
        
        # Generate forecast
        forecast_values = []
        current_batch = scaled_data[-12:].reshape(1, 12, 1)
        
        for i in range(forecast_periods):
            # Get prediction
            current_pred = model.predict(current_batch, verbose=0)[0][0]
            forecast_values.append(current_pred)
            
            # Update batch for next prediction
            current_batch = np.append(current_batch[:, 1:, :], 
                                     [[current_pred]], 
                                     axis=1)
        
        # Inverse transform to get original scale
        forecast_values = scaler.inverse_transform(
            np.array(forecast_values).reshape(-1, 1)
        ).flatten()
        
        # Ensure non-negative values
        forecast_values = np.maximum(0, forecast_values)
        
        return forecast_values, "lstm"
        
    except Exception as e:
        logger.error(f"LSTM forecasting error: {str(e)}")
        raise


def create_time_features(time_series: pd.Series, granularity: str) -> pd.DataFrame:
    """
    Create time-based features for machine learning models

    Args:
        time_series: Time series data
        granularity: Time granularity (hourly, daily, weekly)

    Returns:
        DataFrame with features
    """
    # Create DataFrame
    df = pd.DataFrame({"y": time_series.values}, index=time_series.index)
    
    # Add lag features
    for i in range(1, 8):
        df[f"lag_{i}"] = df["y"].shift(i)
    
    # Add rolling window features
    df["rolling_mean_7"] = df["y"].rolling(window=7, min_periods=1).mean()
    df["rolling_std_7"] = df["y"].rolling(window=7, min_periods=1).std()
    
    # Add time-based features
    df["dayofweek"] = df.index.dayofweek
    df["month"] = df.index.month
    df["year"] = df.index.year
    
    if granularity == "hourly":
        df["hour"] = df.index.hour
        df["is_business_hour"] = ((df["hour"] >= 9) & (df["hour"] <= 17)).astype(int)
    
    # Add cyclical features for time variables
    df["dayofweek_sin"] = np.sin(2 * np.pi * df["dayofweek"] / 7)
    df["dayofweek_cos"] = np.cos(2 * np.pi * df["dayofweek"] / 7)
    df["month_sin"] = np.sin(2 * np.pi * df["month"] / 12)
    df["month_cos"] = np.cos(2 * np.pi * df["month"] / 12)
    
    if granularity == "hourly":
        df["hour_sin"] = np.sin(2 * np.pi * df["hour"] / 24)
        df["hour_cos"] = np.cos(2 * np.pi * df["hour"] / 24)
    
    # Drop NaN values
    df = df.dropna()
    
    return df


def create_future_features(
    time_series: pd.Series, forecast_periods: int, granularity: str
) -> pd.DataFrame:
    """
    Create features for future periods

    Args:
        time_series: Historical time series data
        forecast_periods: Number of periods to forecast
        granularity: Time granularity (hourly, daily, weekly)

    Returns:
        DataFrame with features for future periods
    """
    # Get the last date in the time series
    last_date = time_series.index[-1]
    
    # Generate future dates
    if granularity == "hourly":
        future_dates = [last_date + timedelta(hours=i+1) for i in range(forecast_periods)]
    elif granularity == "weekly":
        future_dates = [last_date + timedelta(weeks=i+1) for i in range(forecast_periods)]
    else:  # daily is default
        future_dates = [last_date + timedelta(days=i+1) for i in range(forecast_periods)]
    
    # Create empty DataFrame with future dates
    future_df = pd.DataFrame(index=future_dates)
    
    # Get the last values from the historical data for lag features
    historical_values = time_series.values[-7:]  # Get last 7 values
    
    # Add lag features
    for i in range(1, 8):
        if i <= len(historical_values):
            future_df[f"lag_{i}"] = np.roll(historical_values, i)[-1]
        else:
            future_df[f"lag_{i}"] = np.nan
    
    # Add rolling window features from historical data
    future_df["rolling_mean_7"] = time_series.rolling(window=7, min_periods=1).mean()[-1]
    future_df["rolling_std_7"] = time_series.rolling(window=7, min_periods=1).std()[-1]
    
    # Add time-based features
    future_df["dayofweek"] = future_df.index.dayofweek
    future_df["month"] = future_df.index.month
    future_df["year"] = future_df.index.year
    
    if granularity == "hourly":
        future_df["hour"] = future_df.index.hour
        future_df["is_business_hour"] = ((future_df["hour"] >= 9) & (future_df["hour"] <= 17)).astype(int)
    
    # Add cyclical features for time variables
    future_df["dayofweek_sin"] = np.sin(2 * np.pi * future_df["dayofweek"] / 7)
    future_df["dayofweek_cos"] = np.cos(2 * np.pi * future_df["dayofweek"] / 7)
    future_df["month_sin"] = np.sin(2 * np.pi * future_df["month"] / 12)
    future_df["month_cos"] = np.cos(2 * np.pi * future_df["month"] / 12)
    
    if granularity == "hourly":
        future_df["hour_sin"] = np.sin(2 * np.pi * future_df["hour"] / 24)
        future_df["hour_cos"] = np.cos(2 * np.pi * future_df["hour"] / 24)
    
    return future_df


def create_sequences(data: np.ndarray, seq_length: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    Create sequences for LSTM model

    Args:
        data: Input data
        seq_length: Sequence length

    Returns:
        Tuple of (X, y) for LSTM training
    """
    X, y = [], []
    for i in range(len(data) - seq_length):
        X.append(data[i:i + seq_length])
        y.append(data[i + seq_length])
    return np.array(X), np.array(y)
