"""
Language analyzer for call flow analytics.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any
import logging
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LanguageAnalyzer(BaseAnalyzer):
    """Analyzer for language-specific performance"""

    def analyze(
        self, data: Dict[str, pd.DataFrame], languages: List[str]
    ) -> Dict[str, Any]:
        """
        Analyze performance across languages

        Args:
            data: Dictionary of DataFrames
            languages: List of languages to analyze

        Returns:
            Analysis result
        """
        result_data = {}
        charts = []

        # Combine all data for language analysis
        df_combined = pd.DataFrame()
        for key, df in data.items():
            if not df.empty:
                # If Language column doesn't exist, try to detect from Campaign
                if "Language" not in df.columns and "Campaign" in df.columns:
                    df["Language"] = df["Campaign"].apply(
                        lambda x: "Arabic" if "Arabic" in str(x) else "English"
                    )

                if "Language" in df.columns:
                    df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return self._format_result(
                {"error": "No data available for language analysis"}, []
            )

        # Filter by specified languages if provided
        if languages and len(languages) > 0 and "All" not in languages:
            df_combined = df_combined[df_combined["Language"].isin(languages)]

            if df_combined.empty:
                return self._format_result(
                    {
                        "error": f"No data available for specified languages: {', '.join(languages)}"
                    },
                    [],
                )

        # Ensure Call Date is datetime if it exists
        if "Call Date" in df_combined.columns:
            df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])
            df_combined["Hour"] = df_combined["Call Date"].dt.hour
            df_combined["Weekday"] = df_combined["Call Date"].dt.dayofweek
            df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()
            df_combined["Date"] = df_combined["Call Date"].dt.date

        # Group by language
        language_groups = df_combined.groupby("Language")

        # Calculate metrics by language
        language_metrics = []

        for language, group in language_groups:
            language_data = {"Language": language}

            # Call volume
            language_data["call_volume"] = len(group)

            # Call length
            if "Length" in group.columns:
                language_data["avg_call_length"] = group["Length"].mean()

            # Resolution rate (approximated by Status)
            if "Status" in group.columns:
                # Assuming certain statuses indicate resolution
                resolved_statuses = ["ANSWERED", "COMPLETED", "RESOLVED"]
                resolved_calls = group[group["Status"].isin(resolved_statuses)].shape[0]
                language_data["resolution_rate"] = (
                    resolved_calls / len(group) if len(group) > 0 else 0
                )
                language_data["resolved_calls"] = resolved_calls
                language_data["total_calls"] = len(group)

            # Top campaigns
            if "Campaign" in group.columns:
                campaign_counts = group["Campaign"].value_counts().head(5).to_dict()
                language_data["top_campaigns"] = campaign_counts

            # Top agents
            if "User" in group.columns:
                agent_counts = group["User"].value_counts().head(5).to_dict()
                language_data["top_agents"] = agent_counts

            # Add to results
            language_metrics.append(language_data)

        # Prepare result data
        result_data["language_metrics"] = language_metrics

        # Create charts

        # Call volume by language
        language_volumes = pd.DataFrame(language_metrics)

        charts.append(
            self._prepare_chart_data(
                chart_type="pie",
                title="Call Volume by Language",
                x_label="",
                y_label="",
                data={
                    "labels": language_volumes["Language"].tolist(),
                    "values": language_volumes["call_volume"].tolist(),
                },
            )
        )

        # Average call length by language
        if "avg_call_length" in language_volumes.columns:
            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Average Call Length by Language",
                    x_label="Language",
                    y_label="Average Length (seconds)",
                    data={
                        "x": language_volumes["Language"].tolist(),
                        "y": language_volumes["avg_call_length"].tolist(),
                    },
                )
            )

        # Resolution rate by language
        if "resolution_rate" in language_volumes.columns:
            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Resolution Rate by Language",
                    x_label="Language",
                    y_label="Resolution Rate",
                    data={
                        "x": language_volumes["Language"].tolist(),
                        "y": language_volumes["resolution_rate"].tolist(),
                    },
                )
            )

        # Call volume by language and hour of day
        if "Hour" in df_combined.columns:
            language_hour = (
                df_combined.groupby(["Language", "Hour"])
                .size()
                .reset_index(name="call_count")
            )

            # Create line chart for hourly distribution by language
            hour_data = {}
            for lang in language_hour["Language"].unique():
                lang_data = language_hour[language_hour["Language"] == lang]
                hour_data[lang] = {
                    "x": lang_data["Hour"].tolist(),
                    "y": lang_data["call_count"].tolist(),
                }

            charts.append(
                self._prepare_chart_data(
                    chart_type="line",
                    title="Call Volume by Hour and Language",
                    x_label="Hour of Day",
                    y_label="Number of Calls",
                    data={"series": list(hour_data.keys()), "data": hour_data},
                )
            )

        # Call volume by language and day of week
        if "WeekdayName" in df_combined.columns:
            language_weekday = (
                df_combined.groupby(["Language", "WeekdayName"])
                .size()
                .reset_index(name="call_count")
            )

            # Pivot for better visualization
            weekday_pivot = language_weekday.pivot(
                index="Language", columns="WeekdayName", values="call_count"
            )

            # Fill NaN values with 0
            weekday_pivot = weekday_pivot.fillna(0)

            # Reorder columns for days of week
            weekday_order = [
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
                "Sunday",
            ]
            weekday_pivot = weekday_pivot.reindex(columns=weekday_order, fill_value=0)

            # Create heatmap chart
            charts.append(
                self._prepare_chart_data(
                    chart_type="heatmap",
                    title="Call Volume by Language and Day of Week",
                    x_label="Day of Week",
                    y_label="Language",
                    data={
                        "x": weekday_pivot.columns.tolist(),
                        "y": weekday_pivot.index.tolist(),
                        "z": weekday_pivot.values.tolist(),
                    },
                )
            )

        return self._format_result(result_data, charts)
