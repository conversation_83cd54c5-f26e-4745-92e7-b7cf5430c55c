"""
Agent scheduling model using Erlang C formula for call flow analytics.
This module implements a more sophisticated staffing model than the basic StaffingOptimizer.
"""

import pandas as pd
import numpy as np
import math
from typing import Dict, Any, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgentSchedulingModel(BaseAnalyzer):
    """
    Agent scheduling model using Erlang C formula for call center staffing optimization.
    This model takes forecasted call volumes and calculates optimal staffing levels.
    """

    def schedule(
        self,
        data: Dict[str, pd.DataFrame],
        target_service_level: float = 0.8,
        max_wait_time: int = 20,
        by_language: bool = True,
        by_campaign: bool = False,
        use_forecast: bool = True,
        forecast_days: int = 7,
        shift_length: int = 8,
        shifts_per_day: int = 3,
        highlight_arabic: bool = True,
        include_hourly_breakdown: bool = True,
        include_daily_summary: bool = True,
        include_shift_recommendations: bool = True,
    ) -> Dict[str, Any]:
        """
        Schedule agents based on call volume forecasts using Erlang C model

        Args:
            data: Dictionary of DataFrames
            target_service_level: Target service level (e.g., 0.8 for 80%)
            max_wait_time: Maximum acceptable wait time in seconds
            by_language: Whether to break down staffing by language
            by_campaign: Whether to break down staffing by campaign
            use_forecast: Whether to use forecasted data for future scheduling
            forecast_days: Number of days to forecast if use_forecast is True
            shift_length: Length of a standard shift in hours
            shifts_per_day: Number of shifts per day
            highlight_arabic: Whether to highlight Arabic staffing specifically
            include_hourly_breakdown: Whether to include hourly breakdown
            include_daily_summary: Whether to include daily summary
            include_shift_recommendations: Whether to include shift recommendations

        Returns:
            Scheduling result with staffing recommendations
        """
        result_data = {}
        charts = []
        recommendations = []

        # Combine all data for staffing analysis
        df_combined = pd.DataFrame()
        for key, df in data.items():
            if not df.empty and "Call Date" in df.columns:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return self._format_result(
                {"error": "No data available for agent scheduling"}, []
            )

        # Ensure Call Date is datetime
        df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

        # Add hour and weekday
        df_combined["Hour"] = df_combined["Call Date"].dt.hour
        df_combined["Weekday"] = df_combined[
            "Call Date"
        ].dt.dayofweek  # 0=Monday, 6=Sunday
        df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()

        # Add language if available
        if "Language" not in df_combined.columns and "language" in df_combined.columns:
            df_combined["Language"] = df_combined["language"]

        # Add campaign if available
        if "Campaign" not in df_combined.columns and "campaign" in df_combined.columns:
            df_combined["Campaign"] = df_combined["campaign"]

        # Calculate staffing by language if requested
        if by_language and "Language" in df_combined.columns:
            language_staffing = self._calculate_staffing_by_language(
                df_combined, target_service_level, max_wait_time
            )
            result_data["language_staffing"] = language_staffing["data"]
            charts.extend(language_staffing["charts"])
            recommendations.extend(language_staffing["recommendations"])

        # Calculate staffing by campaign if requested
        if by_campaign and "Campaign" in df_combined.columns:
            campaign_staffing = self._calculate_staffing_by_campaign(
                df_combined, target_service_level, max_wait_time
            )
            result_data["campaign_staffing"] = campaign_staffing["data"]
            charts.extend(campaign_staffing["charts"])
            recommendations.extend(campaign_staffing["recommendations"])

        # Calculate overall staffing
        overall_staffing = self._calculate_overall_staffing(
            df_combined, target_service_level, max_wait_time
        )
        result_data["overall_staffing"] = overall_staffing["data"]
        charts.extend(overall_staffing["charts"])
        recommendations.extend(overall_staffing["recommendations"])

        # Add recommendations to result
        result_data["recommendations"] = recommendations

        return self._format_result(result_data, charts)

    def _calculate_overall_staffing(
        self, df: pd.DataFrame, target_service_level: float, max_wait_time: int
    ) -> Dict[str, Any]:
        """
        Calculate overall staffing requirements

        Args:
            df: DataFrame with call data
            target_service_level: Target service level (e.g., 0.8 for 80%)
            max_wait_time: Maximum acceptable wait time in seconds

        Returns:
            Dictionary with staffing data, charts, and recommendations
        """
        result = {"data": {}, "charts": [], "recommendations": []}

        # Group by hour and weekday to get call volumes
        hourly_volumes = (
            df.groupby(["Weekday", "Hour"]).size().reset_index(name="call_volume")
        )

        # Calculate average handle time
        if "Length" in df.columns:
            # Calculate average handle time for each hour and weekday
            handle_times = (
                df.groupby(["Weekday", "Hour"])["Length"].mean().reset_index()
            )
            hourly_volumes = hourly_volumes.merge(
                handle_times, on=["Weekday", "Hour"], how="left"
            )
            hourly_volumes.rename(columns={"Length": "avg_handle_time"}, inplace=True)
        else:
            # Use a default handle time of 180 seconds
            hourly_volumes["avg_handle_time"] = 180

        # Calculate required staff using Erlang C formula
        hourly_volumes["required_staff"] = hourly_volumes.apply(
            lambda row: self._erlang_c(
                row["call_volume"] / 3600,  # Convert to calls per second
                row["avg_handle_time"],
                target_service_level,
                max_wait_time,
            ),
            axis=1,
        )

        # Add weekday names for better readability
        weekday_names = {
            0: "Monday",
            1: "Tuesday",
            2: "Wednesday",
            3: "Thursday",
            4: "Friday",
            5: "Saturday",
            6: "Sunday",
        }
        hourly_volumes["WeekdayName"] = hourly_volumes["Weekday"].map(weekday_names)

        # Prepare result data
        result["data"]["hourly_staffing"] = hourly_volumes.to_dict(orient="records")

        # Create a pivot table for visualization
        staff_pivot = hourly_volumes.pivot(
            index="Hour", columns="WeekdayName", values="required_staff"
        )

        # Fill NaN values with 0
        staff_pivot = staff_pivot.fillna(0)

        # Create heatmap chart data
        result["charts"].append(
            self._prepare_chart_data(
                chart_type="heatmap",
                title="Required Staff by Hour and Day",
                x_label="Day of Week",
                y_label="Hour of Day",
                data={
                    "x": staff_pivot.columns.tolist(),
                    "y": staff_pivot.index.tolist(),
                    "z": staff_pivot.values.tolist(),
                },
            )
        )

        # Create bar chart for average staff by day
        daily_staff = (
            hourly_volumes.groupby("WeekdayName")["required_staff"].mean().reset_index()
        )
        daily_staff = daily_staff.sort_values(
            "WeekdayName",
            key=lambda x: pd.Categorical(
                x,
                categories=[
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                    "Sunday",
                ],
            ),
        )

        result["charts"].append(
            self._prepare_chart_data(
                chart_type="bar",
                title="Average Required Staff by Day",
                x_label="Day of Week",
                y_label="Average Required Staff",
                data={
                    "x": daily_staff["WeekdayName"].tolist(),
                    "y": daily_staff["required_staff"].tolist(),
                },
            )
        )

        # Calculate total weekly staff hours
        total_staff_hours = hourly_volumes["required_staff"].sum()
        result["data"]["total_staff_hours"] = int(total_staff_hours)
        result["data"]["avg_daily_staff_hours"] = int(total_staff_hours / 7)

        # Calculate peak staffing requirements
        peak_hour = hourly_volumes.loc[hourly_volumes["required_staff"].idxmax()]
        result["data"]["peak_staffing"] = {
            "weekday": peak_hour["WeekdayName"],
            "hour": int(peak_hour["Hour"]),
            "required_staff": int(peak_hour["required_staff"]),
            "call_volume": int(peak_hour["call_volume"]),
        }

        # Add recommendations
        peak_day = daily_staff.iloc[daily_staff["required_staff"].idxmax()]
        result["recommendations"].append(
            f"Increase staffing on {peak_day['WeekdayName']} to handle higher call volumes (average of {peak_day['required_staff']:.1f} agents needed)."
        )

        peak_hour_data = (
            hourly_volumes.sort_values("required_staff", ascending=False)
            .head(1)
            .iloc[0]
        )
        result["recommendations"].append(
            f"Ensure maximum staffing at {int(peak_hour_data['Hour'])}:00 on {peak_hour_data['WeekdayName']} ({int(peak_hour_data['required_staff'])} agents needed)."
        )

        return result

    def _calculate_staffing_by_language(
        self, df: pd.DataFrame, target_service_level: float, max_wait_time: int
    ) -> Dict[str, Any]:
        """
        Calculate staffing requirements broken down by language

        Args:
            df: DataFrame with call data
            target_service_level: Target service level (e.g., 0.8 for 80%)
            max_wait_time: Maximum acceptable wait time in seconds

        Returns:
            Dictionary with staffing data, charts, and recommendations
        """
        result = {"data": {}, "charts": [], "recommendations": []}

        # Check if Language column exists
        if "Language" not in df.columns:
            return result

        # Get unique languages
        languages = df["Language"].dropna().unique()

        # Calculate staffing for each language
        language_staffing = {}
        language_totals = {}

        for language in languages:
            # Filter data for this language
            language_df = df[df["Language"] == language]

            # Group by hour and weekday to get call volumes
            hourly_volumes = (
                language_df.groupby(["Weekday", "Hour"])
                .size()
                .reset_index(name="call_volume")
            )

            # Calculate average handle time
            if "Length" in language_df.columns:
                # Calculate average handle time for each hour and weekday
                handle_times = (
                    language_df.groupby(["Weekday", "Hour"])["Length"]
                    .mean()
                    .reset_index()
                )
                hourly_volumes = hourly_volumes.merge(
                    handle_times, on=["Weekday", "Hour"], how="left"
                )
                hourly_volumes.rename(
                    columns={"Length": "avg_handle_time"}, inplace=True
                )
            else:
                # Use a default handle time of 180 seconds
                hourly_volumes["avg_handle_time"] = 180

            # Calculate required staff using Erlang C formula
            hourly_volumes["required_staff"] = hourly_volumes.apply(
                lambda row: self._erlang_c(
                    row["call_volume"] / 3600,  # Convert to calls per second
                    row["avg_handle_time"],
                    target_service_level,
                    max_wait_time,
                ),
                axis=1,
            )

            # Add weekday names for better readability
            weekday_names = {
                0: "Monday",
                1: "Tuesday",
                2: "Wednesday",
                3: "Thursday",
                4: "Friday",
                5: "Saturday",
                6: "Sunday",
            }
            hourly_volumes["WeekdayName"] = hourly_volumes["Weekday"].map(weekday_names)

            # Store results
            language_staffing[language] = hourly_volumes.to_dict(orient="records")
            language_totals[language] = hourly_volumes["required_staff"].sum()

            # Create a pivot table for visualization
            staff_pivot = hourly_volumes.pivot(
                index="Hour", columns="WeekdayName", values="required_staff"
            )

            # Fill NaN values with 0
            staff_pivot = staff_pivot.fillna(0)

            # Create heatmap chart data
            result["charts"].append(
                self._prepare_chart_data(
                    chart_type="heatmap",
                    title=f"Required {language} Staff by Hour and Day",
                    x_label="Day of Week",
                    y_label="Hour of Day",
                    data={
                        "x": staff_pivot.columns.tolist(),
                        "y": staff_pivot.index.tolist(),
                        "z": staff_pivot.values.tolist(),
                    },
                )
            )

        # Store language staffing data
        result["data"]["by_language"] = language_staffing
        result["data"]["language_totals"] = language_totals

        # Create bar chart for language comparison
        languages_list = list(language_totals.keys())
        totals_list = [language_totals[lang] for lang in languages_list]

        result["charts"].append(
            self._prepare_chart_data(
                chart_type="bar",
                title="Total Staff Hours by Language",
                x_label="Language",
                y_label="Total Staff Hours",
                data={
                    "x": languages_list,
                    "y": totals_list,
                },
            )
        )

        # Add recommendations
        for language in languages:
            avg_daily = language_totals[language] / 7
            result["recommendations"].append(
                f"Maintain an average of {avg_daily:.1f} {language}-speaking agents per day ({language_totals[language]:.1f} total weekly hours)."
            )

        return result

    def _calculate_staffing_by_campaign(
        self, df: pd.DataFrame, target_service_level: float, max_wait_time: int
    ) -> Dict[str, Any]:
        """
        Calculate staffing requirements broken down by campaign

        Args:
            df: DataFrame with call data
            target_service_level: Target service level (e.g., 0.8 for 80%)
            max_wait_time: Maximum acceptable wait time in seconds

        Returns:
            Dictionary with staffing data, charts, and recommendations
        """
        result = {"data": {}, "charts": [], "recommendations": []}

        # Check if Campaign column exists
        if "Campaign" not in df.columns:
            return result

        # Get unique campaigns
        campaigns = df["Campaign"].dropna().unique()

        # Calculate staffing for each campaign
        campaign_staffing = {}
        campaign_totals = {}

        for campaign in campaigns:
            # Filter data for this campaign
            campaign_df = df[df["Campaign"] == campaign]

            # Group by hour and weekday to get call volumes
            hourly_volumes = (
                campaign_df.groupby(["Weekday", "Hour"])
                .size()
                .reset_index(name="call_volume")
            )

            # Calculate average handle time
            if "Length" in campaign_df.columns:
                # Calculate average handle time for each hour and weekday
                handle_times = (
                    campaign_df.groupby(["Weekday", "Hour"])["Length"]
                    .mean()
                    .reset_index()
                )
                hourly_volumes = hourly_volumes.merge(
                    handle_times, on=["Weekday", "Hour"], how="left"
                )
                hourly_volumes.rename(
                    columns={"Length": "avg_handle_time"}, inplace=True
                )
            else:
                # Use a default handle time of 180 seconds
                hourly_volumes["avg_handle_time"] = 180

            # Calculate required staff using Erlang C formula
            hourly_volumes["required_staff"] = hourly_volumes.apply(
                lambda row: self._erlang_c(
                    row["call_volume"] / 3600,  # Convert to calls per second
                    row["avg_handle_time"],
                    target_service_level,
                    max_wait_time,
                ),
                axis=1,
            )

            # Add weekday names for better readability
            weekday_names = {
                0: "Monday",
                1: "Tuesday",
                2: "Wednesday",
                3: "Thursday",
                4: "Friday",
                5: "Saturday",
                6: "Sunday",
            }
            hourly_volumes["WeekdayName"] = hourly_volumes["Weekday"].map(weekday_names)

            # Store results
            campaign_staffing[campaign] = hourly_volumes.to_dict(orient="records")
            campaign_totals[campaign] = hourly_volumes["required_staff"].sum()

        # Store campaign staffing data
        result["data"]["by_campaign"] = campaign_staffing
        result["data"]["campaign_totals"] = campaign_totals

        # Create bar chart for campaign comparison
        campaigns_list = list(campaign_totals.keys())
        totals_list = [campaign_totals[camp] for camp in campaigns_list]

        result["charts"].append(
            self._prepare_chart_data(
                chart_type="bar",
                title="Total Staff Hours by Campaign",
                x_label="Campaign",
                y_label="Total Staff Hours",
                data={
                    "x": campaigns_list,
                    "y": totals_list,
                },
            )
        )

        # Add recommendations
        for campaign in campaigns:
            avg_daily = campaign_totals[campaign] / 7
            result["recommendations"].append(
                f"Allocate an average of {avg_daily:.1f} agents per day to the {campaign} campaign ({campaign_totals[campaign]:.1f} total weekly hours)."
            )

        return result

    def _erlang_c(
        self,
        arrival_rate: float,
        avg_handle_time: float,
        target_service_level: float,
        max_wait_time: int,
    ) -> int:
        """
        Calculate required staff using Erlang C formula

        Args:
            arrival_rate: Call arrival rate (calls per second)
            avg_handle_time: Average handle time in seconds
            target_service_level: Target service level (e.g., 0.8 for 80%)
            max_wait_time: Maximum acceptable wait time in seconds

        Returns:
            Required number of staff
        """
        # Convert to erlangs (traffic intensity)
        traffic = arrival_rate * avg_handle_time

        # Start with minimum staff required (more than traffic)
        staff = math.ceil(traffic) + 1

        # Calculate service level with current staffing
        service_level = self._calculate_service_level(traffic, staff, max_wait_time)

        # Increase staff until we meet or exceed the target service level
        while (
            service_level < target_service_level and staff < 100
        ):  # Cap at 100 to prevent infinite loops
            staff += 1
            service_level = self._calculate_service_level(traffic, staff, max_wait_time)

        return staff

    def _calculate_service_level(
        self, traffic: float, staff: int, wait_time: int
    ) -> float:
        """
        Calculate service level using Erlang C formula

        Args:
            traffic: Traffic intensity in erlangs
            staff: Number of staff
            wait_time: Maximum wait time in seconds

        Returns:
            Service level (probability of answering within wait_time)
        """
        if staff <= traffic:  # Unstable queue
            return 0.0

        # Calculate probability of delay (Erlang C formula)
        p0 = 0.0
        for i in range(staff):
            p0 += (traffic**i) / math.factorial(i)
        p0 += (traffic**staff) / (math.factorial(staff) * (1 - traffic / staff))
        p0 = 1.0 / p0

        pc = (traffic**staff) / (math.factorial(staff) * (1 - traffic / staff)) * p0

        # Calculate service level
        # Using the standard Erlang C formula for service level
        # SL = 1 - P(delay) * e^(-μ * (N - A) * t)
        # where μ = 1/AHT, N = staff, A = traffic, t = wait_time
        # Since traffic = arrival_rate * AHT, we can simplify:
        service_level = 1 - pc * math.exp(
            -(staff - traffic) * wait_time / (traffic / staff)
        )

        return max(0.0, min(1.0, service_level))
