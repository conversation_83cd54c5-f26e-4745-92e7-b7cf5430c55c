"""
Staffing optimization analyzer for call flow analytics.
"""
import pandas as pd
import numpy as np
from typing import Dict, Any
import logging
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StaffingOptimizer(BaseAnalyzer):
    """Optimizer for staffing levels"""

    def optimize(
        self,
        data: Dict[str, pd.DataFrame],
        target_service_level: float,
        max_wait_time: int,
    ) -> Dict[str, Any]:
        """
        Optimize staffing levels

        Args:
            data: Dictionary of DataFrames
            target_service_level: Target service level (e.g., 0.8 for 80%)
            max_wait_time: Maximum acceptable wait time in seconds

        Returns:
            Optimization result
        """
        result_data = {}
        charts = []

        # Combine all data for staffing analysis
        df_combined = pd.DataFrame()
        for key, df in data.items():
            if not df.empty and "Call Date" in df.columns:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return self._format_result(
                {"error": "No data available for staffing optimization"}, []
            )

        # Ensure Call Date is datetime
        df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

        # Add hour and weekday
        df_combined["Hour"] = df_combined["Call Date"].dt.hour
        df_combined["Weekday"] = df_combined[
            "Call Date"
        ].dt.dayofweek  # 0=Monday, 6=Sunday
        df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()

        # Group by hour and weekday to get call volumes
        hourly_volumes = (
            df_combined.groupby(["Weekday", "Hour"])
            .size()
            .reset_index(name="call_volume")
        )

        # Calculate required staff based on Erlang C formula (simplified)
        # For each hour and weekday, estimate required staff
        hourly_volumes["avg_handle_time"] = 0
        if "Length" in df_combined.columns:
            # Calculate average handle time for each hour and weekday
            handle_times = (
                df_combined.groupby(["Weekday", "Hour"])["Length"].mean().reset_index()
            )
            hourly_volumes = hourly_volumes.merge(
                handle_times, on=["Weekday", "Hour"], how="left"
            )
            hourly_volumes.rename(columns={"Length": "avg_handle_time"}, inplace=True)
        else:
            # Use a default handle time of 180 seconds
            hourly_volumes["avg_handle_time"] = 180

        # Simple staffing calculation (this is a simplified version of Erlang C)
        # In a real implementation, you would use a proper Erlang C calculator
        hourly_volumes["required_staff"] = np.ceil(
            hourly_volumes["call_volume"]
            * hourly_volumes["avg_handle_time"]
            / 3600
            / (1 - target_service_level)
        )

        # Ensure minimum staffing level
        hourly_volumes["required_staff"] = hourly_volumes["required_staff"].apply(
            lambda x: max(1, int(x))
        )

        # Add weekday names for better readability
        weekday_names = {
            0: "Monday",
            1: "Tuesday",
            2: "Wednesday",
            3: "Thursday",
            4: "Friday",
            5: "Saturday",
            6: "Sunday",
        }
        hourly_volumes["WeekdayName"] = hourly_volumes["Weekday"].map(weekday_names)

        # Prepare result data
        result_data["hourly_staffing"] = hourly_volumes.to_dict(orient="records")

        # Create a pivot table for visualization
        staff_pivot = hourly_volumes.pivot(
            index="Hour", columns="WeekdayName", values="required_staff"
        )

        # Fill NaN values with 0
        staff_pivot = staff_pivot.fillna(0)

        # Create heatmap chart data
        charts.append(
            self._prepare_chart_data(
                chart_type="heatmap",
                title="Required Staff by Hour and Day",
                x_label="Day of Week",
                y_label="Hour of Day",
                data={
                    "x": staff_pivot.columns.tolist(),
                    "y": staff_pivot.index.tolist(),
                    "z": staff_pivot.values.tolist(),
                },
            )
        )

        # Create bar chart for average staff by day
        daily_staff = (
            hourly_volumes.groupby("WeekdayName")["required_staff"].mean().reset_index()
        )
        daily_staff = daily_staff.sort_values(
            "WeekdayName",
            key=lambda x: pd.Categorical(
                x,
                categories=[
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                    "Sunday",
                ],
            ),
        )

        charts.append(
            self._prepare_chart_data(
                chart_type="bar",
                title="Average Required Staff by Day",
                x_label="Day of Week",
                y_label="Average Required Staff",
                data={
                    "x": daily_staff["WeekdayName"].tolist(),
                    "y": daily_staff["required_staff"].tolist(),
                },
            )
        )

        # Create bar chart for average staff by hour
        hourly_staff = (
            hourly_volumes.groupby("Hour")["required_staff"].mean().reset_index()
        )

        charts.append(
            self._prepare_chart_data(
                chart_type="bar",
                title="Average Required Staff by Hour",
                x_label="Hour of Day",
                y_label="Average Required Staff",
                data={
                    "x": hourly_staff["Hour"].tolist(),
                    "y": hourly_staff["required_staff"].tolist(),
                },
            )
        )

        # Calculate total weekly staff hours
        total_staff_hours = hourly_volumes["required_staff"].sum()
        result_data["total_staff_hours"] = int(total_staff_hours)
        result_data["avg_daily_staff_hours"] = int(total_staff_hours / 7)

        # Calculate peak staffing requirements
        peak_hour = hourly_volumes.loc[hourly_volumes["required_staff"].idxmax()]
        result_data["peak_staffing"] = {
            "weekday": peak_hour["WeekdayName"],
            "hour": int(peak_hour["Hour"]),
            "required_staff": int(peak_hour["required_staff"]),
            "call_volume": int(peak_hour["call_volume"]),
        }

        return self._format_result(result_data, charts)
