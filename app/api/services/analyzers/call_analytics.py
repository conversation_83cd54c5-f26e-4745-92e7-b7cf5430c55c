"""
Comprehensive call analytics with recommendations for call flow analytics.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List
import logging
from datetime import datetime, timedelta
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CallAnalyticsRecommender(BaseAnalyzer):
    """Comprehensive call analytics with actionable recommendations"""

    def analyze(
        self, data: Dict[str, pd.DataFrame]
    ) -> Dict[str, Any]:
        """
        Analyze call data and generate recommendations

        Args:
            data: Dictionary of DataFrames

        Returns:
            Analysis result with recommendations
        """
        result_data = {}
        charts = []
        recommendations = []

        # Combine all data for analysis
        df_combined = pd.DataFrame()
        for key, df in data.items():
            if df is not None and not df.empty:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return self._format_result(
                {"error": "No data available for call analytics"}, []
            )

        # Ensure Call Date is datetime
        if "Call Date" in df_combined.columns:
            df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])
            
            # Extract hour for hourly analysis
            df_combined["Hour"] = df_combined["Call Date"].dt.hour
            
            # 1. Call Volume by Hour Analysis
            hourly_volume = self._analyze_hourly_volume(df_combined)
            result_data["hourly_volume"] = hourly_volume["data"]
            charts.append(hourly_volume["chart"])
            recommendations.extend(hourly_volume["recommendations"])
            
        # 2. Language Distribution Analysis
        if "Campaign" in df_combined.columns:
            language_dist = self._analyze_language_distribution(df_combined)
            result_data["language_distribution"] = language_dist["data"]
            charts.append(language_dist["chart"])
            recommendations.extend(language_dist["recommendations"])
            
        # 3. Agent Performance Analysis
        if "User" in df_combined.columns and "Length" in df_combined.columns:
            agent_perf = self._analyze_agent_performance(df_combined)
            result_data["agent_performance"] = agent_perf["data"]
            charts.extend(agent_perf["charts"])
            recommendations.extend(agent_perf["recommendations"])
            
        # 4. Queue Time Analysis
        if "Queue Time" in df_combined.columns:
            queue_analysis = self._analyze_queue_time(df_combined)
            result_data["queue_analysis"] = queue_analysis["data"]
            charts.append(queue_analysis["chart"])
            recommendations.extend(queue_analysis["recommendations"])
            
        # 5. Call Status Analysis
        if "Status" in df_combined.columns:
            status_analysis = self._analyze_call_status(df_combined)
            result_data["status_analysis"] = status_analysis["data"]
            charts.append(status_analysis["chart"])
            recommendations.extend(status_analysis["recommendations"])
            
        # 6. Campaign Analysis
        if "Campaign" in df_combined.columns:
            campaign_analysis = self._analyze_campaigns(df_combined)
            result_data["campaign_analysis"] = campaign_analysis["data"]
            charts.append(campaign_analysis["chart"])
            recommendations.extend(campaign_analysis["recommendations"])
            
        # 7. Forecasting (using cumulative call volume)
        if "Call Date" in df_combined.columns:
            forecasting = self._analyze_forecasting(df_combined)
            result_data["forecasting"] = forecasting["data"]
            charts.append(forecasting["chart"])
            recommendations.extend(forecasting["recommendations"])
        
        # Add recommendations to result data
        result_data["recommendations"] = recommendations
        
        return self._format_result(result_data, charts)
    
    def _analyze_hourly_volume(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze call volume by hour"""
        result = {"data": {}, "chart": {}, "recommendations": []}
        
        # Group by hour and count calls
        hourly_counts = df.groupby("Hour").size().reset_index(name="call_count")
        hourly_counts = hourly_counts.sort_values("Hour")
        
        # Find peak hours (top 25% of hours by volume)
        threshold = hourly_counts["call_count"].quantile(0.75)
        peak_hours = hourly_counts[hourly_counts["call_count"] >= threshold]["Hour"].tolist()
        
        # Format peak hours for display
        peak_hours_formatted = [f"{hour}:00-{hour+1}:00" for hour in peak_hours]
        peak_hours_text = ", ".join(peak_hours_formatted)
        
        # Create recommendation
        recommendation = f"Optimize Scheduling: Increase staffing between {peak_hours_formatted[0]} and {peak_hours_formatted[-1]} to handle peak call volumes."
        result["recommendations"].append(recommendation)
        
        # Prepare data for result
        result["data"] = hourly_counts.to_dict(orient="records")
        
        # Create chart
        result["chart"] = self._prepare_chart_data(
            chart_type="line",
            title="Call Volume by Hour",
            x_label="Hour of Day",
            y_label="Number of Calls",
            data={
                "x": hourly_counts["Hour"].tolist(),
                "y": hourly_counts["call_count"].tolist(),
            },
        )
        
        return result
    
    def _analyze_language_distribution(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze distribution of calls by language"""
        result = {"data": {}, "chart": {}, "recommendations": []}
        
        # Extract language from campaign name (assuming format like "TechSupp_English")
        df["Language"] = df["Campaign"].str.extract(r"_(English|Arabic)$", expand=False)
        
        # Fill missing values with default
        df["Language"].fillna("Unknown", inplace=True)
        
        # Group by language and count calls
        language_counts = df.groupby("Language").size().reset_index(name="call_count")
        
        # Calculate percentages
        total_calls = language_counts["call_count"].sum()
        language_counts["percentage"] = (language_counts["call_count"] / total_calls * 100).round(1)
        
        # Create recommendation based on distribution
        if len(language_counts) > 1:
            # Check if languages are balanced or one dominates
            max_lang = language_counts.loc[language_counts["call_count"].idxmax()]
            min_lang = language_counts.loc[language_counts["call_count"].idxmin()]
            
            if max_lang["percentage"] > 70:
                recommendation = f"Language Skills: Prioritize {max_lang['Language']} speaking agents as they handle {max_lang['percentage']}% of calls."
            else:
                recommendation = f"Balance Language Skills: Maintain a balanced roster of {' and '.join(language_counts['Language'].tolist())} speaking agents, as call volumes are nearly equal."
            
            result["recommendations"].append(recommendation)
        
        # Prepare data for result
        result["data"] = language_counts.to_dict(orient="records")
        
        # Create chart
        result["chart"] = self._prepare_chart_data(
            chart_type="bar",
            title="Call Distribution by Language",
            x_label="Language",
            y_label="Number of Calls",
            data={
                "x": language_counts["Language"].tolist(),
                "y": language_counts["call_count"].tolist(),
            },
        )
        
        return result
    
    def _analyze_agent_performance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze agent performance metrics"""
        result = {"data": {}, "charts": [], "recommendations": []}
        
        # Group by agent and calculate metrics
        agent_metrics = df.groupby("User").agg(
            call_count=("Length", "count"),
            avg_duration=("Length", "mean"),
            total_duration=("Length", "sum")
        ).reset_index()
        
        # Calculate calls per hour (assuming 8-hour workday)
        agent_metrics["calls_per_hour"] = (agent_metrics["call_count"] / 8).round(1)
        
        # Sort by call count descending
        agent_metrics = agent_metrics.sort_values("call_count", ascending=False)
        
        # Identify agents with longest average call duration
        long_duration_agents = agent_metrics.sort_values("avg_duration", ascending=False).head(3)
        
        # Create recommendation for agent training
        if not long_duration_agents.empty:
            agent_names = long_duration_agents["User"].tolist()
            recommendation = f"Agent Training: Agents with longer call durations ({', '.join(agent_names)}) may benefit from training to handle complex calls more efficiently."
            result["recommendations"].append(recommendation)
        
        # Prepare data for result
        result["data"] = agent_metrics.to_dict(orient="records")
        
        # Create charts
        # 1. Bar chart for call count by agent
        result["charts"].append(
            self._prepare_chart_data(
                chart_type="bar",
                title="Calls Handled per Agent",
                x_label="Agent",
                y_label="Number of Calls",
                data={
                    "x": agent_metrics["User"].tolist(),
                    "y": agent_metrics["call_count"].tolist(),
                },
            )
        )
        
        # 2. Bar chart for average call duration by agent
        result["charts"].append(
            self._prepare_chart_data(
                chart_type="bar",
                title="Average Call Duration per Agent",
                x_label="Agent",
                y_label="Duration (seconds)",
                data={
                    "x": agent_metrics["User"].tolist(),
                    "y": agent_metrics["avg_duration"].tolist(),
                },
            )
        )
        
        return result
    
    def _analyze_queue_time(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze queue time metrics"""
        result = {"data": {}, "chart": {}, "recommendations": []}
        
        # Calculate average queue time by hour
        queue_by_hour = df.groupby("Hour").agg(
            avg_queue_time=("Queue Time", "mean"),
            max_queue_time=("Queue Time", "max"),
            call_count=("Queue Time", "count")
        ).reset_index()
        
        # Identify hours with long queue times
        long_queue_hours = queue_by_hour[queue_by_hour["avg_queue_time"] > 60]["Hour"].tolist()
        
        # Create recommendation for queue optimization
        if long_queue_hours:
            hours_formatted = [f"{hour}:00-{hour+1}:00" for hour in long_queue_hours]
            recommendation = f"Address Queue Bottlenecks: Long wait times during {', '.join(hours_formatted)} indicate need for additional staffing during these hours."
            result["recommendations"].append(recommendation)
        
        # Prepare data for result
        result["data"] = queue_by_hour.to_dict(orient="records")
        
        # Create chart
        result["chart"] = self._prepare_chart_data(
            chart_type="line",
            title="Average Queue Time by Hour",
            x_label="Hour of Day",
            y_label="Queue Time (seconds)",
            data={
                "x": queue_by_hour["Hour"].tolist(),
                "y": queue_by_hour["avg_queue_time"].tolist(),
            },
        )
        
        return result
    
    def _analyze_call_status(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze call status distribution"""
        result = {"data": {}, "chart": {}, "recommendations": []}
        
        # Group by status and count calls
        status_counts = df.groupby("Status").size().reset_index(name="call_count")
        
        # Calculate percentages
        total_calls = status_counts["call_count"].sum()
        status_counts["percentage"] = (status_counts["call_count"] / total_calls * 100).round(1)
        
        # Check for high rate of "Agent Not Available" or similar statuses
        problem_statuses = ["AGENT NOT AVAILABLE", "ABANDONED", "MISSED", "NO ANSWER"]
        problem_status_data = status_counts[status_counts["Status"].isin(problem_statuses)]
        
        if not problem_status_data.empty:
            problem_pct = problem_status_data["call_count"].sum() / total_calls * 100
            if problem_pct > 20:
                top_problem = problem_status_data.iloc[0]["Status"]
                recommendation = f"Address {top_problem}: The high rate of '{top_problem}' statuses ({problem_pct:.1f}%) suggests adding more agents or redistributing shifts to cover peak times."
                result["recommendations"].append(recommendation)
        
        # Prepare data for result
        result["data"] = status_counts.to_dict(orient="records")
        
        # Create chart
        result["chart"] = self._prepare_chart_data(
            chart_type="pie",
            title="Call Status Distribution",
            x_label="",
            y_label="",
            data={
                "labels": status_counts["Status"].tolist(),
                "values": status_counts["call_count"].tolist(),
            },
        )
        
        return result
    
    def _analyze_campaigns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze campaign performance"""
        result = {"data": {}, "chart": {}, "recommendations": []}
        
        # Group by campaign and count calls
        campaign_counts = df.groupby("Campaign").size().reset_index(name="call_count")
        
        # Sort by call count descending
        campaign_counts = campaign_counts.sort_values("call_count", ascending=False)
        
        # Calculate percentages
        total_calls = campaign_counts["call_count"].sum()
        campaign_counts["percentage"] = (campaign_counts["call_count"] / total_calls * 100).round(1)
        
        # Create recommendation based on top campaign
        if not campaign_counts.empty:
            top_campaign = campaign_counts.iloc[0]["Campaign"]
            top_pct = campaign_counts.iloc[0]["percentage"]
            
            # Extract campaign type (assuming format like "Transaction_English")
            campaign_type = top_campaign.split("_")[0] if "_" in top_campaign else top_campaign
            
            recommendation = f"Prioritize {campaign_type} Campaigns: Allocate more resources to {campaign_type} calls, which form the majority ({top_pct}%) of the volume."
            result["recommendations"].append(recommendation)
        
        # Prepare data for result
        result["data"] = campaign_counts.to_dict(orient="records")
        
        # Create chart
        result["chart"] = self._prepare_chart_data(
            chart_type="bar",
            title="Call Distribution by Campaign",
            x_label="Campaign",
            y_label="Number of Calls",
            data={
                "x": campaign_counts["Campaign"].tolist(),
                "y": campaign_counts["call_count"].tolist(),
            },
        )
        
        return result
    
    def _analyze_forecasting(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze cumulative call volume for forecasting insights"""
        result = {"data": {}, "chart": {}, "recommendations": []}
        
        # Ensure data is sorted by date
        df = df.sort_values("Call Date")
        
        # Group by date and count calls
        daily_counts = df.groupby(df["Call Date"].dt.date).size().reset_index(name="call_count")
        
        # Calculate cumulative sum
        daily_counts["cumulative_calls"] = daily_counts["call_count"].cumsum()
        
        # Convert date to string for JSON serialization
        daily_counts["date_str"] = daily_counts["Call Date"].astype(str)
        
        # Create recommendation for forecasting
        recommendation = "Forecasting: Use the cumulative call volume trend to predict daily call patterns, adjusting staffing dynamically based on hourly data."
        result["recommendations"].append(recommendation)
        
        # Prepare data for result
        result["data"] = daily_counts.to_dict(orient="records")
        
        # Create chart
        result["chart"] = self._prepare_chart_data(
            chart_type="area",
            title="Cumulative Call Volume Over Time",
            x_label="Date",
            y_label="Cumulative Number of Calls",
            data={
                "x": daily_counts["date_str"].tolist(),
                "y": daily_counts["cumulative_calls"].tolist(),
            },
        )
        
        return result
