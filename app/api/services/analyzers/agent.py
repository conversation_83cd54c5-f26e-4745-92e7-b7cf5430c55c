"""
Agent-related analyzer classes for call flow analytics.
"""

import pandas as pd
from typing import Dict, List, Any
import logging
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgentPerformanceAnalyzer(BaseAnalyzer):
    """Analyzer for agent performance"""

    def analyze(
        self, data: Dict[str, pd.DataFrame], metrics: List[str]
    ) -> Dict[str, Any]:
        """
        Analyze agent performance

        Args:
            data: Dictionary of DataFrames
            metrics: List of metrics to analyze

        Returns:
            Analysis result
        """
        result_data = {}
        charts = []

        # Combine inbound and outbound data for agent analysis
        df_combined = pd.DataFrame()
        if (
            "inbound" in data
            and data["inbound"] is not None
            and not data["inbound"].empty
        ):
            df_combined = pd.concat([df_combined, data["inbound"]])
        if (
            "outbound" in data
            and data["outbound"] is not None
            and not data["outbound"].empty
        ):
            df_combined = pd.concat([df_combined, data["outbound"]])

        if df_combined.empty:
            return self._format_result(
                {"error": "No data available for agent performance analysis"}, []
            )

        # Calculate metrics by agent
        agent_metrics = {}

        # Group by agent (User)
        if "User" in df_combined.columns:
            agent_groups = df_combined.groupby("User")

            # Call volume by agent
            if "resolution_rate" in metrics or "call_volume" in metrics:
                call_volume = agent_groups.size().reset_index(name="call_count")
                result_data["call_volume"] = call_volume.to_dict(orient="records")

                # Create chart for call volume
                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Call Volume by Agent",
                        x_label="Agent",
                        y_label="Number of Calls",
                        data={
                            "x": call_volume["User"].tolist(),
                            "y": call_volume["call_count"].tolist(),
                        },
                    )
                )

            # Average call length by agent
            if "call_length" in metrics and "Length" in df_combined.columns:
                avg_length = (
                    agent_groups["Length"].mean().reset_index(name="avg_length")
                )
                result_data["avg_call_length"] = avg_length.to_dict(orient="records")

                # Create chart for average call length
                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Average Call Length by Agent",
                        x_label="Agent",
                        y_label="Average Length (seconds)",
                        data={
                            "x": avg_length["User"].tolist(),
                            "y": avg_length["avg_length"].tolist(),
                        },
                    )
                )

            # Resolution rate by agent (approximated by Status)
            if "resolution_rate" in metrics and "Status" in df_combined.columns:
                # Assuming certain statuses indicate resolution
                resolved_statuses = ["ANSWERED", "COMPLETED", "RESOLVED"]

                # Calculate resolution rate
                resolution_data = []
                for agent, group in agent_groups:
                    total_calls = len(group)
                    resolved_calls = group[
                        group["Status"].isin(resolved_statuses)
                    ].shape[0]
                    resolution_rate = (
                        resolved_calls / total_calls if total_calls > 0 else 0
                    )
                    resolution_data.append(
                        {
                            "User": agent,
                            "resolution_rate": resolution_rate,
                            "resolved_calls": resolved_calls,
                            "total_calls": total_calls,
                        }
                    )

                result_data["resolution_rate"] = resolution_data

                # Create chart for resolution rate
                resolution_df = pd.DataFrame(resolution_data)
                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Resolution Rate by Agent",
                        x_label="Agent",
                        y_label="Resolution Rate",
                        data={
                            "x": resolution_df["User"].tolist(),
                            "y": resolution_df["resolution_rate"].tolist(),
                        },
                    )
                )

            # Calculate overall agent score (weighted average of metrics)
            if len(metrics) > 0:
                agent_scores = []

                for agent in df_combined["User"].unique():
                    score_components = {}

                    # Get agent's data
                    agent_data = df_combined[df_combined["User"] == agent]

                    # Call volume score (normalized)
                    if "call_volume" in metrics:
                        call_count = len(agent_data)
                        max_calls = df_combined.groupby("User").size().max()
                        score_components["call_volume"] = (
                            call_count / max_calls if max_calls > 0 else 0
                        )

                    # Call length score (inverse normalized - shorter is better)
                    if "call_length" in metrics and "Length" in df_combined.columns:
                        avg_length = agent_data["Length"].mean()
                        max_length = df_combined.groupby("User")["Length"].mean().max()
                        score_components["call_length"] = 1 - (
                            avg_length / max_length if max_length > 0 else 0
                        )

                    # Resolution rate score
                    if "resolution_rate" in metrics and "Status" in df_combined.columns:
                        resolved_calls = agent_data[
                            agent_data["Status"].isin(resolved_statuses)
                        ].shape[0]
                        total_calls = len(agent_data)
                        score_components["resolution_rate"] = (
                            resolved_calls / total_calls if total_calls > 0 else 0
                        )

                    # Calculate overall score (equal weights for simplicity)
                    overall_score = (
                        sum(score_components.values()) / len(score_components)
                        if score_components
                        else 0
                    )

                    agent_scores.append(
                        {
                            "User": agent,
                            "overall_score": overall_score,
                            "components": score_components,
                        }
                    )

                result_data["agent_scores"] = agent_scores

                # Create chart for agent scores
                agent_score_df = pd.DataFrame(agent_scores)
                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Overall Agent Performance Score",
                        x_label="Agent",
                        y_label="Score",
                        data={
                            "x": agent_score_df["User"].tolist(),
                            "y": agent_score_df["overall_score"].tolist(),
                        },
                    )
                )

        return self._format_result(result_data, charts)


class AgentSpecializationAnalyzer(BaseAnalyzer):
    """Analyzer for agent specialization"""

    def analyze(self, data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Analyze agent specialization

        Args:
            data: Dictionary of DataFrames

        Returns:
            Analysis result
        """
        # Placeholder for future implementation
        return self._format_result(
            {"message": "Agent specialization analysis not yet implemented"}, []
        )
