"""
Sentiment analyzer for call flow analytics.
"""

import pandas as pd
from typing import Dict, Any, List
import logging
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SentimentAnalyzer(BaseAnalyzer):
    """Analyzer for sentiment analysis"""

    def analyze(
        self, data: Dict[str, pd.DataFrame], include_text_analysis: bool = False
    ) -> Dict[str, Any]:
        """
        Analyze customer sentiment based on call patterns

        Args:
            data: Dictionary of DataFrames
            include_text_analysis: Whether to include text analysis (not implemented yet)

        Returns:
            Analysis result
        """
        result_data = {}
        charts = []

        # Combine all data for sentiment analysis
        df_combined = pd.DataFrame()
        for _, df in data.items():
            if not df.empty:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return self._format_result(
                {"error": "No data available for sentiment analysis"}, []
            )

        # Ensure Call Date is datetime if it exists
        if "Call Date" in df_combined.columns:
            df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

        # Since we don't have actual sentiment scores, we'll use proxy metrics:
        # 1. Call length (shorter calls might indicate better resolution)
        # 2. Call status (certain statuses might indicate positive outcomes)
        # 3. Repeat calls (fewer repeat calls might indicate better resolution)
        # 4. Missed calls (fewer missed calls might indicate better customer experience)

        # Calculate sentiment metrics

        # 1. Call length analysis
        if "Length" in df_combined.columns:
            # Remove outliers
            q1 = df_combined["Length"].quantile(0.25)
            q3 = df_combined["Length"].quantile(0.75)
            iqr = q3 - q1
            length_min = max(0, q1 - 1.5 * iqr)
            length_max = q3 + 1.5 * iqr

            df_length = df_combined[
                (df_combined["Length"] >= length_min)
                & (df_combined["Length"] <= length_max)
            ]

            # Calculate statistics
            avg_length = df_length["Length"].mean()
            median_length = df_length["Length"].median()

            result_data["call_length"] = {
                "average": avg_length,
                "median": median_length,
                "min": df_length["Length"].min(),
                "max": df_length["Length"].max(),
            }

            # Create histogram of call lengths
            length_bins = [0, 60, 120, 180, 300, 600, 1200]
            length_labels = ["<1m", "1-2m", "2-3m", "3-5m", "5-10m", ">10m"]

            length_hist = (
                pd.cut(
                    df_length["Length"],
                    bins=length_bins,
                    labels=length_labels,
                    include_lowest=True,
                )
                .value_counts()
                .reset_index()
            )

            length_hist.columns = ["duration", "count"]
            length_hist = length_hist.sort_values("duration")

            result_data["call_length_distribution"] = length_hist.to_dict(
                orient="records"
            )

            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Call Duration Distribution",
                    x_label="Duration",
                    y_label="Number of Calls",
                    data={
                        "x": length_hist["duration"].tolist(),
                        "y": length_hist["count"].tolist(),
                    },
                )
            )

            # Call length by campaign
            if "Campaign" in df_combined.columns:
                campaign_length = (
                    df_length.groupby("Campaign")["Length"].mean().reset_index()
                )
                campaign_length = campaign_length.sort_values("Length")

                result_data["campaign_avg_length"] = campaign_length.to_dict(
                    orient="records"
                )

                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Average Call Duration by Campaign",
                        x_label="Campaign",
                        y_label="Average Duration (seconds)",
                        data={
                            "x": campaign_length["Campaign"].tolist(),
                            "y": campaign_length["Length"].tolist(),
                        },
                    )
                )

        # 2. Call status analysis
        if "Status" in df_combined.columns:
            status_counts = df_combined["Status"].value_counts().reset_index()
            status_counts.columns = ["status", "count"]

            # Categorize statuses as positive, neutral, or negative
            positive_statuses = ["ANSWERED", "COMPLETED", "RESOLVED"]
            negative_statuses = ["FAILED", "BUSY", "NO ANSWER", "CANCELED", "MISSED"]

            status_counts["sentiment"] = status_counts["status"].apply(
                lambda x: (
                    "Positive"
                    if x in positive_statuses
                    else ("Negative" if x in negative_statuses else "Neutral")
                )
            )

            # Group by sentiment
            sentiment_counts = (
                status_counts.groupby("sentiment")["count"].sum().reset_index()
            )

            result_data["status_sentiment"] = {
                "by_status": status_counts.to_dict(orient="records"),
                "by_sentiment": sentiment_counts.to_dict(orient="records"),
            }

            # Create pie chart for sentiment distribution
            charts.append(
                self._prepare_chart_data(
                    chart_type="pie",
                    title="Call Outcome Sentiment Distribution",
                    x_label="",
                    y_label="",
                    data={
                        "labels": sentiment_counts["sentiment"].tolist(),
                        "values": sentiment_counts["count"].tolist(),
                    },
                )
            )

        # 3. Repeat call analysis (if Phone is available)
        if "Phone" in df_combined.columns:
            # Count calls per phone number
            call_counts = (
                df_combined.groupby("Phone").size().reset_index(name="call_count")
            )

            # Calculate repeat call metrics
            total_customers = len(call_counts)
            repeat_customers = len(call_counts[call_counts["call_count"] > 1])
            repeat_rate = (
                repeat_customers / total_customers if total_customers > 0 else 0
            )

            result_data["repeat_calls"] = {
                "total_customers": total_customers,
                "repeat_customers": repeat_customers,
                "repeat_rate": repeat_rate,
                "avg_calls_per_customer": call_counts["call_count"].mean(),
            }

            # Create histogram of calls per customer
            call_count_hist = call_counts["call_count"].value_counts().reset_index()
            call_count_hist.columns = ["calls_per_customer", "customer_count"]
            call_count_hist = call_count_hist.sort_values("calls_per_customer")

            # Limit to reasonable range for visualization
            call_count_hist = call_count_hist[
                call_count_hist["calls_per_customer"] <= 10
            ]

            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Calls per Customer",
                    x_label="Number of Calls",
                    y_label="Number of Customers",
                    data={
                        "x": call_count_hist["calls_per_customer"].tolist(),
                        "y": call_count_hist["customer_count"].tolist(),
                    },
                )
            )

        # 4. Call type sentiment (inbound, outbound, missed)
        if "Call Type" in df_combined.columns:
            call_type_counts = df_combined["Call Type"].value_counts().reset_index()
            call_type_counts.columns = ["call_type", "count"]

            # Assign sentiment scores to call types
            call_type_sentiment = {
                "Inbound": 0,  # Neutral
                "Outbound": 0.5,  # Slightly positive (proactive)
                "Missed Call": -0.5,  # Slightly negative
            }

            # Add sentiment score
            call_type_counts["sentiment_score"] = call_type_counts["call_type"].apply(
                lambda x: call_type_sentiment.get(x, 0)
            )

            result_data["call_type_sentiment"] = call_type_counts.to_dict(
                orient="records"
            )

            # Create chart for call types
            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Call Volume by Type",
                    x_label="Call Type",
                    y_label="Number of Calls",
                    data={
                        "x": call_type_counts["call_type"].tolist(),
                        "y": call_type_counts["count"].tolist(),
                    },
                )
            )

        # 5. Time-based sentiment (if Call Date is available)
        if "Call Date" in df_combined.columns:
            # Add hour and weekday
            df_combined["Hour"] = df_combined["Call Date"].dt.hour
            df_combined["Weekday"] = df_combined["Call Date"].dt.dayofweek
            df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()

            # Calculate sentiment by hour (using call length as proxy)
            if "Length" in df_combined.columns:
                hourly_length = (
                    df_combined.groupby("Hour")["Length"].mean().reset_index()
                )
                result_data["hourly_avg_length"] = hourly_length.to_dict(
                    orient="records"
                )

                charts.append(
                    self._prepare_chart_data(
                        chart_type="line",
                        title="Average Call Duration by Hour",
                        x_label="Hour of Day",
                        y_label="Average Duration (seconds)",
                        data={
                            "x": hourly_length["Hour"].tolist(),
                            "y": hourly_length["Length"].tolist(),
                        },
                    )
                )

            # Calculate sentiment by weekday (using call length as proxy)
            if "Length" in df_combined.columns and "WeekdayName" in df_combined.columns:
                weekday_length = (
                    df_combined.groupby("WeekdayName")["Length"].mean().reset_index()
                )

                # Sort by day of week
                weekday_order = [
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                    "Sunday",
                ]
                weekday_length["sort_order"] = weekday_length["WeekdayName"].apply(
                    lambda x: weekday_order.index(x)
                )
                weekday_length = weekday_length.sort_values("sort_order")
                weekday_length = weekday_length.drop("sort_order", axis=1)

                result_data["weekday_avg_length"] = weekday_length.to_dict(
                    orient="records"
                )

                charts.append(
                    self._prepare_chart_data(
                        chart_type="bar",
                        title="Average Call Duration by Day of Week",
                        x_label="Day of Week",
                        y_label="Average Duration (seconds)",
                        data={
                            "x": weekday_length["WeekdayName"].tolist(),
                            "y": weekday_length["Length"].tolist(),
                        },
                    )
                )

        # 6. Text analysis (if enabled and text data is available)
        if include_text_analysis and "Notes" in df_combined.columns:
            # This is a placeholder for future text-based sentiment analysis
            # We would use NLP techniques to analyze call notes
            result_data["text_analysis"] = {
                "message": "Text-based sentiment analysis not yet implemented"
            }

        return self._format_result(result_data, charts)
