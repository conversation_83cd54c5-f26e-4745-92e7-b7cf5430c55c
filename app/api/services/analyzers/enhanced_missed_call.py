"""
Enhanced missed call analyzer for call flow analytics with dashboard integration.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List
import logging
from datetime import datetime, timedelta
from app.api.services.analyzers.base import BaseAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedMissedCallAnalyzer(BaseAnalyzer):
    """Enhanced analyzer for missed calls with dashboard integration and recommendations"""

    def analyze(
        self, data: Dict[str, pd.DataFrame], include_callbacks: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze missed calls with enhanced visualizations and recommendations

        Args:
            data: Dictionary of DataFrames
            include_callbacks: Whether to include callback analysis

        Returns:
            Analysis result with recommendations
        """
        result_data = {}
        charts = []
        recommendations = []

        # Get missed call data
        if "missed" not in data or data["missed"] is None or data["missed"].empty:
            return self._format_result({"error": "No missed call data available"}, [])

        df_missed = data["missed"].copy()

        # Ensure Call Date is datetime
        if "Call Date" in df_missed.columns:
            df_missed["Call Date"] = pd.to_datetime(df_missed["Call Date"])
            df_missed["Hour"] = df_missed["Call Date"].dt.hour
            df_missed["Weekday"] = df_missed["Call Date"].dt.dayofweek
            df_missed["WeekdayName"] = df_missed["Call Date"].dt.day_name()
            df_missed["Date"] = df_missed["Call Date"].dt.date
            df_missed["Month"] = df_missed["Call Date"].dt.month
            df_missed["Year"] = df_missed["Call Date"].dt.year
            df_missed["Week"] = df_missed["Call Date"].dt.isocalendar().week

        # Basic missed call metrics
        total_missed = len(df_missed)
        result_data["total_missed_calls"] = total_missed

        # Get inbound data for comparison if available
        inbound_total = 0
        if "inbound" in data and data["inbound"] is not None and not data["inbound"].empty:
            inbound_total = len(data["inbound"])
            result_data["inbound_total"] = inbound_total
            
            # Calculate missed call rate
            if inbound_total + total_missed > 0:
                missed_call_rate = total_missed / (inbound_total + total_missed)
                result_data["missed_call_rate"] = missed_call_rate
                
                # Add recommendation based on missed call rate
                if missed_call_rate > 0.2:  # If more than 20% of calls are missed
                    recommendations.append(
                        f"High Missed Call Rate: {missed_call_rate:.1%} of calls are being missed. Consider increasing staffing levels or implementing a callback system."
                    )
                elif missed_call_rate < 0.05:  # If less than 5% of calls are missed
                    recommendations.append(
                        f"Low Missed Call Rate: Only {missed_call_rate:.1%} of calls are being missed. Current staffing levels appear adequate."
                    )

        # Missed calls by hour
        if "Hour" in df_missed.columns:
            hourly_missed = (
                df_missed.groupby("Hour").size().reset_index(name="missed_count")
            )
            result_data["hourly_missed"] = hourly_missed.to_dict(orient="records")

            # Find peak hours for missed calls
            peak_hours = hourly_missed.sort_values("missed_count", ascending=False).head(3)
            peak_hours_list = peak_hours["Hour"].tolist()
            peak_hours_str = ", ".join([f"{h}:00" for h in peak_hours_list])
            
            # Add recommendation for peak hours
            recommendations.append(
                f"Peak Missed Call Hours: Most calls are missed at {peak_hours_str}. Consider increasing staffing during these hours."
            )

            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Missed Calls by Hour of Day",
                    x_label="Hour of Day",
                    y_label="Number of Missed Calls",
                    data={
                        "x": hourly_missed["Hour"].tolist(),
                        "y": hourly_missed["missed_count"].tolist(),
                    },
                )
            )

        # Missed calls by weekday
        if "WeekdayName" in df_missed.columns:
            weekday_missed = (
                df_missed.groupby("WeekdayName").size().reset_index(name="missed_count")
            )

            # Sort by day of week
            weekday_order = [
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
                "Sunday",
            ]
            weekday_missed["sort_order"] = weekday_missed["WeekdayName"].apply(
                lambda x: weekday_order.index(x)
            )
            weekday_missed = weekday_missed.sort_values("sort_order")
            weekday_missed = weekday_missed.drop("sort_order", axis=1)

            result_data["weekday_missed"] = weekday_missed.to_dict(orient="records")
            
            # Find peak days for missed calls
            peak_day = weekday_missed.loc[weekday_missed["missed_count"].idxmax()]
            
            # Add recommendation for peak day
            recommendations.append(
                f"Peak Missed Call Day: Most calls are missed on {peak_day['WeekdayName']}. Consider adjusting staffing for this day."
            )

            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Missed Calls by Day of Week",
                    x_label="Day of Week",
                    y_label="Number of Missed Calls",
                    data={
                        "x": weekday_missed["WeekdayName"].tolist(),
                        "y": weekday_missed["missed_count"].tolist(),
                    },
                )
            )

        # Missed calls by campaign
        if "Campaign" in df_missed.columns:
            campaign_missed = (
                df_missed.groupby("Campaign").size().reset_index(name="missed_count")
            )
            campaign_missed = campaign_missed.sort_values(
                "missed_count", ascending=False
            )
            result_data["campaign_missed"] = campaign_missed.to_dict(orient="records")
            
            # Find top campaign with missed calls
            top_campaign = campaign_missed.iloc[0]["Campaign"] if not campaign_missed.empty else "Unknown"
            top_campaign_count = campaign_missed.iloc[0]["missed_count"] if not campaign_missed.empty else 0
            
            # Add recommendation for campaign
            if not campaign_missed.empty:
                recommendations.append(
                    f"Campaign Focus: '{top_campaign}' has the highest number of missed calls ({top_campaign_count}). Consider allocating more resources to this campaign."
                )

            charts.append(
                self._prepare_chart_data(
                    chart_type="bar",
                    title="Missed Calls by Campaign",
                    x_label="Campaign",
                    y_label="Number of Missed Calls",
                    data={
                        "x": campaign_missed["Campaign"].tolist(),
                        "y": campaign_missed["missed_count"].tolist(),
                    },
                )
            )
            
        # Missed calls by reason (if available)
        if "Reason" in df_missed.columns:
            reason_missed = (
                df_missed.groupby("Reason").size().reset_index(name="missed_count")
            )
            reason_missed = reason_missed.sort_values(
                "missed_count", ascending=False
            )
            result_data["reason_missed"] = reason_missed.to_dict(orient="records")
            
            # Find top reason for missed calls
            top_reason = reason_missed.iloc[0]["Reason"] if not reason_missed.empty else "Unknown"
            
            # Add recommendation based on reason
            if not reason_missed.empty:
                if top_reason == "AFTER HOURS":
                    recommendations.append(
                        f"After Hours Calls: Most missed calls are occurring after business hours. Consider extending hours or implementing an after-hours voicemail system."
                    )
                elif top_reason == "QUEUE FULL":
                    recommendations.append(
                        f"Queue Capacity: Calls are being missed due to full queues. Consider increasing queue capacity or agent staffing."
                    )
                elif top_reason == "ABANDONED":
                    recommendations.append(
                        f"Call Abandonment: Callers are abandoning before connecting. Consider reducing wait times or implementing queue position announcements."
                    )
                else:
                    recommendations.append(
                        f"Missed Call Reason: The primary reason for missed calls is '{top_reason}'. Address this specific issue to reduce missed calls."
                    )

            charts.append(
                self._prepare_chart_data(
                    chart_type="pie",
                    title="Missed Calls by Reason",
                    x_label="",
                    y_label="",
                    data={
                        "labels": reason_missed["Reason"].tolist(),
                        "values": reason_missed["missed_count"].tolist(),
                    },
                )
            )

        # Trend analysis - missed calls over time
        if "Date" in df_missed.columns:
            daily_missed = (
                df_missed.groupby("Date").size().reset_index(name="missed_count")
            )
            daily_missed["Date_Str"] = daily_missed["Date"].astype(str)
            result_data["daily_missed"] = daily_missed.to_dict(orient="records")
            
            # Check for increasing trend
            if len(daily_missed) > 5:  # Need at least a few days of data
                # Simple trend check - compare first half to second half
                mid_point = len(daily_missed) // 2
                first_half_avg = daily_missed["missed_count"].iloc[:mid_point].mean()
                second_half_avg = daily_missed["missed_count"].iloc[mid_point:].mean()
                
                if second_half_avg > first_half_avg * 1.2:  # 20% increase
                    recommendations.append(
                        f"Increasing Trend: Missed calls are increasing over time. Investigate the cause and take corrective action."
                    )
                elif first_half_avg > second_half_avg * 1.2:  # 20% decrease
                    recommendations.append(
                        f"Decreasing Trend: Missed calls are decreasing over time. Your recent changes appear to be effective."
                    )

            charts.append(
                self._prepare_chart_data(
                    chart_type="line",
                    title="Missed Calls Over Time",
                    x_label="Date",
                    y_label="Number of Missed Calls",
                    data={
                        "x": daily_missed["Date_Str"].tolist(),
                        "y": daily_missed["missed_count"].tolist(),
                    },
                )
            )

        # Callback analysis
        if include_callbacks and "Callback Time" in df_missed.columns:
            # Calculate callback rate
            df_missed["Has Callback"] = df_missed["Callback Time"].notna()
            callback_count = df_missed["Has Callback"].sum()
            callback_rate = callback_count / total_missed if total_missed > 0 else 0

            result_data["callback_metrics"] = {
                "callback_count": int(callback_count),
                "callback_rate": callback_rate,
            }
            
            # Add recommendation based on callback rate
            if callback_rate < 0.5:  # Less than 50% callback rate
                recommendations.append(
                    f"Low Callback Rate: Only {callback_rate:.1%} of missed calls receive callbacks. Implement a more robust callback system."
                )
            elif callback_rate > 0.9:  # More than 90% callback rate
                recommendations.append(
                    f"Excellent Callback Rate: {callback_rate:.1%} of missed calls receive callbacks. Maintain this high standard."
                )

            # Create pie chart for callback status
            charts.append(
                self._prepare_chart_data(
                    chart_type="pie",
                    title="Missed Call Callback Status",
                    x_label="",
                    y_label="",
                    data={
                        "labels": ["Received Callback", "No Callback"],
                        "values": [
                            int(callback_count),
                            int(total_missed - callback_count),
                        ],
                    },
                )
            )

        # Add recommendations to result data
        result_data["recommendations"] = recommendations
        
        return self._format_result(result_data, charts)
