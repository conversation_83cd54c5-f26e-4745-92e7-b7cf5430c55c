"""
Enhanced time series forecaster for call flow analytics.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime, timedelta
from app.api.services.analyzers.base import BaseAnalyzer
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.arima.model import ARIMA
from sklearn.linear_model import LinearRegression

# Import advanced forecasting methods
try:
    from app.api.services.analyzers.advanced_forecasters import (
        forecast_with_prophet,
        forecast_with_xgboost,
        forecast_with_random_forest,
        forecast_with_lstm,
    )

    ADVANCED_FORECASTERS_AVAILABLE = True
except ImportError:
    ADVANCED_FORECASTERS_AVAILABLE = False
    logging.warning(
        "Advanced forecasters not available. Some forecasting methods will not work."
    )

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedTimeSeriesForecaster(BaseAnalyzer):
    """Enhanced time series forecaster for call volumes with language and campaign breakdowns"""

    def forecast(
        self,
        data: Dict[str, pd.DataFrame],
        forecast_days: int = 7,
        granularity: str = "daily",
        by_language: bool = True,
        by_campaign: bool = True,
        forecast_method: str = "auto",
    ) -> Dict[str, Any]:
        """
        Forecast call volumes with language and campaign breakdowns

        Args:
            data: Dictionary of DataFrames
            forecast_days: Number of days to forecast
            granularity: Time granularity for forecasting (hourly, daily, weekly)
            by_language: Whether to break down forecasts by language
            by_campaign: Whether to break down forecasts by campaign
            forecast_method: Forecasting method to use (auto, ets, arima, linear)

        Returns:
            Forecasting result with charts
        """
        try:
            logger.info("Starting time series forecast")
            result_data = {}
            charts = []
            recommendations = []

            # Combine all data for call volume analysis
            df_combined = pd.DataFrame()
            for _, df in data.items():  # Use _ for unused variable
                if df is not None and not df.empty and "Call Date" in df.columns:
                    df_combined = pd.concat([df_combined, df])

            if df_combined.empty:
                logger.warning("No data available for forecasting")
                return self._format_result(
                    {"error": "No data available for call volume forecasting"}, []
                )

            # Log data shape for debugging
            logger.info(f"Combined data shape: {df_combined.shape}")
            logger.info(f"Columns: {df_combined.columns.tolist()}")
        except Exception as e:
            logger.error(f"Error in forecast initialization: {str(e)}")
            return self._format_result(
                {"error": f"Error initializing forecast: {str(e)}"}, []
            )

        try:
            # Ensure Call Date is datetime
            df_combined["Call Date"] = pd.to_datetime(df_combined["Call Date"])

            # Extract time features
            df_combined["Hour"] = df_combined["Call Date"].dt.hour
            df_combined["Date"] = df_combined["Call Date"].dt.date
            df_combined["Weekday"] = df_combined["Call Date"].dt.dayofweek
            df_combined["WeekdayName"] = df_combined["Call Date"].dt.day_name()
            df_combined["Month"] = df_combined["Call Date"].dt.month
            df_combined["Year"] = df_combined["Call Date"].dt.year
            df_combined["Week"] = df_combined["Call Date"].dt.isocalendar().week

            # Extract language from campaign if not present
            if (
                "Language" not in df_combined.columns
                and "Campaign" in df_combined.columns
            ):
                df_combined["Language"] = df_combined["Campaign"].str.extract(
                    r"_(English|Arabic)$"
                )
                df_combined["Language"].fillna("Unknown", inplace=True)

            logger.info("Successfully extracted time features")
        except Exception as e:
            logger.error(f"Error extracting time features: {str(e)}")
            return self._format_result(
                {"error": f"Error extracting time features: {str(e)}"}, []
            )

        try:
            # 1. Overall Call Volume Forecast
            logger.info("Starting overall call volume forecast")
            overall_forecast = self._forecast_time_series(
                df_combined, forecast_days, granularity, forecast_method
            )

            result_data["overall_forecast"] = overall_forecast["data"]
            charts.append(overall_forecast["chart"])

            # Add recommendation based on overall forecast
            if "trend" in overall_forecast:
                if overall_forecast["trend"] > 0.1:  # 10% increase
                    recommendations.append(
                        f"Increasing Call Volume: Call volume is projected to increase by {overall_forecast['trend']:.1%} over the next {forecast_days} days. Consider increasing staffing levels."
                    )
                elif overall_forecast["trend"] < -0.1:  # 10% decrease
                    recommendations.append(
                        f"Decreasing Call Volume: Call volume is projected to decrease by {abs(overall_forecast['trend']):.1%} over the next {forecast_days} days. Consider optimizing staffing levels."
                    )
                else:
                    recommendations.append(
                        f"Stable Call Volume: Call volume is projected to remain stable over the next {forecast_days} days."
                    )

            logger.info("Successfully completed overall forecast")
        except Exception as e:
            logger.error(f"Error in overall call volume forecast: {str(e)}")
            result_data["overall_forecast"] = {
                "error": f"Error in overall forecast: {str(e)}",
                "historical_values": [0],
                "historical_dates": ["Error"],
                "forecast_values": [0],
                "forecast_dates": ["Error"],
                "trend": 0,
                "method_used": "error",
            }
            # Add a minimal chart
            charts.append(
                self._prepare_chart_data(
                    chart_type="line",
                    title="Overall Call Volume Forecast (Error)",
                    x_label="Time",
                    y_label="Calls",
                    data={
                        "x": ["Error"],
                        "y": [0],
                        "series": ["Error"],
                    },
                )
            )

        # 2. Language-based Forecasts
        try:
            if by_language and "Language" in df_combined.columns:
                logger.info("Starting language-based forecasts")
                language_forecasts = {}
                language_charts = []

                for language in df_combined["Language"].unique():
                    try:
                        if language == "Unknown" or pd.isna(language):
                            continue

                        language_data = df_combined[df_combined["Language"] == language]
                        if len(language_data) < 10:  # Need minimum data points
                            continue

                        language_forecast = self._forecast_time_series(
                            language_data,
                            forecast_days,
                            granularity,
                            forecast_method,
                            f"{language} Calls",
                        )

                        language_forecasts[language] = language_forecast["data"]
                        language_charts.append(language_forecast["chart"])

                        # Add language-specific recommendation
                        if "trend" in language_forecast:
                            if language_forecast["trend"] > 0.15:  # 15% increase
                                recommendations.append(
                                    f"Increasing {language} Calls: {language} call volume is projected to increase by {language_forecast['trend']:.1%}. Ensure adequate {language}-speaking staff."
                                )
                    except Exception as e:
                        logger.error(
                            f"Error forecasting for language {language}: {str(e)}"
                        )
                        # Add a placeholder for this language
                        language_forecasts[language] = {
                            "error": f"Error forecasting for {language}: {str(e)}",
                            "historical_values": [0],
                            "historical_dates": ["Error"],
                            "forecast_values": [0],
                            "forecast_dates": ["Error"],
                            "trend": 0,
                            "method_used": "error",
                        }

                result_data["language_forecasts"] = language_forecasts
                charts.extend(language_charts)

                logger.info("Successfully completed language-based forecasts")
        except Exception as e:
            logger.error(f"Error in language-based forecasts: {str(e)}")
            result_data["language_forecasts"] = {
                "error": f"Error in language forecasts: {str(e)}"
            }

            # Add language distribution recommendation
            try:
                if (
                    len(language_forecasts) > 1
                    and "English" in language_forecasts
                    and "Arabic" in language_forecasts
                ):
                    # Safely convert and sum values
                    eng_total = 0
                    for val in language_forecasts["English"]["forecast_values"]:
                        try:
                            eng_total += float(val)
                        except (ValueError, TypeError):
                            logger.error(
                                f"Error converting English forecast value: {val}"
                            )

                    ar_total = 0
                    for val in language_forecasts["Arabic"]["forecast_values"]:
                        try:
                            ar_total += float(val)
                        except (ValueError, TypeError):
                            logger.error(
                                f"Error converting Arabic forecast value: {val}"
                            )

                    total = eng_total + ar_total

                    if total > 0:
                        eng_pct = eng_total / total
                        ar_pct = ar_total / total

                        recommendations.append(
                            f"Language Distribution: Forecast shows {eng_pct:.1%} English and {ar_pct:.1%} Arabic calls. Staff accordingly."
                        )
            except Exception as e:
                logger.error(f"Error in language distribution recommendation: {str(e)}")
                recommendations.append(
                    "Language Distribution: Unable to calculate language distribution due to an error."
                )

        # 3. Campaign-based Forecasts
        try:
            if by_campaign and "Campaign" in df_combined.columns:
                logger.info("Starting campaign-based forecasts")
                campaign_forecasts = {}
                campaign_charts = []

                # Get top campaigns by volume
                try:
                    top_campaigns = (
                        df_combined["Campaign"].value_counts().head(5).index.tolist()
                    )

                    for campaign in top_campaigns:
                        try:
                            campaign_data = df_combined[
                                df_combined["Campaign"] == campaign
                            ]
                            if len(campaign_data) < 10:  # Need minimum data points
                                continue

                            campaign_forecast = self._forecast_time_series(
                                campaign_data,
                                forecast_days,
                                granularity,
                                forecast_method,
                                f"{campaign} Campaign",
                            )

                            campaign_forecasts[campaign] = campaign_forecast["data"]
                            campaign_charts.append(campaign_forecast["chart"])

                            # Add campaign-specific recommendation
                            if (
                                "trend" in campaign_forecast
                                and campaign_forecast["trend"] > 0.2
                            ):  # 20% increase
                                recommendations.append(
                                    f"High Growth Campaign: '{campaign}' is projected to grow by {campaign_forecast['trend']:.1%}. Prioritize resources for this campaign."
                                )
                        except Exception as e:
                            logger.error(
                                f"Error forecasting for campaign {campaign}: {str(e)}"
                            )
                            # Add a placeholder for this campaign
                            campaign_forecasts[campaign] = {
                                "error": f"Error forecasting for {campaign}: {str(e)}",
                                "historical_values": [0],
                                "historical_dates": ["Error"],
                                "forecast_values": [0],
                                "forecast_dates": ["Error"],
                                "trend": 0,
                                "method_used": "error",
                            }
                except Exception as e:
                    logger.error(f"Error getting top campaigns: {str(e)}")

                result_data["campaign_forecasts"] = campaign_forecasts
                charts.extend(campaign_charts)

                # Add campaign prioritization recommendation
                try:
                    if len(campaign_forecasts) > 1:
                        # Safely calculate campaign totals
                        campaign_totals = {}
                        for campaign, data in campaign_forecasts.items():
                            total = 0
                            for val in data["forecast_values"]:
                                try:
                                    total += float(val)
                                except (ValueError, TypeError):
                                    logger.error(
                                        f"Error converting campaign forecast value: {val}"
                                    )
                            campaign_totals[campaign] = total

                        if campaign_totals:
                            top_campaign = max(
                                campaign_totals.items(), key=lambda x: x[1]
                            )

                            recommendations.append(
                                f"Campaign Prioritization: '{top_campaign[0]}' is projected to have the highest volume. Ensure adequate staffing for this campaign."
                            )
                except Exception as e:
                    logger.error(
                        f"Error in campaign prioritization recommendation: {str(e)}"
                    )

                logger.info("Successfully completed campaign-based forecasts")
        except Exception as e:
            logger.error(f"Error in campaign-based forecasts: {str(e)}")
            result_data["campaign_forecasts"] = {
                "error": f"Error in campaign forecasts: {str(e)}"
            }

        # 4. Hourly Pattern Forecast
        try:
            if granularity == "hourly":
                logger.info("Starting hourly pattern forecast")
                try:
                    hourly_pattern = (
                        df_combined.groupby("Hour")
                        .size()
                        .reset_index(name="call_count")
                    )
                    hourly_pattern = hourly_pattern.sort_values("Hour")

                    result_data["hourly_pattern"] = hourly_pattern.to_dict(
                        orient="records"
                    )

                    charts.append(
                        self._prepare_chart_data(
                            chart_type="bar",
                            title="Hourly Call Pattern",
                            x_label="Hour of Day",
                            y_label="Number of Calls",
                            data={
                                "x": hourly_pattern["Hour"].tolist(),
                                "y": hourly_pattern["call_count"].tolist(),
                            },
                        )
                    )

                    # Find peak hours
                    peak_hours = hourly_pattern.sort_values(
                        "call_count", ascending=False
                    ).head(3)
                    peak_hours_list = peak_hours["Hour"].tolist()
                    peak_hours_str = ", ".join([f"{h}:00" for h in peak_hours_list])

                    recommendations.append(
                        f"Peak Hours Staffing: Highest call volumes occur at {peak_hours_str}. Maximize staffing during these hours."
                    )

                    logger.info("Successfully completed hourly pattern forecast")
                except Exception as e:
                    logger.error(f"Error in hourly pattern forecast: {str(e)}")
        except Exception as e:
            logger.error(f"Error checking hourly granularity: {str(e)}")

        # Add recommendations to result data
        try:
            result_data["recommendations"] = recommendations
            logger.info(f"Added {len(recommendations)} recommendations to result data")
        except Exception as e:
            logger.error(f"Error adding recommendations to result data: {str(e)}")

        # Format final result
        logger.info("Formatting final result")
        return self._format_result(result_data, charts)

    def _forecast_time_series(
        self,
        df: pd.DataFrame,
        forecast_days: int,
        granularity: str,
        method: str,
        series_name: str = "Call Volume",
    ) -> Dict[str, Any]:
        """
        Forecast a time series using the specified method

        Args:
            df: DataFrame with call data
            forecast_days: Number of days to forecast
            granularity: Time granularity (hourly, daily, weekly)
            method: Forecasting method (auto, ets, arima, linear)
            series_name: Name of the time series for chart title

        Returns:
            Dictionary with forecast data and chart
        """
        result = {"data": {}, "chart": {}}

        # Group data by time period
        if granularity == "hourly":
            # For hourly, we need to handle differently - group by date and hour
            df["datetime"] = pd.to_datetime(
                df["Date"].astype(str) + " " + df["Hour"].astype(str) + ":00:00"
            )
            time_series = df.groupby("datetime").size()
            # Set frequency for time series
            time_series_freq = "H"  # Used in statsmodels
            forecast_periods = forecast_days * 24
            time_format = "%Y-%m-%d %H:00"
            time_unit = "hour"
        elif granularity == "daily":
            time_series = df.groupby("Date").size()
            # Set frequency for time series
            time_series_freq = "D"  # Used in statsmodels
            forecast_periods = forecast_days
            time_format = "%Y-%m-%d"
            time_unit = "day"
        elif granularity == "weekly":
            # Group by year and week
            df["YearWeek"] = df["Year"].astype(str) + "-" + df["Week"].astype(str)
            time_series = df.groupby("YearWeek").size()
            # Set frequency for time series
            time_series_freq = "W"  # Used in statsmodels
            forecast_periods = (forecast_days + 6) // 7  # Convert days to weeks
            time_format = "%Y-%U"
            time_unit = "week"
        else:
            # Default to daily
            time_series = df.groupby("Date").size()
            # Set frequency for time series
            time_series_freq = "D"  # Used in statsmodels
            forecast_periods = forecast_days
            time_format = "%Y-%m-%d"
            time_unit = "day"

        # Ensure time series is sorted
        time_series = time_series.sort_index()

        # Convert to DataFrame for easier handling
        ts_df = time_series.reset_index()
        ts_df.columns = ["time_period", "call_count"]

        # Store historical data
        historical_data = ts_df.copy()
        historical_data["time_str"] = (
            historical_data["time_period"].dt.strftime(time_format)
            if granularity == "hourly"
            else historical_data["time_period"].astype(str)
        )

        # Select forecasting method
        if method == "auto":
            # Choose method based on data size
            if len(time_series) < 30:
                method = "linear"
            elif len(time_series) < 60:
                method = "ets"
            else:
                method = "arima"

        # Apply forecasting method
        try:
            if method == "ets":
                # Exponential smoothing
                # Create a pandas Series with explicit frequency to avoid warning
                try:
                    # Convert index to DatetimeIndex if it's not already
                    if not isinstance(time_series.index, pd.DatetimeIndex):
                        # Try to convert to datetime
                        try:
                            ts_index = pd.DatetimeIndex(
                                pd.to_datetime(time_series.index)
                            )
                        except:
                            # If conversion fails, create a new index
                            logger.warning(
                                "Failed to convert index to DatetimeIndex, creating new index"
                            )
                            end_date = datetime.now()
                            if granularity == "hourly":
                                start_date = end_date - timedelta(
                                    hours=len(time_series)
                                )
                                ts_index = pd.date_range(
                                    start=start_date, periods=len(time_series), freq="H"
                                )
                            elif granularity == "weekly":
                                start_date = end_date - timedelta(
                                    weeks=len(time_series)
                                )
                                ts_index = pd.date_range(
                                    start=start_date, periods=len(time_series), freq="W"
                                )
                            else:  # daily is default
                                start_date = end_date - timedelta(days=len(time_series))
                                ts_index = pd.date_range(
                                    start=start_date, periods=len(time_series), freq="D"
                                )
                    else:
                        ts_index = time_series.index

                    # Create series with explicit frequency
                    ts_with_freq = pd.Series(
                        time_series.values,
                        index=pd.DatetimeIndex(ts_index, freq=time_series_freq),
                    )
                except Exception as e:
                    logger.error(f"Error creating time series with frequency: {str(e)}")
                    # Fall back to original time series
                    ts_with_freq = time_series

                model = ExponentialSmoothing(
                    ts_with_freq,
                    trend="add",
                    seasonal="add" if len(time_series) >= 14 else None,
                    seasonal_periods=(
                        7
                        if granularity == "daily"
                        else 24 if granularity == "hourly" else None
                    ),
                )
                fit_model = model.fit()
                forecast_values = fit_model.forecast(forecast_periods)
            elif method == "arima":
                # ARIMA model - use simple approach to avoid frequency issues
                try:
                    # Try with statsmodels ARIMA
                    model = ARIMA(time_series, order=(1, 1, 1))
                    fit_model = model.fit()
                    forecast_values = fit_model.forecast(forecast_periods)
                except Exception as e:
                    logger.error(
                        f"ARIMA error: {str(e)}, falling back to simple forecasting"
                    )
                    # Fallback to simple moving average
                    if len(time_series) > 0:
                        avg_calls = time_series.mean()
                        forecast_values = np.array([avg_calls] * forecast_periods)
                    else:
                        forecast_values = np.array([0] * forecast_periods)
            elif method == "linear":
                # Linear regression on time index
                X = np.arange(len(time_series)).reshape(-1, 1)
                y = time_series.values
                model = LinearRegression()
                model.fit(X, y)

                # Forecast future values
                forecast_indices = np.arange(
                    len(time_series), len(time_series) + forecast_periods
                ).reshape(-1, 1)
                forecast_values = model.predict(forecast_indices)
                forecast_values = np.maximum(forecast_values, 0)  # Ensure non-negative
            # Advanced forecasting methods
            elif method == "prophet" and ADVANCED_FORECASTERS_AVAILABLE:
                # Facebook Prophet
                try:
                    forecast_values, method = forecast_with_prophet(
                        time_series, forecast_periods, granularity
                    )
                except Exception as e:
                    logger.error(f"Prophet forecasting error: {str(e)}")
                    # Fallback to ETS
                    logger.info("Falling back to ETS forecasting")
                    method = "ets (fallback)"
                    model = ExponentialSmoothing(
                        time_series,
                        trend="add",
                        seasonal="add" if len(time_series) >= 14 else None,
                        seasonal_periods=(
                            7
                            if granularity == "daily"
                            else 24 if granularity == "hourly" else None
                        ),
                    )
                    fit_model = model.fit()
                    forecast_values = fit_model.forecast(forecast_periods)
            elif method == "xgboost" and ADVANCED_FORECASTERS_AVAILABLE:
                # XGBoost
                try:
                    forecast_values, method = forecast_with_xgboost(
                        time_series, forecast_periods, granularity
                    )
                except Exception as e:
                    logger.error(f"XGBoost forecasting error: {str(e)}")
                    # Fallback to linear regression
                    logger.info("Falling back to linear regression forecasting")
                    method = "linear (fallback)"
                    X = np.arange(len(time_series)).reshape(-1, 1)
                    y = time_series.values
                    model = LinearRegression()
                    model.fit(X, y)
                    forecast_indices = np.arange(
                        len(time_series), len(time_series) + forecast_periods
                    ).reshape(-1, 1)
                    forecast_values = model.predict(forecast_indices)
                    forecast_values = np.maximum(forecast_values, 0)
            elif method == "random_forest" and ADVANCED_FORECASTERS_AVAILABLE:
                # Random Forest
                try:
                    forecast_values, method = forecast_with_random_forest(
                        time_series, forecast_periods, granularity
                    )
                except Exception as e:
                    logger.error(f"Random Forest forecasting error: {str(e)}")
                    # Fallback to linear regression
                    logger.info("Falling back to linear regression forecasting")
                    method = "linear (fallback)"
                    X = np.arange(len(time_series)).reshape(-1, 1)
                    y = time_series.values
                    model = LinearRegression()
                    model.fit(X, y)
                    forecast_indices = np.arange(
                        len(time_series), len(time_series) + forecast_periods
                    ).reshape(-1, 1)
                    forecast_values = model.predict(forecast_indices)
                    forecast_values = np.maximum(forecast_values, 0)
            elif method == "lstm" and ADVANCED_FORECASTERS_AVAILABLE:
                # LSTM
                try:
                    forecast_values, method = forecast_with_lstm(
                        time_series, forecast_periods, granularity
                    )
                except Exception as e:
                    logger.error(f"LSTM forecasting error: {str(e)}")
                    # Fallback to ARIMA
                    logger.info("Falling back to ARIMA forecasting")
                    method = "arima (fallback)"
                    try:
                        model = ARIMA(time_series, order=(1, 1, 1))
                        fit_model = model.fit()
                        forecast_values = fit_model.forecast(forecast_periods)
                    except Exception as arima_e:
                        logger.error(f"ARIMA fallback error: {str(arima_e)}")
                        # Fallback to simple moving average
                        if len(time_series) > 0:
                            avg_calls = time_series.mean()
                            forecast_values = np.array([avg_calls] * forecast_periods)
                        else:
                            forecast_values = np.array([0] * forecast_periods)
            elif (
                method in ["prophet", "xgboost", "random_forest", "lstm"]
                and not ADVANCED_FORECASTERS_AVAILABLE
            ):
                # Advanced methods requested but not available
                logger.warning(
                    f"{method} forecasting method not available, falling back to auto"
                )
                # Choose method based on data size
                if len(time_series) < 30:
                    method = "linear (fallback)"
                    X = np.arange(len(time_series)).reshape(-1, 1)
                    y = time_series.values
                    model = LinearRegression()
                    model.fit(X, y)
                    forecast_indices = np.arange(
                        len(time_series), len(time_series) + forecast_periods
                    ).reshape(-1, 1)
                    forecast_values = model.predict(forecast_indices)
                    forecast_values = np.maximum(forecast_values, 0)
                elif len(time_series) < 60:
                    method = "ets (fallback)"
                    model = ExponentialSmoothing(
                        time_series,
                        trend="add",
                        seasonal="add" if len(time_series) >= 14 else None,
                        seasonal_periods=(
                            7
                            if granularity == "daily"
                            else 24 if granularity == "hourly" else None
                        ),
                    )
                    fit_model = model.fit()
                    forecast_values = fit_model.forecast(forecast_periods)
                else:
                    method = "arima (fallback)"
                    try:
                        model = ARIMA(time_series, order=(1, 1, 1))
                        fit_model = model.fit()
                        forecast_values = fit_model.forecast(forecast_periods)
                    except Exception as e:
                        logger.error(f"ARIMA error: {str(e)}")
                        # Fallback to simple moving average
                        if len(time_series) > 0:
                            avg_calls = time_series.mean()
                            forecast_values = np.array([avg_calls] * forecast_periods)
                        else:
                            forecast_values = np.array([0] * forecast_periods)
            else:
                # Linear regression (fallback)
                logger.warning(
                    f"Unknown method '{method}', falling back to linear regression"
                )
                method = "linear (fallback)"
                X = np.arange(len(time_series)).reshape(-1, 1)
                y = time_series.values
                model = LinearRegression()
                model.fit(X, y)

                forecast_indices = np.arange(
                    len(time_series), len(time_series) + forecast_periods
                ).reshape(-1, 1)
                forecast_values = model.predict(forecast_indices)
                forecast_values = np.maximum(forecast_values, 0)  # Ensure non-negative
        except Exception as e:
            logger.error(f"Error in forecasting: {str(e)}")
            # Fallback to simple moving average
            if len(time_series) > 0:
                avg_calls = time_series.mean()
                forecast_values = np.array([avg_calls] * forecast_periods)
            else:
                forecast_values = np.array([0] * forecast_periods)

        # Generate forecast dates
        last_date = time_series.index[-1] if len(time_series) > 0 else datetime.now()

        if granularity == "hourly":
            # For hourly, we need to handle differently
            if isinstance(last_date, str):
                last_date = datetime.strptime(last_date, time_format)

            forecast_dates = [
                last_date + timedelta(hours=i + 1) for i in range(forecast_periods)
            ]
        elif granularity == "daily":
            if not isinstance(last_date, datetime) and not isinstance(
                last_date, pd.Timestamp
            ):
                last_date = datetime.strptime(str(last_date), "%Y-%m-%d")

            forecast_dates = [
                last_date + timedelta(days=i + 1) for i in range(forecast_periods)
            ]
        elif granularity == "weekly":
            # For weekly, convert string YearWeek to date
            if isinstance(last_date, str):
                year, week = map(int, last_date.split("-"))
                last_date = datetime.strptime(f"{year}-{week}-1", "%Y-%W-%w")

            forecast_dates = [
                last_date + timedelta(weeks=i + 1) for i in range(forecast_periods)
            ]

        # Format forecast dates
        forecast_date_strs = [d.strftime(time_format) for d in forecast_dates]

        # Calculate trend
        if len(time_series) > 0:
            historical_avg = time_series.mean()
            forecast_avg = forecast_values.mean()

            if historical_avg > 0:
                trend = (forecast_avg - historical_avg) / historical_avg
            else:
                trend = 0
        else:
            trend = 0

        # Prepare result data with safer type conversion
        historical_values = []
        for val in historical_data["call_count"].tolist():
            try:
                historical_values.append(float(val))
            except (ValueError, TypeError):
                logger.error(
                    f"Error converting historical value: {val}, type: {type(val)}"
                )
                historical_values.append(0.0)

        forecast_values_list = []
        for val in forecast_values.tolist():
            try:
                forecast_values_list.append(float(val))
            except (ValueError, TypeError):
                logger.error(
                    f"Error converting forecast value: {val}, type: {type(val)}"
                )
                forecast_values_list.append(0.0)

        result["data"] = {
            "historical_values": historical_values,
            "historical_dates": historical_data["time_str"].tolist(),
            "forecast_values": forecast_values_list,
            "forecast_dates": forecast_date_strs,
            "trend": float(trend),
            "method_used": method,
        }

        # Debug logging
        logger.info(
            f"Historical data types: {[type(x) for x in historical_data['time_str'].tolist()[:3]]}"
        )
        logger.info(
            f"Forecast dates types: {[type(x) for x in forecast_date_strs[:3]]}"
        )
        logger.info(
            f"Historical values types: {[type(x) for x in historical_data['call_count'].tolist()[:3]]}"
        )
        logger.info(
            f"Forecast values types: {[type(x) for x in forecast_values.tolist()[:3]]}"
        )

        # Debug the actual values
        logger.info(
            f"Historical dates (first 3): {historical_data['time_str'].tolist()[:3]}"
        )
        logger.info(f"Forecast dates (first 3): {forecast_date_strs[:3]}")
        logger.info(
            f"Historical values (first 3): {historical_data['call_count'].tolist()[:3]}"
        )
        logger.info(f"Forecast values (first 3): {forecast_values.tolist()[:3]}")

        # Debug the combined data
        try:
            x_data = historical_data["time_str"].tolist() + forecast_date_strs
            logger.info(f"Combined x data (first 6): {x_data[:6]}")

            y_data = []
            for val in historical_data["call_count"].tolist():
                try:
                    y_data.append(float(val))
                except (ValueError, TypeError) as e:
                    logger.error(
                        f"Error converting historical value: {val}, error: {str(e)}"
                    )
                    y_data.append(0.0)

            for val in forecast_values.tolist():
                try:
                    y_data.append(float(val))
                except (ValueError, TypeError) as e:
                    logger.error(
                        f"Error converting forecast value: {val}, error: {str(e)}"
                    )
                    y_data.append(0.0)

            logger.info(f"Combined y data (first 6): {y_data[:6]}")

            series_data = ["Historical"] * len(historical_data) + ["Forecast"] * len(
                forecast_date_strs
            )
            logger.info(f"Combined series data (first 6): {series_data[:6]}")
        except Exception as e:
            logger.error(f"Error in debug section: {str(e)}")

        # Convert all data to appropriate types - with extra safety checks
        try:
            # Make sure we have valid data
            if (
                len(historical_data["time_str"].tolist()) == 0
                or len(forecast_date_strs) == 0
            ):
                logger.warning("Empty historical or forecast data")
                # Create a minimal valid dataset
                x_data = ["Past"] + ["Future"]
                y_data = [0.0, 0.0]
                series_data = ["Historical", "Forecast"]
            else:
                # Combine x data (dates)
                x_data = historical_data["time_str"].tolist() + forecast_date_strs

                # Convert historical values to float
                hist_y_data = []
                for val in historical_data["call_count"].tolist():
                    try:
                        hist_y_data.append(float(val))
                    except (ValueError, TypeError):
                        logger.error(
                            f"Error converting historical value to float: {val}, type: {type(val)}"
                        )
                        hist_y_data.append(0.0)

                # Convert forecast values to float
                forecast_y_data = []
                for val in forecast_values.tolist():
                    try:
                        forecast_y_data.append(float(val))
                    except (ValueError, TypeError):
                        logger.error(
                            f"Error converting forecast value to float: {val}, type: {type(val)}"
                        )
                        forecast_y_data.append(0.0)

                # Combine y data
                y_data = hist_y_data + forecast_y_data

                # Create series labels
                series_data = ["Historical"] * len(hist_y_data) + ["Forecast"] * len(
                    forecast_y_data
                )

                # Final safety check - ensure all arrays have the same length
                min_len = min(len(x_data), len(y_data), len(series_data))
                if min_len < max(len(x_data), len(y_data), len(series_data)):
                    logger.warning(
                        f"Data length mismatch: x={len(x_data)}, y={len(y_data)}, series={len(series_data)}"
                    )
                    x_data = x_data[:min_len]
                    y_data = y_data[:min_len]
                    series_data = series_data[:min_len]

            # Create chart with the safely prepared data
            result["chart"] = self._prepare_chart_data(
                chart_type="line",
                title=f"{series_name} Forecast",
                x_label=f"Time ({time_unit})",
                y_label="Number of Calls",
                data={
                    "x": x_data,
                    "y": y_data,
                    "series": series_data,
                },
            )
        except Exception as e:
            logger.error(f"Error preparing chart data: {str(e)}")
            # Create a minimal valid chart
            result["chart"] = self._prepare_chart_data(
                chart_type="line",
                title=f"{series_name} Forecast (Error)",
                x_label="Time",
                y_label="Calls",
                data={
                    "x": ["Error"],
                    "y": [0],
                    "series": ["Error"],
                },
            )

        result["trend"] = trend

        return result
