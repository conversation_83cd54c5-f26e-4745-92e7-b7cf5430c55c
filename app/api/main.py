from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
import logging
import os
import json
from app.api.routers import data, analytics, advanced, tasks
from app.shared.config import API_HOST, API_PORT
from app.shared.utils import clean_nan_for_json, NaNHandlingJSONEncoder

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Call Flow Analytics API",
    description="API for call flow analytics and predictive modeling",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins in development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Custom response class to handle NaN values
class NaNHandlingJSONResponse(JSONResponse):
    """Custom JSONResponse class that handles NaN values"""

    def render(self, content) -> bytes:
        # Clean NaN values from the content
        cleaned_content = clean_nan_for_json(content)
        return json.dumps(
            cleaned_content,
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
        ).encode("utf-8")


# Set default response class
app.router.default_response_class = NaNHandlingJSONResponse

# Include routers
app.include_router(data.router)
app.include_router(analytics.router)
app.include_router(advanced.router)
app.include_router(tasks.router)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to Call Flow Analytics API",
        "docs_url": "/docs",
        "redoc_url": "/redoc",
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}


@app.post("/direct-train-model")
async def direct_train_model(request: dict):
    """Direct model training endpoint for testing"""
    return {
        "success": True,
        "message": "Direct model training endpoint working",
        "request_data": request,
        "task_id": "test-task-123",
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app.api.main:app", host=API_HOST, port=API_PORT, reload=True)
