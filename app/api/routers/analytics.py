from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import json

from app.api.models.schemas import (
    AnalysisRequest,
    AgentPerformanceRequest,
    CallVolumeRequest,
    StaffingOptimizationRequest,
    CampaignAnalysisRequest,
    LanguageAnalysisRequest,
    MissedCallRequest,
    CustomerJourneyRequest,
    FCRAnalysisRequest,
    SentimentAnalysisRequest,
    AgentSpecializationRequest,
    ConversionRateRequest,
    QueueOptimizationRequest,
    SeasonalTrendRequest,
    GeographicInsightRequest,
    CallQualityRequest,
    ABTestingRequest,
    ChurnPredictionRequest,
    CrossSellingRequest,
    AnomalyDetectionRequest,
    CallbackOptimizationRequest,
    KnowledgeBaseRequest,
    CallAnalyticsRequest,
    EnhancedMissedCallRequest,
    TimeSeriesForecastRequest,
    AgentSchedulingRequest,
    ComprehensiveAnalyticsRequest,
    AnalysisResponse,
    AnalysisResultWithCharts,
    ChartData,
)
from app.api.routers.data import get_data_loader
from app.api.services.analyzers import (
    AgentPerformanceAnalyzer,
    CallVolumePredictor,
    StaffingOptimizer,
    CampaignAnalyzer,
    LanguageAnalyzer,
    MissedCallAnalyzer,
    CustomerJourneyAnalyzer,
    FCRAnalyzer,
    SentimentAnalyzer,
    AgentSpecializationAnalyzer,
    ConversionRateOptimizer,
    QueueOptimizer,
    SeasonalTrendAnalyzer,
    GeographicAnalyzer,
    CallQualityScorer,
    ABTestingAnalyzer,
    ChurnPredictor,
    CrossSellingDetector,
    AnomalyDetector,
    CallbackOptimizer,
    KnowledgeBaseEnhancer,
    CallAnalyticsRecommender,
    EnhancedMissedCallAnalyzer,
    EnhancedTimeSeriesForecaster,
)
from app.api.services.analyzers.comprehensive_analytics import ComprehensiveAnalyzer
from app.api.services.analyzers.agent_scheduling import AgentSchedulingModel

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/analytics",
    tags=["analytics"],
    responses={404: {"description": "Not found"}},
)


# Helper function to filter data by common parameters
def filter_data(df: pd.DataFrame, request: AnalysisRequest) -> pd.DataFrame:
    """
    Filter DataFrame based on common request parameters

    Args:
        df: DataFrame to filter
        request: Request containing filter parameters

    Returns:
        Filtered DataFrame
    """
    if df.empty:
        return df

    filtered_df = df.copy()

    # Filter by date range
    if "Call Date" in filtered_df.columns:
        if request.start_date:
            start_date = pd.to_datetime(request.start_date)
            filtered_df = filtered_df[filtered_df["Call Date"] >= start_date]

        if request.end_date:
            end_date = pd.to_datetime(request.end_date)
            filtered_df = filtered_df[filtered_df["Call Date"] <= end_date]

    # Filter by campaigns
    if request.campaigns and "Campaign" in filtered_df.columns:
        filtered_df = filtered_df[filtered_df["Campaign"].isin(request.campaigns)]

    # Filter by agents
    if request.agents and "User" in filtered_df.columns:
        filtered_df = filtered_df[filtered_df["User"].isin(request.agents)]

    # Filter by languages
    if request.languages and "Language" in filtered_df.columns:
        filtered_df = filtered_df[filtered_df["Language"].isin(request.languages)]

    return filtered_df


# 1. Agent Performance Analytics
@router.post("/agent-performance", response_model=AnalysisResultWithCharts)
async def analyze_agent_performance(
    request: AgentPerformanceRequest, data_loader=Depends(get_data_loader)
):
    """
    Analyze agent performance based on various metrics
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request)
            for key, df in data.items()
            if df is not None and not df.empty
        }

        # Initialize analyzer
        analyzer = AgentPerformanceAnalyzer()

        # Perform analysis
        result = analyzer.analyze(filtered_data, request.metrics)

        return {
            "success": True,
            "message": "Agent performance analysis completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error analyzing agent performance: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 2. Call Volume Prediction
@router.post("/call-volume-prediction", response_model=AnalysisResultWithCharts)
async def predict_call_volume(
    request: CallVolumeRequest, data_loader=Depends(get_data_loader)
):
    """
    Predict call volumes based on historical data
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request)
            for key, df in data.items()
            if df is not None and not df.empty
        }

        # Initialize predictor
        predictor = CallVolumePredictor()

        # Perform prediction
        result = predictor.predict(filtered_data, request.granularity)

        return {
            "success": True,
            "message": f"Call volume prediction ({request.granularity}) completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error predicting call volume: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Add endpoints for all other use cases following the same pattern
# 3. Staffing Optimization
@router.post("/staffing-optimization", response_model=AnalysisResultWithCharts)
async def optimize_staffing(
    request: StaffingOptimizationRequest, data_loader=Depends(get_data_loader)
):
    """
    Optimize staffing based on call volume and service level targets
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request)
            for key, df in data.items()
            if df is not None and not df.empty
        }

        # Initialize optimizer
        optimizer = StaffingOptimizer()

        # Perform optimization
        result = optimizer.optimize(
            filtered_data, request.target_service_level, request.max_wait_time
        )

        return {
            "success": True,
            "message": "Staffing optimization completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error optimizing staffing: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Continue with endpoints for all other use cases...
# For brevity, I'll include just a few more examples


# 4. Campaign Analysis
@router.post("/campaign-analysis", response_model=AnalysisResultWithCharts)
async def analyze_campaigns(
    request: CampaignAnalysisRequest, data_loader=Depends(get_data_loader)
):
    """
    Analyze campaign performance
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request)
            for key, df in data.items()
            if df is not None and not df.empty
        }

        # Initialize analyzer
        analyzer = CampaignAnalyzer()

        # Perform analysis
        result = analyzer.analyze(filtered_data, request.metrics)

        return {
            "success": True,
            "message": "Campaign analysis completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error analyzing campaigns: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 5. Language Analysis
@router.post("/language-analysis", response_model=AnalysisResultWithCharts)
async def analyze_languages(
    request: LanguageAnalysisRequest, data_loader=Depends(get_data_loader)
):
    """
    Analyze performance across different languages
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request)
            for key, df in data.items()
            if df is not None and not df.empty
        }

        # Initialize analyzer
        analyzer = LanguageAnalyzer()

        # Perform analysis
        result = analyzer.analyze(filtered_data, request.languages)

        return {
            "success": True,
            "message": "Language analysis completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error analyzing languages: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 6. Comprehensive Call Analytics with Recommendations
@router.post("/call-analytics", response_model=AnalysisResultWithCharts)
async def analyze_call_data(
    request: CallAnalyticsRequest, data_loader=Depends(get_data_loader)
):
    """
    Comprehensive call analytics with actionable recommendations
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request)
            for key, df in data.items()
            if df is not None and not df.empty
        }

        # Initialize analyzer
        analyzer = CallAnalyticsRecommender()

        # Perform analysis
        result = analyzer.analyze(filtered_data)

        return {
            "success": True,
            "message": "Comprehensive call analytics completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error performing comprehensive call analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 7. Enhanced Missed Call Analysis
@router.post("/enhanced-missed-call", response_model=AnalysisResultWithCharts)
async def analyze_missed_calls_enhanced(
    request: EnhancedMissedCallRequest, data_loader=Depends(get_data_loader)
):
    """
    Enhanced missed call analysis with dashboard integration and recommendations
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request)
            for key, df in data.items()
            if df is not None and not df.empty
        }

        # Initialize analyzer
        analyzer = EnhancedMissedCallAnalyzer()

        # Perform analysis
        result = analyzer.analyze(filtered_data, request.include_callbacks)

        return {
            "success": True,
            "message": "Enhanced missed call analysis completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error analyzing missed calls: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 8. Enhanced Time Series Forecasting
@router.post("/time-series-forecast", response_model=AnalysisResultWithCharts)
async def forecast_time_series(
    request: TimeSeriesForecastRequest, data_loader=Depends(get_data_loader)
):
    """
    Enhanced time series forecasting with language and campaign breakdowns
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request)
            for key, df in data.items()
            if df is not None and not df.empty
        }

        # Initialize forecaster
        forecaster = EnhancedTimeSeriesForecaster()

        # Perform forecasting
        result = forecaster.forecast(
            filtered_data,
            forecast_days=request.forecast_days,
            granularity=request.granularity,
            by_language=request.by_language,
            by_campaign=request.by_campaign,
            forecast_method=request.forecast_method,
        )

        return {
            "success": True,
            "message": f"Time series forecasting for {request.forecast_days} days completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error forecasting time series: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 9. Agent Scheduling Model
@router.post("/agent-scheduling", response_model=AnalysisResultWithCharts)
async def schedule_agents(
    request: AgentSchedulingRequest, data_loader=Depends(get_data_loader)
):
    """
    Schedule agents based on call volume forecasts using Erlang C model
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request)
            for key, df in data.items()
            if df is not None and not df.empty
        }

        # Initialize scheduler
        scheduler = AgentSchedulingModel()

        # Perform scheduling
        result = scheduler.schedule(
            filtered_data,
            target_service_level=request.target_service_level,
            max_wait_time=request.max_wait_time,
            by_language=request.by_language,
            by_campaign=request.by_campaign,
            use_forecast=request.use_forecast,
            forecast_days=request.forecast_days,
            shift_length=request.shift_length,
            shifts_per_day=request.shifts_per_day,
            highlight_arabic=request.highlight_arabic,
            include_hourly_breakdown=request.include_hourly_breakdown,
            include_daily_summary=request.include_daily_summary,
            include_shift_recommendations=request.include_shift_recommendations,
        )

        return {
            "success": True,
            "message": "Agent scheduling completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error scheduling agents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 10. Comprehensive Analytics with All Metrics and Filters
@router.post("/comprehensive-analytics", response_model=AnalysisResultWithCharts)
async def analyze_comprehensive(
    request: ComprehensiveAnalyticsRequest, data_loader=Depends(get_data_loader)
):
    """
    Comprehensive call flow analytics with all requested metrics and filters:
    - Average call time, total duration, standard deviation
    - Day-wise and date-wise filtering
    - Language, campaign, agent, and call type segregation
    - Customer satisfaction analysis (if available)
    - Statistical analysis with percentiles and outlier detection
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Convert request to dict for easier parameter passing
        request_params = request.model_dump()

        # Initialize comprehensive analyzer
        analyzer = ComprehensiveAnalyzer()

        # Perform comprehensive analysis
        result = analyzer.analyze(data, request_params)

        return {
            "success": True,
            "message": "Comprehensive analytics completed successfully",
            "data": result["data"],
            "charts": result["charts"],
        }

    except Exception as e:
        logger.error(f"Error performing comprehensive analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
