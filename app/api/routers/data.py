from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
import os
import shutil
from typing import List
import pandas as pd
import logging
from app.shared.data_loader import Call<PERSON>lowDataLoader
from app.shared.config import DATA_DIR
from app.api.models.schemas import FileUpload, AnalysisResponse
from app.shared.utils import get_cached_result

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/data",
    tags=["data"],
    responses={404: {"description": "Not found"}},
)

# Global data loader instance
data_loader = CallFlowDataLoader()


@router.post("/upload", response_model=AnalysisResponse)
async def upload_files(
    background_tasks: BackgroundTasks, files: List[UploadFile] = File(...)
):
    """
    Upload Excel files for analysis
    """
    try:
        uploaded_files = []

        for file in files:
            # Check if file is an Excel file
            if not file.filename.endswith((".xlsx", ".xls")):
                return JSONResponse(
                    status_code=400,
                    content={
                        "success": False,
                        "message": f"File {file.filename} is not an Excel file",
                        "data": None,
                    },
                )

            # Save the file
            file_path = os.path.join(DATA_DIR, file.filename)
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

            uploaded_files.append(file_path)
            logger.info(f"Uploaded file: {file_path}")

        # Update data loader with new files in the background
        background_tasks.add_task(reload_data, uploaded_files)

        return {
            "success": True,
            "message": f"Successfully uploaded {len(uploaded_files)} files",
            "data": {"filenames": [os.path.basename(f) for f in uploaded_files]},
        }

    except Exception as e:
        logger.error(f"Error uploading files: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/files", response_model=AnalysisResponse)
async def list_files():
    """
    List all available data files
    """
    try:
        files = [f for f in os.listdir(DATA_DIR) if f.endswith((".xlsx", ".xls"))]

        return {
            "success": True,
            "message": f"Found {len(files)} files",
            "data": {"files": files},
        }

    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reload", response_model=AnalysisResponse)
async def reload_data_endpoint(file_paths: List[str] = None):
    """
    Reload data from specified files or all available files
    """
    try:
        success = reload_data(file_paths)

        if success:
            return {
                "success": True,
                "message": "Data reloaded successfully",
                "data": {"file_count": len(file_paths) if file_paths else "all"},
            }
        else:
            return {"success": False, "message": "Failed to reload data", "data": None}

    except Exception as e:
        logger.error(f"Error reloading data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/summary", response_model=AnalysisResponse)
async def get_data_summary(
    start_date: str = None,
    end_date: str = None,
    campaigns: str = None,
    agents: str = None,
    languages: str = None,
):
    """
    Get summary of loaded data with optional filters

    Args:
        start_date: Start date filter (YYYY-MM-DD)
        end_date: End date filter (YYYY-MM-DD)
        campaigns: Comma-separated list of campaigns
        agents: Comma-separated list of agents
        languages: Comma-separated list of languages
    """
    try:
        # Parse filter parameters
        campaign_list = campaigns.split(",") if campaigns else None
        agent_list = agents.split(",") if agents else None
        language_list = languages.split(",") if languages else None

        # Define a function to compute the summary with filters
        def compute_summary(
            start_date=None, end_date=None, campaigns=None, agents=None, languages=None
        ):
            # Ensure data is loaded
            if (
                not hasattr(data_loader, "combined_data")
                or data_loader.combined_data is None
            ):
                reload_data()

            # Get data
            data = data_loader.get_data("all")

            # Check if we have any data
            has_data = any(df is not None and not df.empty for df in data.values())

            if not has_data:
                # Return empty summary if no data
                return {"summary": {}, "has_data": False}

            # Apply filters if provided
            if any([start_date, end_date, campaign_list, agent_list, language_list]):
                # Create a simple request object to use with filter_data
                class FilterRequest:
                    pass

                filter_request = FilterRequest()
                if start_date:
                    filter_request.start_date = start_date
                if end_date:
                    filter_request.end_date = end_date
                if campaign_list:
                    filter_request.campaigns = campaign_list
                if agent_list:
                    filter_request.agents = agent_list
                if language_list:
                    filter_request.languages = language_list

                # Apply filters to each dataframe
                filtered_data = {
                    key: filter_data(df, filter_request)
                    for key, df in data.items()
                    if df is not None and not df.empty
                }
                data = filtered_data

            # Create summary
            summary = {}
            for data_type, df in data.items():
                if df is not None and not df.empty:
                    summary[data_type] = {
                        "rows": len(df),
                        "columns": list(df.columns),
                        "date_range": [
                            (
                                df["Call Date"].min().strftime("%Y-%m-%d")
                                if "Call Date" in df.columns
                                else None
                            ),
                            (
                                df["Call Date"].max().strftime("%Y-%m-%d")
                                if "Call Date" in df.columns
                                else None
                            ),
                        ],
                        "campaigns": (
                            df["Campaign"].unique().tolist()
                            if "Campaign" in df.columns
                            else []
                        ),
                        "users": (
                            df["User"].unique().tolist() if "User" in df.columns else []
                        ),
                    }
            return {"summary": summary, "has_data": True}

        # Get cached or computed summary with filter parameters
        summary_data = get_cached_result(
            "data_summary",
            compute_summary,
            start_date=start_date,
            end_date=end_date,
            campaigns=campaigns,
            agents=agents,
            languages=languages,
        )

        # Log cache status with filter info
        filter_info = []
        if start_date or end_date:
            filter_info.append(f"date:{start_date or 'all'}-{end_date or 'all'}")
        if campaigns:
            filter_info.append(f"campaigns:{campaigns}")
        if agents:
            filter_info.append(f"agents:{agents}")
        if languages:
            filter_info.append(f"languages:{languages}")

        filter_str = " with filters: " + ", ".join(filter_info) if filter_info else ""
        logger.info(f"Data summary retrieved (cached or computed){filter_str}")

        return {
            "success": True,
            "message": "Data summary retrieved successfully",
            "data": summary_data,
        }

    except Exception as e:
        logger.error(f"Error getting data summary: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


def reload_data(file_paths=None):
    """
    Reload data from specified files or default files

    Args:
        file_paths: List of file paths to load

    Returns:
        True if successful, False otherwise
    """
    global data_loader

    try:
        # If file paths are provided, use them
        if file_paths:
            data_loader = CallFlowDataLoader(file_paths)
        else:
            # Otherwise use default files
            data_loader = CallFlowDataLoader()

        # Load and preprocess data
        success = data_loader.load_data()
        if success:
            success = data_loader.preprocess_data()

        return success

    except Exception as e:
        logger.error(f"Error in reload_data: {str(e)}")
        return False


def get_data_loader():
    """
    Dependency to get the data loader

    Returns:
        CallFlowDataLoader instance
    """
    global data_loader

    # Ensure data is loaded
    if not hasattr(data_loader, "combined_data") or data_loader.combined_data is None:
        reload_data()

    return data_loader


def filter_data(df, request):
    """
    Filter DataFrame based on request parameters

    Args:
        df: DataFrame to filter
        request: Request object with filter parameters

    Returns:
        Filtered DataFrame
    """
    if df is None or df.empty:
        return df

    filtered_df = df.copy()

    # Filter by date range
    if (
        hasattr(request, "start_date")
        and request.start_date
        and "Call Date" in filtered_df.columns
    ):
        start_date = pd.to_datetime(request.start_date)
        filtered_df = filtered_df[filtered_df["Call Date"] >= start_date]

    if (
        hasattr(request, "end_date")
        and request.end_date
        and "Call Date" in filtered_df.columns
    ):
        end_date = pd.to_datetime(request.end_date)
        filtered_df = filtered_df[filtered_df["Call Date"] <= end_date]

    # Filter by campaigns
    if (
        hasattr(request, "campaigns")
        and request.campaigns
        and "Campaign" in filtered_df.columns
    ):
        filtered_df = filtered_df[filtered_df["Campaign"].isin(request.campaigns)]

    # Filter by agents
    if hasattr(request, "agents") and request.agents and "User" in filtered_df.columns:
        filtered_df = filtered_df[filtered_df["User"].isin(request.agents)]

    # Filter by languages
    if (
        hasattr(request, "languages")
        and request.languages
        and "Language" in filtered_df.columns
    ):
        filtered_df = filtered_df[filtered_df["Language"].isin(request.languages)]

    return filtered_df
