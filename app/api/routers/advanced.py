from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Response
from fastapi.responses import FileResponse
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
import logging
import os
import tempfile
from datetime import datetime, timedelta
import json

from app.api.models.schemas import (
    AnalysisRequest,
    AnalysisResponse,
    AnalysisResultWithCharts,
)
from app.api.routers.data import get_data_loader, filter_data
from app.api.services.ml_models import CallVolumeModel, AgentPerformanceModel
from app.api.services.report_generator import ReportGenerator
from app.shared.config import MODELS_DIR

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/advanced",
    tags=["advanced"],
    responses={404: {"description": "Not found"}},
)

# Create temporary directory for reports
os.makedirs(os.path.join(tempfile.gettempdir(), "call_flow_reports"), exist_ok=True)


# Define request and response schemas
class AdvancedCallVolumeRequest(AnalysisRequest):
    """Request schema for advanced call volume prediction"""

    granularity: str = "daily"
    model_type: str = "ensemble"
    periods: int = 14
    retrain: bool = False


class ModelTrainingRequest(AnalysisRequest):
    """Request schema for model training"""

    model_name: str
    model_type: str = "ensemble"
    parameters: Optional[Dict[str, Any]] = None


class ReportGenerationRequest(AnalysisRequest):
    """Request schema for report generation"""

    report_title: str
    report_type: str = "pdf"  # pdf or excel
    sections: List[str]
    include_charts: bool = True
    include_tables: bool = True


class ReportResponse(AnalysisResponse):
    """Response schema for report generation"""

    report_url: Optional[str] = None


@router.post("/call-volume-prediction", response_model=AnalysisResultWithCharts)
async def advanced_call_volume_prediction(
    request: AdvancedCallVolumeRequest, data_loader=Depends(get_data_loader)
):
    """
    Advanced call volume prediction using machine learning models
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request) for key, df in data.items() if not df.empty
        }

        # Combine all data for prediction
        df_combined = pd.DataFrame()
        for df in filtered_data.values():
            if not df.empty and "Call Date" in df.columns:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return {
                "success": False,
                "message": "No data available for call volume prediction",
                "data": None,
                "charts": [],
            }

        # Initialize model
        model = CallVolumeModel()

        # Train model if requested or if model doesn't exist
        if request.retrain or not os.path.exists(model.model_path):
            training_result = model.train(
                df_combined,
                date_col="Call Date",
                granularity=request.granularity,
                model_type=request.model_type,
            )

            if not training_result["success"]:
                error_message = training_result.get("message", "Unknown error")
                logger.error(f"Call volume model training failed: {error_message}")

                # Add more detailed information if available
                details = {}
                if "data_points" in training_result:
                    details["data_points"] = training_result["data_points"]
                if "features_used" in training_result:
                    details["features_used"] = training_result["features_used"]

                return {
                    "success": False,
                    "message": f"Model training failed: {error_message}",
                    "data": details if details else None,
                    "charts": [],
                }

            logger.info(f"Model trained successfully: {training_result['metrics']}")

        # Make predictions
        prediction_result = model.predict(
            df_combined,
            date_col="Call Date",
            granularity=request.granularity,
            periods=request.periods,
        )

        if not prediction_result["success"]:
            return {
                "success": False,
                "message": f"Prediction failed: {prediction_result.get('message', 'Unknown error')}",
                "data": None,
                "charts": [],
            }

        # Prepare result data
        forecast = prediction_result["forecast"]

        # Get historical data for comparison
        historical_data = model.aggregate_by_time(
            df_combined, date_col="Call Date", granularity=request.granularity
        )

        historical = (
            historical_data[["time_group", "call_count"]]
            .tail(request.periods)
            .to_dict(orient="records")
        )
        historical = [
            {
                "date": row["time_group"].strftime("%Y-%m-%d %H:%M:%S"),
                "call_volume": int(row["call_count"]),
            }
            for row in historical
        ]

        # Create chart data
        chart_data = {
            "chart_type": "line",
            "title": f"Call Volume Prediction ({request.granularity})",
            "x_label": "Date",
            "y_label": "Call Volume",
            "data": {
                "x": [item["date"] for item in historical]
                + [item["date"] for item in forecast],
                "y": [item["call_volume"] for item in historical]
                + [item["call_volume"] for item in forecast],
                "series": ["Historical"] * len(historical)
                + ["Forecast"] * len(forecast),
            },
        }

        return {
            "success": True,
            "message": "Advanced call volume prediction completed successfully",
            "data": {
                "historical": historical,
                "forecast": forecast,
                "granularity": request.granularity,
                "model_type": request.model_type,
            },
            "charts": [chart_data],
        }

    except Exception as e:
        logger.error(f"Error in advanced call volume prediction: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/train-model", response_model=AnalysisResponse)
async def train_model(
    request: ModelTrainingRequest, data_loader=Depends(get_data_loader)
):
    """
    Train a machine learning model
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request) for key, df in data.items() if not df.empty
        }

        # Combine all data for training
        df_combined = pd.DataFrame()
        for df in filtered_data.values():
            if not df.empty:
                df_combined = pd.concat([df_combined, df])

        if df_combined.empty:
            return {
                "success": False,
                "message": "No data available for model training",
                "data": None,
            }

        # Initialize model based on model name
        if request.model_name == "call_volume":
            model = CallVolumeModel()

            # Train model
            training_result = model.train(
                df_combined,
                date_col="Call Date",
                granularity=(
                    request.parameters.get("granularity", "daily")
                    if request.parameters
                    else "daily"
                ),
                model_type=request.model_type,
            )

        elif request.model_name == "agent_performance":
            model = AgentPerformanceModel()

            # Train model
            training_result = model.train(df_combined)

        else:
            return {
                "success": False,
                "message": f"Unknown model name: {request.model_name}",
                "data": None,
            }

        if not training_result["success"]:
            error_message = training_result.get("message", "Unknown error")
            logger.error(f"Model {request.model_name} training failed: {error_message}")

            # Add more detailed information if available
            details = {
                "model_name": request.model_name,
                "model_type": request.model_type,
            }

            if "data_points" in training_result:
                details["data_points"] = training_result["data_points"]
            if "features_used" in training_result:
                details["features_used"] = training_result["features_used"]

            return {
                "success": False,
                "message": f"Model training failed: {error_message}",
                "data": details,
            }

        return {
            "success": True,
            "message": f"Model {request.model_name} trained successfully",
            "data": {
                "model_name": request.model_name,
                "model_type": request.model_type,
                "metrics": training_result.get("metrics", {}),
            },
        }

    except Exception as e:
        logger.error(f"Error training model: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-report", response_model=ReportResponse)
async def generate_report(
    request: ReportGenerationRequest,
    background_tasks: BackgroundTasks,
    data_loader=Depends(get_data_loader),
):
    """
    Generate a custom report
    """
    try:
        # Get data
        data = data_loader.get_data("all")

        # Filter data
        filtered_data = {
            key: filter_data(df, request) for key, df in data.items() if not df.empty
        }

        # Initialize report generator
        report_generator = ReportGenerator(title=request.report_title)

        # Add metadata
        report_generator.add_metadata(
            "date_range",
            (
                f"{request.start_date} to {request.end_date}"
                if request.start_date and request.end_date
                else "All data"
            ),
        )

        # Generate report in background
        report_filename = f"call_flow_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{request.report_type}"
        report_path = os.path.join(
            tempfile.gettempdir(), "call_flow_reports", report_filename
        )

        background_tasks.add_task(
            generate_report_task,
            report_generator,
            filtered_data,
            request.sections,
            request.include_charts,
            request.include_tables,
            report_path,
            request.report_type,
        )

        # Return response with report URL
        return {
            "success": True,
            "message": f"Report generation started. The report will be available shortly.",
            "data": {
                "report_type": request.report_type,
                "report_title": request.report_title,
            },
            "report_url": f"/advanced/reports/{report_filename}",
        }

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/reports/{filename}")
async def get_report(filename: str):
    """
    Get a generated report
    """
    report_path = os.path.join(tempfile.gettempdir(), "call_flow_reports", filename)

    if not os.path.exists(report_path):
        raise HTTPException(status_code=404, detail="Report not found")

    return FileResponse(
        path=report_path,
        filename=filename,
        media_type=(
            "application/pdf"
            if filename.endswith(".pdf")
            else "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ),
    )


async def generate_report_task(
    report_generator: ReportGenerator,
    data: Dict[str, pd.DataFrame],
    sections: List[str],
    include_charts: bool,
    include_tables: bool,
    output_path: str,
    report_type: str,
):
    """
    Background task to generate a report

    Args:
        report_generator: ReportGenerator instance
        data: Dictionary of DataFrames
        sections: List of sections to include
        include_charts: Whether to include charts
        include_tables: Whether to include tables
        output_path: Path to save the report
        report_type: Report type (pdf or excel)
    """
    try:
        # Combine data for overall metrics
        df_combined = pd.DataFrame()
        for df in data.values():
            if not df.empty:
                df_combined = pd.concat([df_combined, df])

        # Add sections based on request
        for section_name in sections:
            if section_name == "overview":
                # Add overview section
                section_index = report_generator.add_section(
                    "Call Flow Overview", "Summary of call flow metrics"
                )

                # Add overview text
                total_calls = len(df_combined)
                date_range = (
                    f"{df_combined['Call Date'].min().strftime('%Y-%m-%d')} to {df_combined['Call Date'].max().strftime('%Y-%m-%d')}"
                    if "Call Date" in df_combined.columns and not df_combined.empty
                    else "N/A"
                )

                overview_text = f"""
                Total Calls: {total_calls}
                Date Range: {date_range}
                """

                report_generator.add_text(section_index, overview_text, "Key Metrics")

                # Add call type distribution table
                if "Call Type" in df_combined.columns and include_tables:
                    call_type_counts = (
                        df_combined["Call Type"].value_counts().reset_index()
                    )
                    call_type_counts.columns = ["Call Type", "Count"]

                    report_generator.add_table(
                        section_index,
                        call_type_counts,
                        "Call Type Distribution",
                        "Distribution of calls by type",
                    )

                # Add call volume by day chart
                if "Call Date" in df_combined.columns and include_charts:
                    df_combined["Date"] = df_combined["Call Date"].dt.date
                    daily_counts = (
                        df_combined.groupby("Date")
                        .size()
                        .reset_index(name="call_count")
                    )
                    daily_counts = daily_counts.sort_values("Date")

                    chart_data = {
                        "chart_type": "line",
                        "title": "Daily Call Volume",
                        "x_label": "Date",
                        "y_label": "Number of Calls",
                        "data": {
                            "x": [d.strftime("%Y-%m-%d") for d in daily_counts["Date"]],
                            "y": daily_counts["call_count"].tolist(),
                        },
                    }

                    report_generator.add_chart(
                        section_index,
                        chart_data,
                        "Daily Call Volume",
                        "Number of calls per day",
                    )

            elif section_name == "agent_performance":
                # Add agent performance section
                section_index = report_generator.add_section(
                    "Agent Performance", "Analysis of agent performance metrics"
                )

                if "User" in df_combined.columns:
                    # Add agent call volume table
                    agent_counts = (
                        df_combined.groupby("User")
                        .size()
                        .reset_index(name="call_count")
                    )
                    agent_counts = agent_counts.sort_values(
                        "call_count", ascending=False
                    )

                    if include_tables:
                        report_generator.add_table(
                            section_index,
                            agent_counts,
                            "Agent Call Volume",
                            "Number of calls handled by each agent",
                        )

                    # Add agent call volume chart
                    if include_charts:
                        chart_data = {
                            "chart_type": "bar",
                            "title": "Agent Call Volume",
                            "x_label": "Agent",
                            "y_label": "Number of Calls",
                            "data": {
                                "x": agent_counts["User"].tolist(),
                                "y": agent_counts["call_count"].tolist(),
                            },
                        }

                        report_generator.add_chart(
                            section_index,
                            chart_data,
                            "Agent Call Volume",
                            "Number of calls handled by each agent",
                        )

                    # Add agent performance metrics if Length is available
                    if "Length" in df_combined.columns:
                        agent_metrics = (
                            df_combined.groupby("User")
                            .agg({"Length": ["mean", "median", "count"]})
                            .reset_index()
                        )

                        agent_metrics.columns = [
                            "User",
                            "Avg Length",
                            "Median Length",
                            "Call Count",
                        ]
                        agent_metrics = agent_metrics.sort_values(
                            "Call Count", ascending=False
                        )

                        if include_tables:
                            report_generator.add_table(
                                section_index,
                                agent_metrics,
                                "Agent Performance Metrics",
                                "Performance metrics for each agent",
                            )

            # Add more sections as needed

        # Generate report based on type
        if report_type == "pdf":
            report_generator.generate_pdf(output_path)
        else:  # excel
            report_generator.generate_excel(output_path)

        logger.info(f"Report generated successfully: {output_path}")

    except Exception as e:
        logger.error(f"Error in report generation task: {str(e)}")
        # Create error report
        with open(output_path, "w") as f:
            f.write(f"Error generating report: {str(e)}")
