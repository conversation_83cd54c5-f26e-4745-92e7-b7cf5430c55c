from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any, List, Optional
import logging
from app.celery_app import celery_app, get_task_info, get_all_tasks

# Import tasks module to ensure tasks are registered
import app.tasks
from app.api.models.schemas import AnalysisResponse
from pydantic import BaseModel

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/tasks",
    tags=["tasks"],
    responses={404: {"description": "Not found"}},
)


# Add a simple test endpoint to verify the router is working
@router.get("/test")
async def test_tasks_router():
    """Test endpoint to verify the tasks router is working"""
    return {"status": "ok", "message": "Tasks router is working"}


# Define request and response schemas
class ModelTrainingTaskRequest(BaseModel):
    """Request schema for model training task"""

    model_name: str
    model_type: str = "ensemble"
    granularity: Optional[str] = "daily"
    filter_params: Optional[Dict[str, Any]] = None


class TaskResponse(AnalysisResponse):
    """Response schema for task operations"""

    task_id: Optional[str] = None


class TaskInfoResponse(BaseModel):
    """Response schema for task info"""

    task_id: str
    status: str
    progress: int
    message: str
    result: Optional[Dict[str, Any]] = None
    created_at: Optional[str] = None
    started_at: Optional[str] = None
    completed_at: Optional[str] = None


class TaskListResponse(BaseModel):
    """Response schema for task list"""

    tasks: List[TaskInfoResponse]
    count: int


# Define the endpoint with both hyphen and underscore versions for compatibility
@router.post("/train-model", response_model=TaskResponse)
@router.post(
    "/train_model", response_model=TaskResponse
)  # Add underscore version as well
async def start_model_training(request: ModelTrainingTaskRequest):
    """
    Start a background task to train a model
    """
    try:
        # Log the request for debugging
        logger.info(f"Model training request: {request.dict()}")

        # Check if Celery is available
        try:
            # Try to ping Celery
            celery_app.control.ping(timeout=1)
            logger.info("Celery is available")
        except Exception as celery_error:
            logger.error(f"Celery ping failed: {str(celery_error)}")
            return {
                "success": False,
                "message": f"Celery is not available: {str(celery_error)}",
                "data": None,
            }

        if request.model_name == "call_volume":
            # Prepare task parameters
            task_kwargs = {
                "date_col": "Call Date",
                "granularity": request.granularity,
                "model_type": request.model_type,
                "filter_params": request.filter_params,
            }

            logger.info(f"Sending task with parameters: {task_kwargs}")

            # Start call volume model training task
            try:
                task = celery_app.send_task(
                    "train_call_volume_model",
                    kwargs=task_kwargs,
                )

                logger.info(f"Started call volume model training task: {task.id}")

                return {
                    "success": True,
                    "message": f"Model training task started",
                    "data": {
                        "model_name": request.model_name,
                        "model_type": request.model_type,
                        "granularity": request.granularity,
                    },
                    "task_id": task.id,
                }
            except Exception as task_error:
                logger.error(f"Failed to send task: {str(task_error)}")
                return {
                    "success": False,
                    "message": f"Failed to send task: {str(task_error)}",
                    "data": None,
                }

        elif request.model_name == "agent_performance":
            # Prepare task parameters
            task_kwargs = {
                "filter_params": request.filter_params,
            }

            logger.info(
                f"Sending agent performance task with parameters: {task_kwargs}"
            )

            # Start agent performance model training task
            try:
                task = celery_app.send_task(
                    "train_agent_performance_model",
                    kwargs=task_kwargs,
                )

                logger.info(f"Started agent performance model training task: {task.id}")

                return {
                    "success": True,
                    "message": f"Model training task started",
                    "data": {
                        "model_name": request.model_name,
                        "model_type": request.model_type,
                    },
                    "task_id": task.id,
                }
            except Exception as task_error:
                logger.error(
                    f"Failed to send agent performance task: {str(task_error)}"
                )
                return {
                    "success": False,
                    "message": f"Failed to send task: {str(task_error)}",
                    "data": None,
                }

        else:
            return {
                "success": False,
                "message": f"Unknown model name: {request.model_name}",
                "data": None,
            }

    except Exception as e:
        logger.error(f"Error starting model training task: {str(e)}")
        import traceback

        logger.error(f"Traceback: {traceback.format_exc()}")

        # Return a more detailed error response instead of raising an exception
        return {
            "success": False,
            "message": f"Server error: {str(e)}",
            "data": {
                "error_type": type(e).__name__,
                "error_details": str(e),
            },
        }


@router.get("/status/{task_id}", response_model=TaskInfoResponse)
async def get_task_status(task_id: str):
    """
    Get the status of a task
    """
    try:
        task_info = get_task_info(task_id)

        if not task_info:
            # Try to get from Celery
            task = celery_app.AsyncResult(task_id)

            if task.state == "PENDING":
                task_info = {
                    "status": "PENDING",
                    "progress": 0,
                    "message": "Task is pending",
                    "result": None,
                }
            elif task.state == "STARTED":
                task_info = {
                    "status": "RUNNING",
                    "progress": 0,
                    "message": "Task is running",
                    "result": None,
                }
            elif task.state == "SUCCESS":
                task_info = {
                    "status": "SUCCESS",
                    "progress": 100,
                    "message": "Task completed successfully",
                    "result": task.result,
                }
            elif task.state == "FAILURE":
                task_info = {
                    "status": "FAILURE",
                    "progress": 100,
                    "message": f"Task failed: {str(task.result)}",
                    "result": None,
                }
            else:
                task_info = {
                    "status": task.state,
                    "progress": 0,
                    "message": f"Task is in state {task.state}",
                    "result": None,
                }

        if not task_info:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

        # Add task_id to response
        task_info["task_id"] = task_id

        return task_info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=TaskListResponse)
async def list_tasks(task_type: Optional[str] = None, status: Optional[str] = None):
    """
    List all tasks
    """
    try:
        tasks = get_all_tasks()

        # Convert to list of TaskInfoResponse
        task_list = []
        for task_id, task_info in tasks.items():
            # Add task_id to task_info
            task_info_copy = task_info.copy()
            task_info_copy["task_id"] = task_id
            task_list.append(task_info_copy)

        # Filter by task_type if provided
        if task_type:
            task_list = [
                task for task in task_list if task.get("task_type") == task_type
            ]

        # Filter by status if provided
        if status:
            task_list = [task for task in task_list if task.get("status") == status]

        return {"tasks": task_list, "count": len(task_list)}

    except Exception as e:
        logger.error(f"Error listing tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
