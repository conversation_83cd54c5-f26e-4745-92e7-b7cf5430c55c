import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
import logging
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CallFlowPreprocessor:
    """
    Class for preprocessing call flow data for analytics and ML models
    """
    
    def __init__(self):
        """Initialize the preprocessor"""
        self.numeric_features = ['Length', 'Queue Time']
        self.categorical_features = ['Campaign', 'Status', 'User', 'Language']
        self.datetime_features = ['Call Date']
        self.preprocessor = None
        
    def fit_transform(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Fit the preprocessor and transform the data
        
        Args:
            df: DataFrame to preprocess
            
        Returns:
            Preprocessed DataFrame
        """
        try:
            # Filter features that exist in the dataframe
            numeric_features = [f for f in self.numeric_features if f in df.columns]
            categorical_features = [f for f in self.categorical_features if f in df.columns]
            
            # Create preprocessing pipelines
            numeric_transformer = Pipeline(steps=[
                ('imputer', SimpleImputer(strategy='median')),
                ('scaler', StandardScaler())
            ])
            
            categorical_transformer = Pipeline(steps=[
                ('imputer', SimpleImputer(strategy='constant', fill_value='missing')),
                ('onehot', OneHotEncoder(handle_unknown='ignore'))
            ])
            
            # Combine preprocessing steps
            preprocessor = ColumnTransformer(
                transformers=[
                    ('num', numeric_transformer, numeric_features),
                    ('cat', categorical_transformer, categorical_features)
                ],
                remainder='passthrough'
            )
            
            # Fit and transform
            self.preprocessor = preprocessor
            transformed_array = preprocessor.fit_transform(df)
            
            # Get feature names
            numeric_cols = numeric_features
            
            # Get categorical column names after one-hot encoding
            if categorical_features:
                cat_encoder = preprocessor.named_transformers_['cat'].named_steps['onehot']
                categorical_cols = []
                for i, col in enumerate(categorical_features):
                    categories = cat_encoder.categories_[i]
                    categorical_cols.extend([f"{col}_{cat}" for cat in categories])
            else:
                categorical_cols = []
            
            # Get passthrough column names
            passthrough_cols = [col for col in df.columns 
                               if col not in numeric_features + categorical_features]
            
            # Combine all column names
            all_cols = numeric_cols + categorical_cols + passthrough_cols
            
            # Convert to DataFrame
            if isinstance(transformed_array, np.ndarray):
                # If it's a dense array
                transformed_df = pd.DataFrame(
                    transformed_array, 
                    index=df.index,
                    columns=all_cols[:transformed_array.shape[1]]  # Ensure we don't exceed array dimensions
                )
            else:
                # If it's a sparse array
                transformed_df = pd.DataFrame(
                    transformed_array.toarray(), 
                    index=df.index,
                    columns=all_cols[:transformed_array.toarray().shape[1]]
                )
            
            return transformed_df
        
        except Exception as e:
            logger.error(f"Error in preprocessing: {str(e)}")
            # Return original dataframe if preprocessing fails
            return df
    
    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Transform data using the fitted preprocessor
        
        Args:
            df: DataFrame to transform
            
        Returns:
            Transformed DataFrame
        """
        if self.preprocessor is None:
            logger.warning("Preprocessor not fitted. Fitting now.")
            return self.fit_transform(df)
        
        try:
            transformed_array = self.preprocessor.transform(df)
            
            # Get feature names (simplified for transform-only)
            all_cols = [f"feature_{i}" for i in range(transformed_array.shape[1])]
            
            # Convert to DataFrame
            if isinstance(transformed_array, np.ndarray):
                transformed_df = pd.DataFrame(transformed_array, index=df.index, columns=all_cols)
            else:
                transformed_df = pd.DataFrame(transformed_array.toarray(), index=df.index, columns=all_cols)
            
            return transformed_df
        
        except Exception as e:
            logger.error(f"Error in transform: {str(e)}")
            return df
    
    @staticmethod
    def extract_time_features(df: pd.DataFrame, date_col: str = 'Call Date') -> pd.DataFrame:
        """
        Extract time-based features from a date column
        
        Args:
            df: DataFrame containing the date column
            date_col: Name of the date column
            
        Returns:
            DataFrame with additional time features
        """
        if date_col not in df.columns:
            return df
        
        try:
            # Create a copy to avoid modifying the original
            result_df = df.copy()
            
            # Convert to datetime if not already
            if not pd.api.types.is_datetime64_any_dtype(result_df[date_col]):
                result_df[date_col] = pd.to_datetime(result_df[date_col], errors='coerce')
            
            # Extract features
            result_df['hour'] = result_df[date_col].dt.hour
            result_df['day'] = result_df[date_col].dt.day
            result_df['weekday'] = result_df[date_col].dt.dayofweek
            result_df['weekday_name'] = result_df[date_col].dt.day_name()
            result_df['month'] = result_df[date_col].dt.month
            result_df['year'] = result_df[date_col].dt.year
            result_df['is_weekend'] = result_df['weekday'].isin([5, 6]).astype(int)
            
            # Time of day categories
            result_df['time_of_day'] = pd.cut(
                result_df['hour'],
                bins=[0, 6, 12, 18, 24],
                labels=['Night', 'Morning', 'Afternoon', 'Evening'],
                include_lowest=True
            )
            
            return result_df
        
        except Exception as e:
            logger.error(f"Error extracting time features: {str(e)}")
            return df
    
    @staticmethod
    def detect_language(df: pd.DataFrame, campaign_col: str = 'Campaign') -> pd.DataFrame:
        """
        Detect language based on campaign name
        
        Args:
            df: DataFrame containing the campaign column
            campaign_col: Name of the campaign column
            
        Returns:
            DataFrame with additional language column
        """
        if campaign_col not in df.columns:
            return df
        
        try:
            result_df = df.copy()
            
            # Simple rule-based language detection
            result_df['language'] = result_df[campaign_col].apply(
                lambda x: 'Arabic' if 'Arabic' in str(x) else 'English'
            )
            
            return result_df
        
        except Exception as e:
            logger.error(f"Error detecting language: {str(e)}")
            return df
