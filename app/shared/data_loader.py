import os
import pandas as pd
from typing import Dict, List, Optional, Union
import logging
from .config import SHEET_MAP, DEFAULT_EXCEL_FILES

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CallFlowDataLoader:
    """
    Class to load and process call flow data from Excel files
    """

    def __init__(self, file_paths: Optional[List[str]] = None):
        """
        Initialize the data loader with file paths

        Args:
            file_paths: List of Excel file paths to load. If None, uses default files.
        """
        self.file_paths = file_paths or DEFAULT_EXCEL_FILES
        self.inbound_data = []
        self.missed_data = []
        self.outbound_data = []
        self.combined_data = None

    def load_relevant_sheets(
        self, file_path: str, sheet_aliases: Dict[str, str]
    ) -> Dict[str, pd.DataFrame]:
        """
        Load relevant sheets from an Excel file

        Args:
            file_path: Path to the Excel file
            sheet_aliases: Dictionary mapping sheet names to normalized names

        Returns:
            Dictionary of DataFrames with normalized sheet names as keys
        """
        try:
            xls = pd.ExcelFile(file_path)
            data = {}

            for sheet in xls.sheet_names:
                if sheet in sheet_aliases:
                    df = xls.parse(sheet)
                    df["Source File"] = os.path.basename(file_path)
                    df["Call Type"] = sheet_aliases[sheet]
                    data[sheet_aliases[sheet]] = df

            return data
        except Exception as e:
            logger.error(f"Error loading {file_path}: {str(e)}")
            return {}

    def load_data(self) -> bool:
        """
        Load data from all specified Excel files

        Returns:
            True if data was loaded successfully, False otherwise
        """
        try:
            self.inbound_data = []
            self.missed_data = []
            self.outbound_data = []

            for path in self.file_paths:
                if not os.path.exists(path):
                    logger.warning(f"File not found: {path}")
                    continue

                sheets = self.load_relevant_sheets(path, SHEET_MAP)

                if "Inbound" in sheets:
                    self.inbound_data.append(sheets["Inbound"])
                if "Missed Call" in sheets:
                    self.missed_data.append(sheets["Missed Call"])
                if "Outbound" in sheets:
                    self.outbound_data.append(sheets["Outbound"])

            # Check if we have data
            if (
                not self.inbound_data
                and not self.missed_data
                and not self.outbound_data
            ):
                logger.error("No data was loaded from any files")
                return False

            return True
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return False

    def preprocess_data(self) -> bool:
        """
        Preprocess the loaded data

        Returns:
            True if preprocessing was successful, False otherwise
        """
        try:
            # Initialize empty dataframes
            self.df_inbound = pd.DataFrame()
            self.df_missed = pd.DataFrame()
            self.df_outbound = pd.DataFrame()
            self.combined_data = pd.DataFrame()

            # If no data was loaded, return early but still consider it a success
            if (
                not self.inbound_data
                and not self.missed_data
                and not self.outbound_data
            ):
                logger.warning(
                    "No data to preprocess. Please upload Excel files first."
                )
                return True

            # Combine data if available
            if self.inbound_data:
                self.df_inbound = pd.concat(self.inbound_data, ignore_index=True)

            if self.missed_data:
                self.df_missed = pd.concat(self.missed_data, ignore_index=True)

            if self.outbound_data:
                self.df_outbound = pd.concat(self.outbound_data, ignore_index=True)

            # Process each dataframe
            for df_name in ["df_inbound", "df_missed", "df_outbound"]:
                if (
                    hasattr(self, df_name)
                    and getattr(self, df_name) is not None
                    and not getattr(self, df_name).empty
                ):
                    df = getattr(self, df_name)

                    # Convert date columns
                    if "Call Date" in df.columns:
                        df["Call Date"] = pd.to_datetime(
                            df["Call Date"], errors="coerce"
                        )
                        df["Hour"] = df["Call Date"].dt.hour
                        df["Weekday"] = df["Call Date"].dt.day_name()
                        df["Date"] = df["Call Date"].dt.date
                        df["Month"] = df["Call Date"].dt.month
                        df["Year"] = df["Call Date"].dt.year
                        df["Day"] = df["Call Date"].dt.day

                    # Detect language based on campaign name
                    if "Campaign" in df.columns:
                        df["Language"] = df["Campaign"].apply(
                            lambda x: "Arabic" if "Arabic" in str(x) else "English"
                        )

                    # Set the processed dataframe back
                    setattr(self, df_name, df)

            # Create combined dataset for cross-analysis
            dfs_to_combine = []
            if (
                hasattr(self, "df_inbound")
                and self.df_inbound is not None
                and not self.df_inbound.empty
            ):
                dfs_to_combine.append(self.df_inbound)
            if (
                hasattr(self, "df_missed")
                and self.df_missed is not None
                and not self.df_missed.empty
            ):
                dfs_to_combine.append(self.df_missed)
            if (
                hasattr(self, "df_outbound")
                and self.df_outbound is not None
                and not self.df_outbound.empty
            ):
                dfs_to_combine.append(self.df_outbound)

            if dfs_to_combine:
                self.combined_data = pd.concat(dfs_to_combine, ignore_index=True)

            return True
        except Exception as e:
            logger.error(f"Error preprocessing data: {str(e)}")
            return False

    def get_data(
        self, data_type: str = "all"
    ) -> Union[pd.DataFrame, Dict[str, pd.DataFrame]]:
        """
        Get the processed data

        Args:
            data_type: Type of data to return ('inbound', 'missed', 'outbound', or 'all')

        Returns:
            DataFrame or dictionary of DataFrames
        """
        if data_type.lower() == "inbound" and hasattr(self, "df_inbound"):
            return self.df_inbound
        elif data_type.lower() == "missed" and hasattr(self, "df_missed"):
            return self.df_missed
        elif data_type.lower() == "outbound" and hasattr(self, "df_outbound"):
            return self.df_outbound
        elif data_type.lower() == "combined" and hasattr(self, "combined_data"):
            return self.combined_data
        elif data_type.lower() == "all":
            return {
                "inbound": getattr(self, "df_inbound", pd.DataFrame()),
                "missed": getattr(self, "df_missed", pd.DataFrame()),
                "outbound": getattr(self, "df_outbound", pd.DataFrame()),
                "combined": getattr(self, "combined_data", pd.DataFrame()),
            }
        else:
            return pd.DataFrame()
