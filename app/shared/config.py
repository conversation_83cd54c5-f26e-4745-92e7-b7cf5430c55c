import os
from pathlib import Path

# Base paths
BASE_DIR = Path(__file__).resolve().parent.parent.parent
DATA_DIR = os.path.join(BASE_DIR, "data")
MODELS_DIR = os.path.join(BASE_DIR, "models")

# Ensure directories exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(MODELS_DIR, exist_ok=True)

# Default Excel files - look for Excel files in the data directory
DEFAULT_EXCEL_FILES = (
    [
        os.path.join(DATA_DIR, f)
        for f in os.listdir(DATA_DIR)
        if f.endswith((".xlsx", ".xls"))
    ]
    if os.path.exists(DATA_DIR) and os.listdir(DATA_DIR)
    else []
)

# Sheet normalization map
SHEET_MAP = {
    "Inbound": "Inbound",
    "Missed Call": "Missed Call",
    "Missed call": "Missed Call",
    "missed call": "Missed Call",
    "Outbound": "Outbound",
}

# API settings
API_HOST = os.getenv("API_HOST", "127.0.0.1")
API_PORT = int(os.getenv("API_PORT", "8002"))  # Using port 8002 to match run.sh
API_URL = f"http://{API_HOST}:{API_PORT}"

# Languages
SUPPORTED_LANGUAGES = ["English", "Arabic"]

# Campaign categories
CAMPAIGN_CATEGORIES = {
    "Transaction_English": "Transaction",
    "GeneralArabic": "General",
    "TechSupp": "Technical Support",
    "ONLINE": "Online",
    "AGENTDIRECT": "Agent Direct",
}

# Model settings
MODEL_VERSION = "v1"
