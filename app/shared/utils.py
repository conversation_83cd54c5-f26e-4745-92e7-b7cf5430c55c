"""
Utility functions for the Call Flow Analytics application
"""

import pandas as pd
import numpy as np
import json
import os
import time
from typing import Any, Dict, List, Union, Callable
from app.shared.config import DATA_DIR


def clean_nan_for_json(obj: Any) -> Any:
    """
    Recursively clean NaN, Infinity, and -Infinity values in an object to make it JSON serializable.

    Args:
        obj: The object to clean

    Returns:
        The cleaned object
    """
    if isinstance(obj, dict):
        return {k: clean_nan_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [clean_nan_for_json(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(clean_nan_for_json(item) for item in obj)
    elif isinstance(obj, (pd.DataFrame, pd.Series)):
        return clean_nan_for_json(obj.to_dict())
    elif isinstance(obj, np.ndarray):
        return clean_nan_for_json(obj.tolist())
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64, np.float32)):
        if np.isnan(obj) or np.isinf(obj):
            return None
        return float(obj)
    elif pd.isna(obj) or (isinstance(obj, float) and (np.isnan(obj) or np.isinf(obj))):
        return None
    else:
        return obj


class NaNHandlingJSONEncoder(json.JSONEncoder):
    """
    Custom JSON encoder that handles NaN, Infinity, and -Infinity values.
    """

    def default(self, obj):
        if isinstance(obj, (np.integer, np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32)):
            if np.isnan(obj) or np.isinf(obj):
                return None
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        return super().default(obj)


def get_cached_result(
    cache_name: str, compute_function: Callable, *args, **kwargs
) -> Any:
    """
    Simple file-based caching function

    Args:
        cache_name: Name of the cache file
        compute_function: Function to compute the result if cache is invalid
        *args, **kwargs: Arguments to pass to compute_function

    Returns:
        The cached or newly computed result
    """
    # Create cache directory if it doesn't exist
    cache_dir = os.path.join(DATA_DIR, "cache")
    os.makedirs(cache_dir, exist_ok=True)

    # Create a cache key that includes the parameters
    params_str = ""
    if kwargs:
        # Sort the kwargs by key to ensure consistent cache keys
        sorted_kwargs = sorted(kwargs.items())
        params_str = "_".join(f"{k}={v}" for k, v in sorted_kwargs)

    # Create a cache filename that includes the parameters
    cache_filename = f"{cache_name}{('_' + params_str) if params_str else ''}.json"
    cache_filename = cache_filename.replace("/", "_").replace(
        "\\", "_"
    )  # Sanitize filename

    # Cache file path
    cache_file = os.path.join(cache_dir, cache_filename)

    # Get the latest modification time of data files
    data_files = [
        os.path.join(DATA_DIR, f)
        for f in os.listdir(DATA_DIR)
        if f.endswith((".xlsx", ".xls"))
    ]

    if not data_files:
        # No data files, just compute the result
        return compute_function(*args, **kwargs)

    latest_mod_time = max(os.path.getmtime(f) for f in data_files)

    # Check if cache exists and is newer than the latest data file
    if os.path.exists(cache_file) and os.path.getmtime(cache_file) > latest_mod_time:
        try:
            with open(cache_file, "r") as f:
                return json.load(f)
        except Exception as e:
            print(f"Error reading cache: {str(e)}")
            # If there's any error reading the cache, recompute
            pass

    # Cache doesn't exist or is outdated, compute the result
    result = compute_function(*args, **kwargs)

    # Save to cache
    try:
        with open(cache_file, "w") as f:
            json.dump(result, f)
    except Exception as e:
        print(f"Error saving cache: {str(e)}")

    return result
