# Import the page config first to ensure it's set before any other Streamlit code
import streamlit as st


# Set page config directly here instead of importing from another module
def setup_page_config():
    """Set up the page configuration for Streamlit"""
    # Try to set the page config, but catch the exception if it's already set
    try:
        st.set_page_config(
            page_title="Call Flow Analytics",
            page_icon="📞",
            layout="wide",
            initial_sidebar_state="expanded",
        )
    except Exception:
        # If the config is already set, this will raise an exception
        # We can safely ignore it
        pass


# Call the function to set up the page config
setup_page_config()

# Now import other modules
# streamlit is already imported above
import pandas as pd
import numpy as np
import requests
import json
import os
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import io

# Try different import approaches
try:
    # First try the standard import
    from app.shared.config import API_URL
except ImportError:
    try:
        # Try relative import
        import sys
        import os

        # Add project root to path
        sys.path.insert(
            0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
        )
        from app.shared.config import API_URL
    except ImportError:
        # Fallback to direct import
        import sys
        import os

        # Get the absolute path to the shared config
        shared_dir = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "../shared")
        )
        sys.path.insert(0, shared_dir)
        try:
            from config import API_URL
        except ImportError:
            # Final fallback to hardcoded value
            API_URL = "http://localhost:8002"

# Define API endpoints
API_ENDPOINTS = {
    "upload_files": f"{API_URL}/data/upload",
    "list_files": f"{API_URL}/data/files",
    "data_summary": f"{API_URL}/data/summary",
    "agent_performance": f"{API_URL}/analytics/agent-performance",
    "call_volume_prediction": f"{API_URL}/analytics/call-volume-prediction",
    "staffing_optimization": f"{API_URL}/analytics/staffing-optimization",
    "campaign_analysis": f"{API_URL}/analytics/campaign-analysis",
    "language_analysis": f"{API_URL}/analytics/language-analysis",
    "call_analytics": f"{API_URL}/analytics/call-analytics",
    "enhanced_missed_call": f"{API_URL}/analytics/enhanced-missed-call",
    "time_series_forecast": f"{API_URL}/analytics/time-series-forecast",
    "agent_scheduling": f"{API_URL}/analytics/agent-scheduling",
    "comprehensive_analytics": f"{API_URL}/analytics/comprehensive-analytics",
    # Add other endpoints as needed
}


# Helper functions
def make_api_request(endpoint, method="GET", data=None, files=None, params=None):
    """
    Make a request to the API

    Args:
        endpoint: API endpoint
        method: HTTP method
        data: Request data
        files: Files to upload
        params: Query parameters for GET requests

    Returns:
        API response
    """
    try:
        if method.upper() == "GET":
            response = requests.get(endpoint, params=params)
        elif method.upper() == "POST":
            if files:
                response = requests.post(endpoint, files=files)
            else:
                response = requests.post(endpoint, json=data)
        else:
            st.error(f"Unsupported method: {method}")
            return None

        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"API Error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        st.error(f"Error making API request: {str(e)}")
        return None


def render_chart(chart_data):
    """
    Render a chart based on chart data

    Args:
        chart_data: Chart data from API
    """
    chart_type = chart_data.get("chart_type", "")
    title = chart_data.get("title", "")
    x_label = chart_data.get("x_label", "")
    y_label = chart_data.get("y_label", "")
    data = chart_data.get("data", {})

    if not data:
        st.warning("No data available for chart")
        return

    if chart_type == "bar":
        fig = px.bar(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "line":
        if "series" in data:
            # Multiple series line chart
            df = pd.DataFrame(
                {
                    "x": data.get("x", []),
                    "y": data.get("y", []),
                    "series": data.get("series", []),
                }
            )
            fig = px.line(
                df,
                x="x",
                y="y",
                color="series",
                title=title,
                labels={"x": x_label, "y": y_label, "series": "Series"},
            )
        else:
            # Single series line chart
            fig = px.line(
                x=data.get("x", []),
                y=data.get("y", []),
                title=title,
                labels={"x": x_label, "y": y_label},
            )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "pie":
        fig = px.pie(
            names=data.get("labels", []), values=data.get("values", []), title=title
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "scatter":
        fig = px.scatter(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "heatmap":
        fig = px.imshow(
            data.get("z", []),
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    else:
        st.warning(f"Unsupported chart type: {chart_type}")


# Sidebar navigation
st.sidebar.title("Call Flow Analytics")
st.sidebar.image("https://img.icons8.com/color/96/000000/phone-office.png", width=100)

# Add quick access to popular features
st.sidebar.markdown("---")
st.sidebar.subheader("🚀 Quick Access")

# Use columns for better layout
col1, col2 = st.sidebar.columns(2)

with col1:
    if st.button("📊 Analytics", key="quick_analytics", use_container_width=True):
        st.session_state.page = "Comprehensive Analytics"
        st.rerun()

    if st.button("📈 Forecast", key="quick_forecast", use_container_width=True):
        st.session_state.page = "Call Volume Forecasting"
        st.rerun()

with col2:
    if st.button("👥 Agents", key="quick_agents", use_container_width=True):
        st.session_state.page = "Agent Performance"
        st.rerun()

    if st.button("📞 Missed", key="quick_missed", use_container_width=True):
        st.session_state.page = "Missed Call Dashboard"
        st.rerun()

# Main navigation
# Use session state to remember the current page
if "page" not in st.session_state:
    st.session_state.page = "Dashboard"

# Create the page selection dropdown
page = st.sidebar.selectbox(
    "📋 Select Analysis",
    [
        "Dashboard",
        "Data Upload",
        "Agent Performance",
        "Call Volume Prediction",
        "Staffing Optimization",
        "Campaign Analysis",
        "Language Analysis",
        "Missed Call Analysis",
        "Customer Journey",
        "First Call Resolution",
        "Sentiment Analysis",
        "Agent Specialization",
        "Conversion Rate Optimization",
        "Queue Optimization",
        "Seasonal Trend Analysis",
        "Geographic Insights",
        "Call Quality Scoring",
        "A/B Testing Framework",
        "Churn Prediction",
        "Cross-selling Opportunities",
        "Anomaly Detection",
        "Callback Optimization",
        "Knowledge Base Enhancement",
        "Comprehensive Call Analytics",
        "Comprehensive Analytics",
        "Missed Call Dashboard",
        "Call Volume Forecasting",
        "Agent Scheduling Model",
        "Advanced ML Models",
        "Model Training",
        "Task Monitor",
        "Custom Reports",
    ],
    index=(
        [
            "Dashboard",
            "Data Upload",
            "Agent Performance",
            "Call Volume Prediction",
            "Staffing Optimization",
            "Campaign Analysis",
            "Language Analysis",
            "Missed Call Analysis",
            "Customer Journey",
            "First Call Resolution",
            "Sentiment Analysis",
            "Agent Specialization",
            "Conversion Rate Optimization",
            "Queue Optimization",
            "Seasonal Trend Analysis",
            "Geographic Insights",
            "Call Quality Scoring",
            "A/B Testing Framework",
            "Churn Prediction",
            "Cross-selling Opportunities",
            "Anomaly Detection",
            "Callback Optimization",
            "Knowledge Base Enhancement",
            "Comprehensive Call Analytics",
            "Missed Call Dashboard",
            "Call Volume Forecasting",
            "Agent Scheduling Model",
            "Advanced ML Models",
            "Model Training",
            "Task Monitor",
            "Custom Reports",
        ].index(st.session_state.page)
        if st.session_state.page
        in [
            "Dashboard",
            "Data Upload",
            "Agent Performance",
            "Call Volume Prediction",
            "Staffing Optimization",
            "Campaign Analysis",
            "Language Analysis",
            "Missed Call Analysis",
            "Customer Journey",
            "First Call Resolution",
            "Sentiment Analysis",
            "Agent Specialization",
            "Conversion Rate Optimization",
            "Queue Optimization",
            "Seasonal Trend Analysis",
            "Geographic Insights",
            "Call Quality Scoring",
            "A/B Testing Framework",
            "Churn Prediction",
            "Cross-selling Opportunities",
            "Anomaly Detection",
            "Callback Optimization",
            "Knowledge Base Enhancement",
            "Comprehensive Call Analytics",
            "Comprehensive Analytics",
            "Missed Call Dashboard",
            "Call Volume Forecasting",
            "Agent Scheduling Model",
            "Advanced ML Models",
            "Model Training",
            "Task Monitor",
            "Custom Reports",
        ]
        else 0
    ),
)

# Update session state with the selected page (skip separator lines)
if not page.startswith("---"):
    st.session_state.page = page
else:
    # If a separator line is selected, default to Dashboard
    page = "Dashboard"
    st.session_state.page = page

# Dashboard page
if page == "Dashboard":
    st.title("Call Flow Analytics Dashboard")
    st.write(
        "Welcome to the Call Flow Analytics Dashboard. Use the sidebar to navigate to different analyses."
    )

    # Add filters for the dashboard
    with st.expander("Filter Dashboard Data", expanded=False):
        filter_col1, filter_col2 = st.columns(2)

        # Date range
        with filter_col1:
            start_date = st.date_input(
                "Start Date", datetime.now() - timedelta(days=30)
            )
            end_date = st.date_input("End Date", datetime.now())

        # Campaigns and languages
        with filter_col2:
            campaigns = st.multiselect(
                "Filter by Campaigns",
                [
                    "Transaction_English",
                    "GeneralArabic",
                    "TechSupp",
                    "ONLINE",
                    "AGENTDIRECT",
                ],
            )
            languages = st.multiselect("Filter by Languages", ["English", "Arabic"])

        # Apply filters button
        apply_filters = st.button("Apply Filters")

        # Store filter state
        if apply_filters:
            st.session_state.dashboard_filters = {
                "start_date": start_date.strftime("%Y-%m-%d") if start_date else None,
                "end_date": end_date.strftime("%Y-%m-%d") if end_date else None,
                "campaigns": ",".join(campaigns) if campaigns else None,
                "languages": ",".join(languages) if languages else None,
            }

        # Clear filters button
        if st.button("Clear Filters"):
            st.session_state.dashboard_filters = {}
            st.rerun()

    # Initialize filters in session state if not present
    if "dashboard_filters" not in st.session_state:
        st.session_state.dashboard_filters = {}

    # Get data summary with filters
    summary_response = make_api_request(
        API_ENDPOINTS["data_summary"], params=st.session_state.dashboard_filters
    )

    # Show active filters if any
    if any(st.session_state.dashboard_filters.values()):
        filter_info = []
        if st.session_state.dashboard_filters.get(
            "start_date"
        ) or st.session_state.dashboard_filters.get("end_date"):
            date_range = f"{st.session_state.dashboard_filters.get('start_date', 'all')} to {st.session_state.dashboard_filters.get('end_date', 'all')}"
            filter_info.append(f"Date Range: {date_range}")
        if st.session_state.dashboard_filters.get("campaigns"):
            filter_info.append(
                f"Campaigns: {st.session_state.dashboard_filters.get('campaigns')}"
            )
        if st.session_state.dashboard_filters.get("languages"):
            filter_info.append(
                f"Languages: {st.session_state.dashboard_filters.get('languages')}"
            )

        st.info("Active Filters: " + " | ".join(filter_info))

    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})

        if summary_data:
            st.subheader("Data Overview")

            # Create columns for metrics
            col1, col2, col3 = st.columns(3)

            # Total calls
            total_calls = sum(
                summary.get("rows", 0) for summary in summary_data.values()
            )
            col1.metric("Total Calls", f"{total_calls:,}")

            # Inbound calls
            inbound_calls = summary_data.get("inbound", {}).get("rows", 0)
            col2.metric("Inbound Calls", f"{inbound_calls:,}")

            # Outbound calls
            outbound_calls = summary_data.get("outbound", {}).get("rows", 0)
            col3.metric("Outbound Calls", f"{outbound_calls:,}")

            # Date range
            if "inbound" in summary_data and summary_data["inbound"].get("date_range"):
                start_date = summary_data["inbound"]["date_range"][0]
                end_date = summary_data["inbound"]["date_range"][1]
                st.info(f"Data range: {start_date} to {end_date}")

            # Show campaigns
            if "inbound" in summary_data and summary_data["inbound"].get("campaigns"):
                st.subheader("Campaigns")
                st.write(", ".join(summary_data["inbound"]["campaigns"]))

            # Show agents
            if "inbound" in summary_data and summary_data["inbound"].get("users"):
                st.subheader("Agents")
                st.write(", ".join(summary_data["inbound"]["users"]))
        else:
            st.warning("No data available. Please upload data files.")
    else:
        st.warning(
            "Could not retrieve data summary. Please check if the API is running."
        )

    # Quick access to key analyses
    st.subheader("🚀 Quick Access")
    st.info(
        "💡 Use the sidebar dropdown menu to navigate to different analysis pages, or use the quick access buttons in the sidebar for popular features."
    )

# Data Upload page
elif page == "Data Upload":
    st.title("Data Upload")
    st.write("Upload Excel files containing call flow data.")

    uploaded_files = st.file_uploader(
        "Upload Excel Files", type=["xlsx", "xls"], accept_multiple_files=True
    )

    if uploaded_files:
        if st.button("Process Files"):
            files = [
                (
                    "files",
                    (
                        file.name,
                        file.getvalue(),
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    ),
                )
                for file in uploaded_files
            ]

            with st.spinner("Uploading and processing files..."):
                response = make_api_request(
                    API_ENDPOINTS["upload_files"], method="POST", files=files
                )

                if response and response.get("success"):
                    st.success(f"Successfully uploaded {len(uploaded_files)} files")

                    # Display summary of uploaded files
                    uploaded_data = response.get("data", {})
                    if "filenames" in uploaded_data:
                        st.subheader("Uploaded Files")
                        for filename in uploaded_data["filenames"]:
                            st.write(f"- {filename}")

                    # Display any warnings
                    if "warnings" in uploaded_data and uploaded_data["warnings"]:
                        st.warning("Warnings:")
                        for warning in uploaded_data["warnings"]:
                            st.write(f"- {warning}")
                else:
                    st.error("Failed to upload files. Please try again.")

    # List existing files
    st.subheader("Existing Files")

    files_response = make_api_request(API_ENDPOINTS["list_files"])

    if files_response and files_response.get("success"):
        files = files_response.get("data", {}).get("files", [])

        if files:
            for file in files:
                st.write(f"- {file}")
        else:
            st.info("No files available")
    else:
        st.warning("Could not retrieve file list")

# Agent Performance page
elif page == "Agent Performance":
    st.title("Agent Performance Analysis")
    st.write("Analyze agent performance based on various metrics.")

    # Form for analysis parameters
    with st.form("agent_performance_form"):
        # Date range
        col1, col2 = st.columns(2)
        start_date = col1.date_input("Start Date", datetime.now() - timedelta(days=30))
        end_date = col2.date_input("End Date", datetime.now())

        # Metrics
        metrics = st.multiselect(
            "Select Metrics",
            ["resolution_rate", "call_length", "call_volume"],
            default=["resolution_rate", "call_length", "call_volume"],
        )

        # Filters
        campaigns = st.multiselect(
            "Filter by Campaigns",
            [
                "All",
                "Transaction_English",
                "GeneralArabic",
                "TechSupp",
                "ONLINE",
                "AGENTDIRECT",
            ],
        )
        agents = st.multiselect("Filter by Agents", ["All"])
        languages = st.multiselect("Filter by Languages", ["All", "English", "Arabic"])

        submit_button = st.form_submit_button("Analyze")

    if submit_button:
        # Prepare request data
        request_data = {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "metrics": metrics,
        }

        # Add filters if not "All"
        if campaigns and "All" not in campaigns:
            request_data["campaigns"] = campaigns
        if agents and "All" not in agents:
            request_data["agents"] = agents
        if languages and "All" not in languages:
            request_data["languages"] = languages

        # Make API request
        with st.spinner("Analyzing agent performance..."):
            response = make_api_request(
                API_ENDPOINTS["agent_performance"], method="POST", data=request_data
            )

            if response and response.get("success"):
                st.success("Analysis completed successfully")

                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])

                # Display agent scores if available
                if "agent_scores" in result_data:
                    st.subheader("Agent Performance Scores")

                    # Convert to DataFrame for better display
                    scores_df = pd.DataFrame(result_data["agent_scores"])
                    scores_df = scores_df.sort_values("overall_score", ascending=False)

                    # Display as table
                    st.dataframe(scores_df[["User", "overall_score"]])

                # Render charts
                if charts:
                    st.subheader("Performance Charts")

                    for chart in charts:
                        render_chart(chart)
                else:
                    st.info("No charts available")
            else:
                st.error("Failed to analyze agent performance")

# Call Volume Prediction page
elif page == "Call Volume Prediction":
    st.title("Call Volume Prediction")
    st.write("Predict call volumes based on historical data.")

    # Form for prediction parameters
    with st.form("call_volume_form"):
        # Date range
        col1, col2 = st.columns(2)
        start_date = col1.date_input(
            "Start Date (for training data)", datetime.now() - timedelta(days=90)
        )
        end_date = col2.date_input("End Date (for training data)", datetime.now())

        # Granularity
        granularity = st.selectbox(
            "Prediction Granularity", ["hourly", "daily", "weekly", "monthly"], index=1
        )

        # Filters
        campaigns = st.multiselect(
            "Filter by Campaigns",
            [
                "All",
                "Transaction_English",
                "GeneralArabic",
                "TechSupp",
                "ONLINE",
                "AGENTDIRECT",
            ],
        )
        languages = st.multiselect("Filter by Languages", ["All", "English", "Arabic"])

        submit_button = st.form_submit_button("Predict")

    if submit_button:
        # Prepare request data
        request_data = {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "granularity": granularity,
        }

        # Add filters if not "All"
        if campaigns and "All" not in campaigns:
            request_data["campaigns"] = campaigns
        if languages and "All" not in languages:
            request_data["languages"] = languages

        # Make API request
        with st.spinner(f"Predicting call volumes ({granularity})..."):
            response = make_api_request(
                API_ENDPOINTS["call_volume_prediction"],
                method="POST",
                data=request_data,
            )

            if response and response.get("success"):
                st.success("Prediction completed successfully")

                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])

                # Display historical and forecast data
                if "historical" in result_data and "forecast" in result_data:
                    st.subheader("Call Volume Forecast")

                    # Create tabs for chart and data
                    tab1, tab2 = st.tabs(["Chart", "Data"])

                    with tab1:
                        # Render charts
                        if charts:
                            for chart in charts:
                                render_chart(chart)
                        else:
                            st.info("No charts available")

                    with tab2:
                        # Display forecast data
                        st.subheader("Forecast Data")
                        forecast_df = pd.DataFrame(result_data["forecast"])
                        st.dataframe(forecast_df)

                        # Display historical data
                        st.subheader("Historical Data")
                        historical_df = pd.DataFrame(result_data["historical"])
                        st.dataframe(historical_df)
                else:
                    st.info("No prediction data available")
            else:
                st.error("Failed to predict call volumes")

# Model Training page
elif page == "Model Training":
    # Import the model training module
    try:
        # Try relative import first
        from .model_training import model_training_page
    except ImportError:
        # Fall back to direct import
        import sys
        import os

        # Get the absolute path to the frontend directory
        frontend_dir = os.path.dirname(os.path.abspath(__file__))
        if frontend_dir not in sys.path:
            sys.path.insert(0, frontend_dir)

        try:
            from model_training import model_training_page

            model_training_page(API_URL)
        except ImportError:
            st.title("Model Training")
            st.write("Model Training page is not yet implemented.")
            st.info("This feature will be available in a future update.")

# Task Monitor page
elif page == "Task Monitor":
    # Import the task monitor module
    try:
        # Try relative import first
        from .task_monitor import task_monitor_page
    except ImportError:
        # Fall back to direct import
        import sys
        import os

        # Get the absolute path to the frontend directory
        frontend_dir = os.path.dirname(os.path.abspath(__file__))
        if frontend_dir not in sys.path:
            sys.path.insert(0, frontend_dir)

        try:
            from task_monitor import task_monitor_page

            task_monitor_page(API_URL)
        except ImportError:
            st.title("Task Monitor")
            st.write("Task Monitor page is not yet implemented.")
            st.info("This feature will be available in a future update.")

# Advanced ML Models page
elif page == "Advanced ML Models":
    st.title("Advanced ML Models")
    st.write("Use advanced machine learning models for predictive analytics")

    # Get data summary for metadata with filters if available
    params = {}
    if "dashboard_filters" in st.session_state and any(
        st.session_state.dashboard_filters.values()
    ):
        params = st.session_state.dashboard_filters

    summary_response = make_api_request(API_ENDPOINTS["data_summary"], params=params)
    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})

        if summary_data:
            st.info(
                f"Data available from {summary_data.get('inbound', {}).get('date_range', ['', ''])[0]} to {summary_data.get('inbound', {}).get('date_range', ['', ''])[1]}"
            )

    # Create tabs for different ML models
    tab1, tab2, tab3 = st.tabs(
        ["Call Volume Prediction", "Model Training", "Model Evaluation"]
    )

    # Call the tab content functions
    with tab1:
        st.header("Advanced Call Volume Prediction")
        st.write("Predict call volumes using advanced machine learning models")
        # Rest of the tab1 code would go here

    with tab2:
        st.header("Model Training")
        st.write("Train machine learning models for call flow analytics")
        # Rest of the tab2 code would go here

    with tab3:
        st.header("Model Evaluation")
        st.write("Evaluate trained machine learning models")
        st.info(
            "Model evaluation functionality will be implemented in a future update."
        )

# Custom Reports page
elif page == "Custom Reports":
    st.title("Custom Reports")
    st.write("Generate custom reports for call flow analytics")

    # Get data summary for metadata with filters if available
    params = {}
    if "dashboard_filters" in st.session_state and any(
        st.session_state.dashboard_filters.values()
    ):
        params = st.session_state.dashboard_filters

    summary_response = make_api_request(API_ENDPOINTS["data_summary"], params=params)
    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})

        if summary_data:
            st.info(
                f"Data available from {summary_data.get('inbound', {}).get('date_range', ['', ''])[0]} to {summary_data.get('inbound', {}).get('date_range', ['', ''])[1]}"
            )

    # Rest of the custom reports code would go here
    st.info("Please use the form below to generate custom reports.")

# Comprehensive Call Analytics page
elif page == "Comprehensive Call Analytics":
    # Import the call analytics module
    try:
        # Get the absolute path to the frontend directory
        import sys
        import os

        frontend_dir = os.path.dirname(os.path.abspath(__file__))

        # Make sure the pages directory is in the path
        pages_dir = os.path.join(frontend_dir, "pages")
        if pages_dir not in sys.path:
            sys.path.insert(0, pages_dir)

        # Make sure app_config.py exists in the frontend directory
        config_path = os.path.join(frontend_dir, "app_config.py")
        if not os.path.exists(config_path):
            with open(config_path, "w") as f:
                f.write(f"API_URL = '{API_URL}'\n")

        # Try to import and run the module
        try:
            # Try direct import first
            from pages.call_analytics import main

            main()
        except ImportError:
            # If that fails, try importlib approach
            import importlib.util

            call_analytics_path = os.path.join(pages_dir, "call_analytics.py")

            if os.path.exists(call_analytics_path):
                spec = importlib.util.spec_from_file_location(
                    "call_analytics", call_analytics_path
                )
                call_analytics = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(call_analytics)
                call_analytics.main()
            else:
                st.error(f"Could not find call_analytics.py in {pages_dir}")
                st.info("Please check that the file exists and try again.")
    except Exception as e:
        st.title("Comprehensive Call Analytics")
        st.error(f"Error loading Comprehensive Call Analytics page: {str(e)}")
        st.info("Please check the console for more details.")

# Missed Call Dashboard page
elif page == "Missed Call Dashboard":
    # Import the enhanced missed call module
    try:
        # Get the absolute path to the frontend directory
        import sys
        import os

        frontend_dir = os.path.dirname(os.path.abspath(__file__))

        # Make sure the pages directory is in the path
        pages_dir = os.path.join(frontend_dir, "pages")
        if pages_dir not in sys.path:
            sys.path.insert(0, pages_dir)

        # Make sure app_config.py exists in the frontend directory
        config_path = os.path.join(frontend_dir, "app_config.py")
        if not os.path.exists(config_path):
            with open(config_path, "w") as f:
                f.write(f"API_URL = '{API_URL}'\n")

        # Try to import and run the module
        try:
            # Try direct import first
            from pages.enhanced_missed_call import main

            main()
        except ImportError:
            # If that fails, try importlib approach
            import importlib.util

            missed_call_path = os.path.join(pages_dir, "enhanced_missed_call.py")

            if os.path.exists(missed_call_path):
                spec = importlib.util.spec_from_file_location(
                    "enhanced_missed_call", missed_call_path
                )
                missed_call = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(missed_call)
                missed_call.main()
            else:
                st.error(f"Could not find enhanced_missed_call.py in {pages_dir}")
                st.info("Please check that the file exists and try again.")
    except Exception as e:
        st.title("Missed Call Dashboard")
        st.error(f"Error loading Missed Call Dashboard page: {str(e)}")
        st.info("Please check the console for more details.")

# Call Volume Forecasting page
elif page == "Call Volume Forecasting":
    # Import the time series forecast module
    try:
        # Get the absolute path to the frontend directory
        import sys
        import os

        frontend_dir = os.path.dirname(os.path.abspath(__file__))

        # Make sure the pages directory is in the path
        pages_dir = os.path.join(frontend_dir, "pages")
        if pages_dir not in sys.path:
            sys.path.insert(0, pages_dir)

        # Make sure app_config.py exists in the frontend directory
        config_path = os.path.join(frontend_dir, "app_config.py")
        if not os.path.exists(config_path):
            with open(config_path, "w") as f:
                f.write(f"API_URL = '{API_URL}'\n")

        # Try to import and run the module
        # Try to use the new version first
        try:
            from pages.time_series_forecast_new import main

            main()
        except ImportError:
            # If that fails, try the original version
            try:
                from pages.time_series_forecast import main

                main()
            except ImportError:
                # If that fails, try importlib approach
                import importlib.util

                # Try new version first
                forecast_path_new = os.path.join(
                    pages_dir, "time_series_forecast_new.py"
                )
                if os.path.exists(forecast_path_new):
                    spec = importlib.util.spec_from_file_location(
                        "time_series_forecast_new", forecast_path_new
                    )
                    forecast = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(forecast)
                    forecast.main()
                else:
                    # Fall back to original version
                    forecast_path = os.path.join(pages_dir, "time_series_forecast.py")
                    if os.path.exists(forecast_path):
                        spec = importlib.util.spec_from_file_location(
                            "time_series_forecast", forecast_path
                        )
                        forecast = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(forecast)
                        forecast.main()
                    else:
                        st.error(
                            f"Could not find time_series_forecast.py in {pages_dir}"
                        )
                        st.info("Please check that the file exists and try again.")
    except Exception as e:
        st.title("Call Volume Forecasting")
        st.error(f"Error loading Call Volume Forecasting page: {str(e)}")
        st.info("Please check the console for more details.")

# Agent Scheduling Model page
elif page == "Agent Scheduling Model":
    # Import the agent scheduling module
    try:
        # Get the absolute path to the frontend directory
        import sys
        import os

        frontend_dir = os.path.dirname(os.path.abspath(__file__))

        # Make sure the pages directory is in the path
        pages_dir = os.path.join(frontend_dir, "pages")
        if pages_dir not in sys.path:
            sys.path.insert(0, pages_dir)

        # Make sure app_config.py exists in the frontend directory
        config_path = os.path.join(frontend_dir, "app_config.py")
        if not os.path.exists(config_path):
            with open(config_path, "w") as f:
                f.write(f"API_URL = '{API_URL}'\n")

        # Try to import and run the module
        try:
            # Try direct import first
            from pages.agent_scheduling import main

            main()
        except ImportError:
            # If that fails, try importlib approach
            import importlib.util

            agent_scheduling_path = os.path.join(pages_dir, "agent_scheduling.py")

            if os.path.exists(agent_scheduling_path):
                spec = importlib.util.spec_from_file_location(
                    "agent_scheduling", agent_scheduling_path
                )
                agent_scheduling = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(agent_scheduling)
                agent_scheduling.main()
            else:
                st.error(f"Could not find agent_scheduling.py in {pages_dir}")
                st.info("Please check that the file exists and try again.")
    except Exception as e:
        st.title("Agent Scheduling Model")
        st.error(f"Error loading Agent Scheduling Model page: {str(e)}")
        st.info("Please check the console for more details.")

# Comprehensive Analytics page (new implementation)
elif page == "Comprehensive Analytics":
    st.title("📊 Comprehensive Call Flow Analytics")
    st.write("Complete analysis with all metrics, filters, and statistical insights.")

    # Form for comprehensive analysis parameters
    with st.form("comprehensive_analytics_form"):
        st.subheader("📅 Date and Time Filters")

        # Date range
        col1, col2 = st.columns(2)
        start_date = col1.date_input("Start Date", datetime.now() - timedelta(days=30))
        end_date = col2.date_input("End Date", datetime.now())

        st.subheader("🎯 Segmentation Filters")

        # Filters in columns
        filter_col1, filter_col2 = st.columns(2)

        with filter_col1:
            # Day of week filter
            days_of_week = st.multiselect(
                "Filter by Days of Week",
                [
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                    "Sunday",
                ],
                help="Select specific days to analyze",
            )

            # Language filter
            languages = st.multiselect(
                "Filter by Languages",
                ["English", "Arabic"],
                help="Select languages to include in analysis",
            )

            # Call type filter
            call_types = st.multiselect(
                "Filter by Call Types",
                ["inbound", "outbound", "missed"],
                help="Select call types to analyze",
            )

        with filter_col2:
            # Campaign filter
            campaigns = st.multiselect(
                "Filter by Campaigns",
                [
                    "Transaction_English",
                    "GeneralArabic",
                    "TechSupp",
                    "ONLINE",
                    "AGENTDIRECT",
                ],
                help="Select specific campaigns to analyze",
            )

            # Agent filter
            agents = st.multiselect(
                "Filter by Agents",
                [],  # Will be populated dynamically
                help="Select specific agents to analyze",
            )

        st.subheader("📊 Analysis Options")

        # Analysis options in columns
        analysis_col1, analysis_col2 = st.columns(2)

        with analysis_col1:
            st.write("**Core Metrics:**")
            include_avg_call_time = st.checkbox("Average Call Time", value=True)
            include_total_duration = st.checkbox("Total Duration", value=True)
            include_standard_deviation = st.checkbox("Standard Deviation", value=True)
            include_call_volume = st.checkbox("Call Volume Statistics", value=True)

            st.write("**Grouping Options:**")
            group_by_language = st.checkbox("Group by Language", value=True)
            group_by_campaign = st.checkbox("Group by Campaign", value=True)
            group_by_agent = st.checkbox("Group by Agent", value=True)
            group_by_call_type = st.checkbox("Group by Call Type", value=True)

        with analysis_col2:
            st.write("**Time Analysis:**")
            group_by_day = st.checkbox("Group by Day of Week", value=True)
            group_by_hour = st.checkbox("Group by Hour of Day", value=True)

            st.write("**Statistical Analysis:**")
            include_percentiles = st.checkbox("Include Percentiles", value=True)
            include_outlier_analysis = st.checkbox("Outlier Detection", value=True)
            include_trend_analysis = st.checkbox("Trend Analysis", value=True)

            st.write("**Customer Satisfaction:**")
            include_satisfaction_analysis = st.checkbox(
                "Satisfaction Analysis", value=True
            )
            satisfaction_field = st.text_input(
                "Satisfaction Field Name", placeholder="e.g., rating, score, csat"
            )

        submit_button = st.form_submit_button(
            "🚀 Run Comprehensive Analysis", use_container_width=True
        )

    if submit_button:
        # Prepare request data
        request_data = {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            # Core metrics
            "include_avg_call_time": include_avg_call_time,
            "include_total_duration": include_total_duration,
            "include_standard_deviation": include_standard_deviation,
            "include_call_volume": include_call_volume,
            # Filtering options
            "filter_by_day": days_of_week if days_of_week else None,
            "filter_by_language": languages if languages else None,
            "filter_by_campaign": campaigns if campaigns else None,
            "filter_by_agent": agents if agents else None,
            "filter_by_call_type": call_types if call_types else None,
            # Grouping options
            "group_by_language": group_by_language,
            "group_by_campaign": group_by_campaign,
            "group_by_agent": group_by_agent,
            "group_by_call_type": group_by_call_type,
            "group_by_day": group_by_day,
            "group_by_hour": group_by_hour,
            # Statistical analysis
            "include_percentiles": include_percentiles,
            "include_outlier_analysis": include_outlier_analysis,
            "include_trend_analysis": include_trend_analysis,
            # Customer satisfaction
            "include_satisfaction_analysis": include_satisfaction_analysis,
            "satisfaction_field": satisfaction_field if satisfaction_field else None,
            # Visualization
            "include_charts": True,
            "chart_types": ["bar", "line", "pie", "box"],
        }

        # Make API request
        with st.spinner("🔄 Running comprehensive analysis... This may take a moment."):
            response = make_api_request(
                API_ENDPOINTS["comprehensive_analytics"],
                method="POST",
                data=request_data,
            )

            if response and response.get("success"):
                st.success("✅ Comprehensive analysis completed successfully!")

                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])

                # Core Metrics Section
                if "core_metrics" in result_data:
                    st.header("📈 Core Metrics")
                    core_metrics = result_data["core_metrics"]

                    if "error" not in core_metrics:
                        # Display metrics in columns
                        metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(
                            4
                        )

                        if "average_call_time" in core_metrics:
                            metric_col1.metric(
                                "Average Call Time",
                                core_metrics["average_call_time"]["formatted"],
                                help=f"{core_metrics['average_call_time']['seconds']:.1f} seconds",
                            )

                        if "total_duration" in core_metrics:
                            metric_col2.metric(
                                "Total Duration",
                                core_metrics["total_duration"]["formatted"],
                                help=f"{core_metrics['total_duration']['hours']:.1f} hours",
                            )

                        if "standard_deviation" in core_metrics:
                            metric_col3.metric(
                                "Standard Deviation",
                                core_metrics["standard_deviation"]["formatted"],
                                help=f"{core_metrics['standard_deviation']['seconds']:.1f} seconds",
                            )

                        if "call_volume" in core_metrics:
                            volume = core_metrics["call_volume"]
                            metric_col4.metric(
                                "Answer Rate",
                                f"{volume['answer_rate']*100:.1f}%",
                                help=f"{volume['answered_calls']:,} answered / {volume['total_calls']:,} total",
                            )

                # Time Analysis Section
                if "time_analysis" in result_data:
                    st.header("⏰ Time-Based Analysis")
                    time_analysis = result_data["time_analysis"]

                    if "hourly_volume" in time_analysis:
                        st.subheader("Hourly Call Volume")
                        hourly_data = time_analysis["hourly_volume"]

                        # Create hourly chart
                        hours = list(hourly_data.keys())
                        volumes = list(hourly_data.values())

                        fig = px.bar(
                            x=hours,
                            y=volumes,
                            title="Call Volume by Hour of Day",
                            labels={"x": "Hour", "y": "Number of Calls"},
                        )
                        st.plotly_chart(fig, use_container_width=True)

                # Segmentation Analysis Section
                if "segmentation" in result_data:
                    st.header("🎯 Segmentation Analysis")
                    segmentation = result_data["segmentation"]

                    # Language segmentation
                    if "by_language" in segmentation:
                        st.subheader("Performance by Language")
                        lang_data = segmentation["by_language"]

                        # Convert to DataFrame for better display
                        lang_df = pd.DataFrame.from_dict(lang_data, orient="index")
                        lang_df.index.name = "Language"
                        lang_df = lang_df.reset_index()

                        # Display as table
                        st.dataframe(lang_df, use_container_width=True)

                    # Campaign segmentation
                    if "by_campaign" in segmentation:
                        st.subheader("Performance by Campaign")
                        campaign_data = segmentation["by_campaign"]

                        # Convert to DataFrame
                        campaign_df = pd.DataFrame.from_dict(
                            campaign_data, orient="index"
                        )
                        campaign_df.index.name = "Campaign"
                        campaign_df = campaign_df.reset_index()

                        # Display as table
                        st.dataframe(campaign_df, use_container_width=True)

                # Statistical Analysis Section
                if "statistics" in result_data:
                    st.header("📊 Statistical Analysis")
                    statistics = result_data["statistics"]

                    # Percentiles
                    if "percentiles" in statistics:
                        st.subheader("Call Duration Percentiles")
                        percentiles = statistics["percentiles"]

                        # Display percentiles in columns
                        perc_cols = st.columns(len(percentiles))
                        for i, (perc, value) in enumerate(percentiles.items()):
                            perc_cols[i].metric(
                                f"{perc.upper()}",
                                f"{value:.0f}s",
                                help=f"{value/60:.1f} minutes",
                            )

                    # Outliers
                    if "outliers" in statistics:
                        st.subheader("Outlier Analysis")
                        outliers = statistics["outliers"]

                        outlier_col1, outlier_col2 = st.columns(2)
                        outlier_col1.metric("Outlier Count", f"{outliers['count']:,}")
                        outlier_col2.metric(
                            "Outlier Percentage", f"{outliers['percentage']:.1f}%"
                        )

                        if outliers["count"] > 0:
                            st.info(
                                f"Outliers are calls with duration < {outliers['lower_bound']:.0f}s or > {outliers['upper_bound']:.0f}s"
                            )

                # Customer Satisfaction Section
                if "satisfaction" in result_data:
                    st.header("😊 Customer Satisfaction Analysis")
                    satisfaction = result_data["satisfaction"]

                    # Display satisfaction metrics
                    sat_col1, sat_col2, sat_col3 = st.columns(3)
                    sat_col1.metric(
                        "Average Score", f"{satisfaction['average_score']:.2f}"
                    )
                    sat_col2.metric(
                        "Total Responses", f"{satisfaction['total_responses']:,}"
                    )
                    sat_col3.metric(
                        "Score Range",
                        f"{satisfaction['min_score']:.1f} - {satisfaction['max_score']:.1f}",
                    )

                # Charts Section
                if charts:
                    st.header("📈 Visualizations")

                    # Display charts in tabs
                    chart_tabs = st.tabs(
                        [
                            f"Chart {i+1}: {chart.get('title', 'Untitled')}"
                            for i, chart in enumerate(charts)
                        ]
                    )

                    for i, (tab, chart) in enumerate(zip(chart_tabs, charts)):
                        with tab:
                            render_chart(chart)

                # Summary and Recommendations
                st.header("💡 Summary & Recommendations")

                # Display any summary data
                if "summary" in result_data:
                    summary = result_data["summary"]
                    if summary:
                        st.subheader("Key Insights")
                        for key, value in summary.items():
                            if isinstance(value, dict):
                                st.write(f"**{key.replace('_', ' ').title()}:**")
                                for sub_key, sub_value in value.items():
                                    st.write(
                                        f"  - {sub_key.replace('_', ' ').title()}: {sub_value}"
                                    )
                            else:
                                st.write(
                                    f"**{key.replace('_', ' ').title()}:** {value}"
                                )

                # Display recommendations if available
                if "recommendations" in result_data:
                    recommendations = result_data["recommendations"]
                    if recommendations:
                        st.subheader("Actionable Recommendations")
                        for i, recommendation in enumerate(recommendations, 1):
                            st.write(f"{i}. {recommendation}")

            else:
                st.error(
                    "❌ Failed to perform comprehensive analysis. Please check your data and try again."
                )
                if response:
                    st.error(f"Error details: {response}")

# Other pages
else:
    st.title(page)
    st.write(f"This is the {page} page.")
    st.info("This feature is under development and will be available soon.")

# Run the app
if __name__ == "__main__":
    pass
