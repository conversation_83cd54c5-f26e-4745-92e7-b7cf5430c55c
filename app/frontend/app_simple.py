import streamlit as st

# Set page config
st.set_page_config(
    page_title="Call Flow Analytics",
    page_icon="📞",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Main title
st.title("Call Flow Analytics")
st.write("Welcome to the Call Flow Analytics Dashboard.")

# Sidebar
st.sidebar.title("Navigation")
st.sidebar.write("This is a simplified version of the app to fix widget key issues.")

# Main content
st.write("The original app has been temporarily replaced with this simplified version to fix widget key issues.")
st.write("Please contact the development team for assistance.")

# Display information about the error
st.error("""
The original app encountered a DuplicateWidgetID error. This happens when multiple widgets are created with the same key.

To fix this issue:
1. Make sure all widget keys are unique
2. Avoid importing modules that create widgets with the same keys
3. Use a different approach for navigation
""")

# Add a button to try the original app
if st.button("Try Original App"):
    st.write("Redirecting to the original app...")
    # This would normally redirect to the original app, but for now it just shows a message
    st.info("This feature is not yet implemented.")
