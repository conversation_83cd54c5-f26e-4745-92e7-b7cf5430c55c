"""
Streamlit configuration for the Call Flow Analytics application.
This module contains the configuration for the Streamlit frontend.
"""

import streamlit as st


# Set page config - this should be imported before any other Streamlit code
def setup_page_config():
    """Set up the page configuration for Streamlit"""
    # Try to set the page config, but catch the exception if it's already set
    try:
        st.set_page_config(
            page_title="Call Flow Analytics",
            page_icon="📞",
            layout="wide",
            initial_sidebar_state="expanded",
        )
    except Exception as e:
        # If the config is already set, this will raise an exception
        # We can safely ignore it
        pass
