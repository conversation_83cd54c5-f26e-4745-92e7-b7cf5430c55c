import streamlit as st
import pandas as pd
import numpy as np
import requests
import json
import os
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import io
import base64
from app.shared.config import API_URL

# Note: Page config is now set in the main app.py file
# Do not set page config here to avoid conflicts

# Define API endpoints
API_ENDPOINTS = {
    "generate_report": f"{API_URL}/advanced/generate-report",
    "data_summary": f"{API_URL}/data/summary",
}


# Helper functions
def make_api_request(endpoint, method="GET", data=None, files=None, params=None):
    """
    Make a request to the API

    Args:
        endpoint: API endpoint
        method: HTTP method
        data: Request data
        files: Files to upload
        params: Query parameters for GET requests

    Returns:
        API response
    """
    try:
        if method.upper() == "GET":
            response = requests.get(endpoint, params=params)
        elif method.upper() == "POST":
            if files:
                response = requests.post(endpoint, files=files)
            else:
                response = requests.post(endpoint, json=data)
        else:
            st.error(f"Unsupported method: {method}")
            return None

        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"API Error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        st.error(f"Error making API request: {str(e)}")
        return None


def download_report(url, report_type):
    """
    Create a download link for the report

    Args:
        url: Report URL
        report_type: Report type (pdf or excel)
    """
    try:
        response = requests.get(f"{API_URL}{url}")

        if response.status_code == 200:
            # Get file content
            content = response.content

            # Create download link
            b64 = base64.b64encode(content).decode()

            # Determine file extension and mime type
            if report_type == "pdf":
                ext = "pdf"
                mime = "application/pdf"
            else:
                ext = "xlsx"
                mime = (
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )

            # Create download link
            href = f'<a href="data:{mime};base64,{b64}" download="call_flow_report.{ext}">Download {report_type.upper()} Report</a>'

            return href
        else:
            return f"Error downloading report: {response.status_code}"

    except Exception as e:
        return f"Error downloading report: {str(e)}"


# Main content
st.title("Custom Reports")
st.write("Generate custom reports for call flow analytics")

# Get data summary for metadata
summary_response = make_api_request(API_ENDPOINTS["data_summary"])
if summary_response and summary_response.get("success"):
    summary_data = summary_response.get("data", {}).get("summary", {})

    if summary_data:
        st.info(
            f"Data available from {summary_data.get('inbound', {}).get('date_range', ['', ''])[0]} to {summary_data.get('inbound', {}).get('date_range', ['', ''])[1]}"
        )

# Form for report generation
with st.form("report_generation_form"):
    # Report title and type
    col1, col2 = st.columns(2)
    report_title = col1.text_input("Report Title", "Call Flow Analytics Report")
    report_type = col2.selectbox("Report Type", ["pdf", "excel"], index=0)

    # Date range
    col3, col4 = st.columns(2)
    start_date = col3.date_input("Start Date", datetime.now() - timedelta(days=30))
    end_date = col4.date_input("End Date", datetime.now())

    # Report sections
    st.subheader("Report Sections")

    # Create columns for section selection
    col5, col6, col7 = st.columns(3)

    # Define available sections
    available_sections = [
        "overview",
        "agent_performance",
        "call_volume",
        "campaign_analysis",
        "language_analysis",
        "missed_call_analysis",
        "customer_journey",
        "first_call_resolution",
        "sentiment_analysis",
        "agent_specialization",
    ]

    # Create checkboxes for sections
    selected_sections = []

    # Distribute sections across columns
    for i, section in enumerate(available_sections):
        col = [col5, col6, col7][i % 3]
        if col.checkbox(section.replace("_", " ").title(), value=(i < 3)):
            selected_sections.append(section)

    # Report options
    st.subheader("Report Options")

    col8, col9 = st.columns(2)
    include_charts = col8.checkbox("Include Charts", value=True)
    include_tables = col9.checkbox("Include Tables", value=True)

    # Filters
    st.subheader("Filters")

    campaigns = st.multiselect(
        "Filter by Campaigns",
        [
            "All",
            "Transaction_English",
            "GeneralArabic",
            "TechSupp",
            "ONLINE",
            "AGENTDIRECT",
        ],
    )
    languages = st.multiselect("Filter by Languages", ["All", "English", "Arabic"])

    submit_button = st.form_submit_button("Generate Report")

if submit_button:
    if not selected_sections:
        st.error("Please select at least one report section")
    else:
        # Prepare request data
        request_data = {
            "report_title": report_title,
            "report_type": report_type,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "sections": selected_sections,
            "include_charts": include_charts,
            "include_tables": include_tables,
        }

        # Add filters if not "All"
        if campaigns and "All" not in campaigns:
            request_data["campaigns"] = campaigns
        if languages and "All" not in languages:
            request_data["languages"] = languages

        # Make API request
        with st.spinner("Generating report..."):
            response = make_api_request(
                API_ENDPOINTS["generate_report"], method="POST", data=request_data
            )

            if response and response.get("success"):
                st.success("Report generated successfully")

                # Display results
                result_data = response.get("data", {})
                report_url = response.get("report_url")

                if report_url:
                    # Create download link
                    download_link = download_report(report_url, report_type)

                    # Display download link
                    st.markdown(download_link, unsafe_allow_html=True)

                    # Display report info
                    st.subheader("Report Information")
                    st.write(f"Report Type: {report_type.upper()}")
                    st.write(
                        f"Report Title: {result_data.get('report_title', report_title)}"
                    )
                    st.write(
                        f"Sections: {', '.join([s.replace('_', ' ').title() for s in selected_sections])}"
                    )
                else:
                    st.warning("Report URL not available. Please try again later.")
            else:
                st.error("Failed to generate report")

# Report templates
st.header("Report Templates")
st.write("Use pre-defined report templates for common use cases")

# Create columns for templates
template_col1, template_col2, template_col3 = st.columns(3)

with template_col1:
    st.subheader("Agent Performance Report")
    st.write("Comprehensive analysis of agent performance metrics")

    if st.button("Generate Agent Report"):
        # Pre-fill form with agent performance template
        st.session_state["report_title"] = "Agent Performance Report"
        st.session_state["selected_sections"] = [
            "overview",
            "agent_performance",
            "agent_specialization",
        ]

        # Add safety mechanism for template reruns
        if "template_rerun_counter" not in st.session_state:
            st.session_state.template_rerun_counter = 0

        if st.session_state.template_rerun_counter < 3:
            st.session_state.template_rerun_counter += 1
            st.rerun()
        else:
            st.warning(
                "Please manually fill in the form with the Agent Performance template values."
            )

with template_col2:
    st.subheader("Campaign Analysis Report")
    st.write("Detailed analysis of campaign performance")

    if st.button("Generate Campaign Report"):
        # Pre-fill form with campaign analysis template
        st.session_state["report_title"] = "Campaign Analysis Report"
        st.session_state["selected_sections"] = [
            "overview",
            "campaign_analysis",
            "call_volume",
        ]

        # Add safety mechanism for template reruns
        if "template_rerun_counter" not in st.session_state:
            st.session_state.template_rerun_counter = 0

        if st.session_state.template_rerun_counter < 3:
            st.session_state.template_rerun_counter += 1
            st.rerun()
        else:
            st.warning(
                "Please manually fill in the form with the Campaign Analysis template values."
            )

with template_col3:
    st.subheader("Customer Experience Report")
    st.write("Analysis of customer experience metrics")

    if st.button("Generate CX Report"):
        # Pre-fill form with customer experience template
        st.session_state["report_title"] = "Customer Experience Report"
        st.session_state["selected_sections"] = [
            "overview",
            "first_call_resolution",
            "sentiment_analysis",
            "missed_call_analysis",
        ]

        # Add safety mechanism for template reruns
        if "template_rerun_counter" not in st.session_state:
            st.session_state.template_rerun_counter = 0

        if st.session_state.template_rerun_counter < 3:
            st.session_state.template_rerun_counter += 1
            st.rerun()
        else:
            st.warning(
                "Please manually fill in the form with the Customer Experience template values."
            )

# Report scheduling
st.header("Report Scheduling")
st.write("Schedule reports to be generated automatically")

st.info("Report scheduling functionality will be implemented in a future update.")

# Placeholder for report scheduling
st.write("This section will include:")
st.write("- Daily, weekly, or monthly report scheduling")
st.write("- Email delivery of reports")
st.write("- Custom report templates")
st.write("- Report archiving and management")
