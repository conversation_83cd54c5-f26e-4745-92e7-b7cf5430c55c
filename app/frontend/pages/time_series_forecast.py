"""
Time Series Forecasting page for the Call Flow Analytics application.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import os
import sys

# Try to import from parent directory first
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from app_config import API_URL
except ImportError:
    # If that fails, try to import from the current directory
    try:
        from .app_config import API_URL
    except ImportError:
        # Last resort, use a default value
        API_URL = "http://localhost:8002"

# Define API endpoints
API_ENDPOINTS = {
    "time_series_forecast": f"{API_URL}/analytics/time-series-forecast",
    "data_summary": f"{API_URL}/data/summary",
}


def make_api_request(endpoint, params=None, method="POST"):
    """
    Make a request to the API

    Args:
        endpoint: API endpoint
        params: Request parameters
        method: HTTP method

    Returns:
        Response JSON or None if error
    """
    import requests

    try:
        if method == "GET":
            response = requests.get(endpoint, json=params)
        else:
            response = requests.post(endpoint, json=params)

        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Error making API request: {str(e)}")
        return None


def render_chart(chart_data):
    """
    Render a chart based on chart data

    Args:
        chart_data: Chart data from API
    """
    try:
        st.write("DEBUG: Starting render_chart")
        chart_type = chart_data.get("chart_type", "")
        title = chart_data.get("title", "")
        x_label = chart_data.get("x_label", "")
        y_label = chart_data.get("y_label", "")
        data = chart_data.get("data", {})

        st.write(f"DEBUG: Chart type: {chart_type}, Title: {title}")

        if not data:
            st.warning("No data available for chart")
            return

        # Debug data types
        if "x" in data:
            st.write(f"DEBUG: x data types: {[type(x) for x in data.get('x', [])[:3]]}")
        if "y" in data:
            st.write(f"DEBUG: y data types: {[type(y) for y in data.get('y', [])[:3]]}")
        if "series" in data:
            st.write(
                f"DEBUG: series data types: {[type(s) for s in data.get('series', [])[:3]]}"
            )

        # Check for length mismatches
        x_len = len(data.get("x", []))
        y_len = len(data.get("y", []))
        series_len = len(data.get("series", []))
        st.write(f"DEBUG: Data lengths - x: {x_len}, y: {y_len}, series: {series_len}")

        # Convert y values to float
        y_values = []
        for val in data.get("y", []):
            try:
                y_values.append(float(val))
            except (ValueError, TypeError):
                st.warning(f"Error converting y value: {val}, type: {type(val)}")
                y_values.append(0.0)

        # Handle different chart types
        if chart_type == "line":
            if "series" in data:
                # Create a DataFrame for easier handling with series
                df = pd.DataFrame(
                    {
                        "x": data.get("x", [])[: len(y_values)],  # Ensure lengths match
                        "y": y_values,
                        "series": data.get("series", [])[
                            : len(y_values)
                        ],  # Ensure lengths match
                    }
                )

                # Create a line chart with different colors for historical vs forecast
                fig = px.line(
                    df,
                    x="x",
                    y="y",
                    color="series",
                    title=title,
                    labels={"x": x_label, "y": y_label, "series": "Data Type"},
                    color_discrete_map={"Historical": "blue", "Forecast": "red"},
                )

                # Add a vertical line to separate historical from forecast
                if (
                    "Historical" in df["series"].values
                    and "Forecast" in df["series"].values
                ):
                    try:
                        # Find the last historical point
                        last_historical_idx = df[df["series"] == "Historical"].index[-1]
                        # Add a vertical line
                        fig.add_vline(
                            x=df.loc[last_historical_idx, "x"],
                            line_dash="dash",
                            line_color="gray",
                            annotation_text="Forecast Start",
                            annotation_position="top right",
                        )
                    except Exception as e:
                        st.warning(f"Error adding vertical line: {str(e)}")
            else:
                # Regular line chart
                fig = px.line(
                    x=data.get("x", [])[: len(y_values)],  # Ensure lengths match
                    y=y_values,
                    title=title,
                    labels={"x": x_label, "y": y_label},
                )
        elif chart_type == "bar":
            # Bar chart
            fig = px.bar(
                x=data.get("x", [])[: len(y_values)],  # Ensure lengths match
                y=y_values,
                title=title,
                labels={"x": x_label, "y": y_label},
            )
        else:
            st.warning(f"Unsupported chart type: {chart_type}")
            return

        # Display the chart
        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"Error in render_chart: {str(e)}")
        import traceback

        st.error(traceback.format_exc())


def display_recommendations(recommendations):
    """
    Display recommendations in a nice format

    Args:
        recommendations: List of recommendation strings
    """
    if not recommendations:
        st.info("No recommendations available")
        return

    st.subheader("📋 Actionable Recommendations")

    for i, recommendation in enumerate(recommendations):
        with st.container():
            st.markdown(f"**{i+1}. {recommendation}**")
            st.markdown("---")


def main():
    """Main function for the Time Series Forecasting page"""

    # Main title
    st.title("📈 Call Volume Forecasting")
    st.write("Forecast future call volumes with language and campaign breakdowns")

    # Sidebar filters
    st.sidebar.header("Filters")

    # Date range filter
    st.sidebar.subheader("Date Range")

    # Get current date and 30 days ago for default date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)

    # Date inputs
    start_date_input = st.sidebar.date_input(
        "Start Date", value=start_date, key="start_date"
    )
    end_date_input = st.sidebar.date_input("End Date", value=end_date, key="end_date")

    # Campaign filter
    st.sidebar.subheader("Campaigns")

    # Get data summary for available campaigns
    summary_response = make_api_request(API_ENDPOINTS["data_summary"], method="GET")
    available_campaigns = []

    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})
        if "inbound" in summary_data and summary_data["inbound"].get("campaigns"):
            available_campaigns = summary_data["inbound"]["campaigns"]

    selected_campaigns = st.sidebar.multiselect(
        "Select Campaigns", options=available_campaigns, default=None, key="campaigns"
    )

    # Forecasting parameters
    st.sidebar.subheader("Forecasting Parameters")

    # Number of days to forecast
    forecast_days = st.sidebar.slider(
        "Days to Forecast",
        min_value=1,
        max_value=30,
        value=7,
        step=1,
        key="forecast_days",
    )

    # Time granularity
    granularity = st.sidebar.selectbox(
        "Time Granularity",
        options=["hourly", "daily", "weekly"],
        index=1,  # Default to daily
        key="granularity",
    )

    # Breakdown options
    by_language = st.sidebar.checkbox(
        "Break Down by Language", value=True, key="by_language"
    )

    by_campaign = st.sidebar.checkbox(
        "Break Down by Campaign", value=True, key="by_campaign"
    )

    # Forecasting method
    forecast_method = st.sidebar.selectbox(
        "Forecasting Method",
        options=["auto", "ets", "arima", "linear"],
        index=0,  # Default to auto
        key="forecast_method",
    )

    # Run forecasting button
    if st.sidebar.button("Run Forecast", key="run_forecast"):
        # Show spinner while loading
        with st.spinner(
            f"Forecasting call volumes for the next {forecast_days} days..."
        ):
            # Prepare request parameters
            params = {
                "start_date": (
                    start_date_input.strftime("%Y-%m-%d") if start_date_input else None
                ),
                "end_date": (
                    end_date_input.strftime("%Y-%m-%d") if end_date_input else None
                ),
                "campaigns": selected_campaigns if selected_campaigns else None,
                "forecast_days": forecast_days,
                "granularity": granularity,
                "by_language": by_language,
                "by_campaign": by_campaign,
                "forecast_method": forecast_method,
            }

            # Make API request
            response = make_api_request(
                API_ENDPOINTS["time_series_forecast"], params=params
            )

            if response and response.get("success"):
                st.success("Forecasting completed successfully")

                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])

                # Display recommendations if available
                if "recommendations" in result_data:
                    display_recommendations(result_data["recommendations"])

                # Create tabs for different forecast sections
                tab1, tab2, tab3 = st.tabs(
                    ["Overall Forecast", "Language Breakdown", "Campaign Breakdown"]
                )

                with tab1:
                    st.header("Overall Call Volume Forecast")

                    # Display overall forecast chart
                    overall_chart = next(
                        (
                            chart
                            for chart in charts
                            if "Call Volume Forecast" == chart.get("title", "")
                        ),
                        None,
                    )
                    if overall_chart:
                        render_chart(overall_chart)

                    # Display hourly pattern if available
                    hourly_chart = next(
                        (
                            chart
                            for chart in charts
                            if "Hourly Call Pattern" == chart.get("title", "")
                        ),
                        None,
                    )
                    if hourly_chart:
                        render_chart(hourly_chart)

                    # Display forecast data
                    if "overall_forecast" in result_data:
                        st.subheader("Forecast Data")

                        # Create columns for historical vs forecast
                        col1, col2 = st.columns(2)

                        with col1:
                            st.write("Historical Data")
                            # Safely convert values to float
                            try:
                                hist_values = []
                                for val in result_data["overall_forecast"][
                                    "historical_values"
                                ]:
                                    try:
                                        hist_values.append(float(val))
                                    except (ValueError, TypeError):
                                        st.warning(f"Error converting value: {val}")
                                        hist_values.append(0.0)

                                hist_df = pd.DataFrame(
                                    {
                                        "Date": result_data["overall_forecast"][
                                            "historical_dates"
                                        ],
                                        "Call Count": hist_values,
                                    }
                                )
                            except Exception as e:
                                st.error(
                                    f"Error creating historical dataframe: {str(e)}"
                                )
                                hist_df = pd.DataFrame({"Date": [], "Call Count": []})
                            st.dataframe(hist_df)

                        with col2:
                            st.write("Forecast Data")
                            # Safely convert values to float
                            try:
                                forecast_values = []
                                for val in result_data["overall_forecast"][
                                    "forecast_values"
                                ]:
                                    try:
                                        forecast_values.append(float(val))
                                    except (ValueError, TypeError):
                                        st.warning(
                                            f"Error converting forecast value: {val}"
                                        )
                                        forecast_values.append(0.0)

                                forecast_df = pd.DataFrame(
                                    {
                                        "Date": result_data["overall_forecast"][
                                            "forecast_dates"
                                        ],
                                        "Predicted Call Count": forecast_values,
                                    }
                                )
                            except Exception as e:
                                st.error(f"Error creating forecast dataframe: {str(e)}")
                                forecast_df = pd.DataFrame(
                                    {"Date": [], "Predicted Call Count": []}
                                )
                            st.dataframe(forecast_df)

                with tab2:
                    st.header("Language Breakdown Forecast")

                    if by_language and "language_forecasts" in result_data:
                        # Display language forecast charts
                        language_charts = [
                            chart
                            for chart in charts
                            if "English Calls" in chart.get("title", "")
                            or "Arabic Calls" in chart.get("title", "")
                        ]

                        for chart in language_charts:
                            render_chart(chart)

                        # Display language forecast data
                        st.subheader("Language Forecast Data")

                        for language, forecast in result_data[
                            "language_forecasts"
                        ].items():
                            st.write(f"{language} Forecast")
                            # Safely convert values to float
                            try:
                                forecast_values = []
                                for val in forecast["forecast_values"]:
                                    try:
                                        forecast_values.append(float(val))
                                    except (ValueError, TypeError):
                                        forecast_values.append(0.0)

                                forecast_df = pd.DataFrame(
                                    {
                                        "Date": forecast["forecast_dates"],
                                        "Predicted Call Count": forecast_values,
                                    }
                                )
                            except Exception as e:
                                st.error(
                                    f"Error creating language forecast dataframe: {str(e)}"
                                )
                                forecast_df = pd.DataFrame(
                                    {"Date": [], "Predicted Call Count": []}
                                )
                            st.dataframe(forecast_df)
                    else:
                        st.info(
                            "Language breakdown was not requested or no language data is available."
                        )

                with tab3:
                    st.header("Campaign Breakdown Forecast")

                    if by_campaign and "campaign_forecasts" in result_data:
                        # Display campaign forecast charts
                        campaign_charts = [
                            chart
                            for chart in charts
                            if "Campaign" in chart.get("title", "")
                        ]

                        for chart in campaign_charts:
                            render_chart(chart)

                        # Display campaign forecast data
                        st.subheader("Campaign Forecast Data")

                        for campaign, forecast in result_data[
                            "campaign_forecasts"
                        ].items():
                            st.write(f"{campaign} Forecast")
                            # Safely convert values to float
                            try:
                                forecast_values = []
                                for val in forecast["forecast_values"]:
                                    try:
                                        forecast_values.append(float(val))
                                    except (ValueError, TypeError):
                                        forecast_values.append(0.0)

                                forecast_df = pd.DataFrame(
                                    {
                                        "Date": forecast["forecast_dates"],
                                        "Predicted Call Count": forecast_values,
                                    }
                                )
                            except Exception as e:
                                st.error(
                                    f"Error creating campaign forecast dataframe: {str(e)}"
                                )
                                forecast_df = pd.DataFrame(
                                    {"Date": [], "Predicted Call Count": []}
                                )
                            st.dataframe(forecast_df)
                    else:
                        st.info(
                            "Campaign breakdown was not requested or no campaign data is available."
                        )
            else:
                st.error("Failed to forecast call volumes")
    else:
        # Display instructions when the page loads
        st.info(
            "👈 Use the filters in the sidebar and click 'Run Forecast' to generate call volume forecasts."
        )

        # Display sample dashboard
        st.subheader("Sample Forecast")
        st.markdown(
            """
        After running the forecast, you'll see:

        1. **Overall Forecast**: Prediction of total call volumes for the selected time period
        2. **Language Breakdown**: Separate forecasts for each language (e.g., English, Arabic)
        3. **Campaign Breakdown**: Separate forecasts for each campaign type
        4. **Actionable Recommendations**: Specific steps for staffing and resource allocation

        You can adjust the forecast parameters in the sidebar:
        - **Days to Forecast**: How many days into the future to predict
        - **Time Granularity**: Hourly, daily, or weekly predictions
        - **Forecasting Method**: Different statistical methods for prediction
        """
        )


if __name__ == "__main__":
    main()
