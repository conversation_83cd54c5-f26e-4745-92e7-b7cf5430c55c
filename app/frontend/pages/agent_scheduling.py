"""
Agent Scheduling page for the Call Flow Analytics application.
This page implements the agent scheduling model using Erlang C formula.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import os
import sys

# Try to import from parent directory first
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from app_config import API_URL
except ImportError:
    # If that fails, try to import from the current directory
    try:
        from .app_config import API_URL
    except ImportError:
        # Last resort, use a default value
        API_URL = "http://localhost:8002"

# Define API endpoints
API_ENDPOINTS = {
    "agent_scheduling": f"{API_URL}/analytics/agent-scheduling",
    "data_summary": f"{API_URL}/data/summary",
}


def make_api_request(endpoint, params=None):
    """
    Make an API request to the specified endpoint

    Args:
        endpoint: API endpoint URL
        params: Request parameters

    Returns:
        Response JSON or None if request failed
    """
    import requests
    import json

    try:
        response = requests.post(endpoint, json=params)
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"API request failed with status code {response.status_code}")
            st.error(f"Response: {response.text}")
            return None
    except Exception as e:
        st.error(f"Error making API request: {str(e)}")
        return None


def display_recommendations(recommendations):
    """
    Display recommendations in a nice format

    Args:
        recommendations: List of recommendation strings
    """
    if not recommendations:
        return

    st.subheader("📋 Recommendations")
    for i, rec in enumerate(recommendations, 1):
        st.markdown(f"**{i}.** {rec}")


def display_staffing_metrics(data):
    """
    Display staffing metrics in a nice format

    Args:
        data: Dictionary with staffing data
    """
    if not data:
        return

    # Create metrics for total staff hours and peak staffing
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            "Total Weekly Staff Hours",
            f"{data.get('total_staff_hours', 0):,}",
            help="Total staff hours required per week",
        )

    with col2:
        st.metric(
            "Average Daily Staff Hours",
            f"{data.get('avg_daily_staff_hours', 0):,}",
            help="Average staff hours required per day",
        )

    with col3:
        peak = data.get("peak_staffing", {})
        if peak:
            st.metric(
                "Peak Staffing Requirement",
                f"{peak.get('required_staff', 0)} agents",
                help=f"Maximum staffing needed on {peak.get('weekday', '')} at {peak.get('hour', 0)}:00",
            )


def display_charts(charts):
    """
    Display charts using Plotly

    Args:
        charts: List of chart data dictionaries
    """
    if not charts:
        return

    for chart in charts:
        chart_type = chart.get("chart_type")
        title = chart.get("title")
        x_label = chart.get("x_label")
        y_label = chart.get("y_label")
        data = chart.get("data", {})

        if chart_type == "bar":
            fig = px.bar(
                x=data.get("x", []),
                y=data.get("y", []),
                title=title,
                labels={"x": x_label, "y": y_label},
            )
            st.plotly_chart(fig, use_container_width=True)

        elif chart_type == "line":
            fig = px.line(
                x=data.get("x", []),
                y=data.get("y", []),
                title=title,
                labels={"x": x_label, "y": y_label},
            )
            st.plotly_chart(fig, use_container_width=True)

        elif chart_type == "heatmap":
            fig = go.Figure(
                data=go.Heatmap(
                    z=data.get("z", []),
                    x=data.get("x", []),
                    y=data.get("y", []),
                    colorscale="Viridis",
                )
            )
            fig.update_layout(
                title=title,
                xaxis_title=x_label,
                yaxis_title=y_label,
            )
            st.plotly_chart(fig, use_container_width=True)


def main():
    """Main function for the Agent Scheduling page"""

    # Main title
    st.title("👥 Agent Scheduling Model")
    st.write(
        "Optimize agent scheduling based on call volume forecasts using Erlang C model"
    )

    # Sidebar filters
    st.sidebar.header("Parameters")

    # Date range filter
    st.sidebar.subheader("Date Range")

    # Get current date and 30 days ago for default date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)

    # Date inputs
    start_date_input = st.sidebar.date_input(
        "Start Date", value=start_date, key="start_date"
    )
    end_date_input = st.sidebar.date_input("End Date", value=end_date, key="end_date")

    # Campaign filter
    st.sidebar.subheader("Filters")
    campaigns = [
        "All",
        "Transaction_English",
        "GeneralArabic",
        "TechSupp",
        "ONLINE",
        "AGENTDIRECT",
    ]
    selected_campaigns = st.sidebar.multiselect(
        "Select Campaigns", options=campaigns, default=None, key="campaigns"
    )

    # Service level parameters
    st.sidebar.subheader("Service Level Parameters")

    # Add explanation about service level
    st.sidebar.markdown(
        """
    **Service Level** is a key call center metric that measures the percentage of calls answered within a specified time threshold.

    For example, a service level of 80% with a wait time of 20 seconds means that 80% of calls should be answered within 20 seconds.

    Higher service levels require more staff but result in shorter customer wait times.
    """
    )

    target_service_level = st.sidebar.slider(
        "Target Service Level",
        min_value=0.5,
        max_value=0.95,
        value=0.8,
        step=0.05,
        format="%.0f%%",
        key="service_level",
        help="Percentage of calls to be answered within the maximum wait time",
    )

    max_wait_time = st.sidebar.slider(
        "Maximum Wait Time (seconds)",
        min_value=5,
        max_value=120,
        value=20,
        step=5,
        key="max_wait_time",
        help="Maximum acceptable wait time in seconds",
    )

    # Add example of what the selected parameters mean
    sl_example = f"With these settings, {int(target_service_level*100)}% of calls should be answered within {max_wait_time} seconds."
    st.sidebar.info(sl_example)

    # Breakdown options
    st.sidebar.subheader("Data Breakdown Options")
    by_language = st.sidebar.checkbox(
        "Break Down by Language", value=True, key="by_language"
    )

    by_campaign = st.sidebar.checkbox(
        "Break Down by Campaign", value=False, key="by_campaign"
    )

    highlight_arabic = st.sidebar.checkbox(
        "Highlight Arabic Staff Requirements",
        value=True,
        key="highlight_arabic",
        help="Show detailed Arabic staffing requirements",
    )

    # Forecasting options
    st.sidebar.subheader("Forecasting Options")
    use_forecast = st.sidebar.checkbox(
        "Use Forecast for Future Scheduling", value=True, key="use_forecast"
    )

    forecast_days = st.sidebar.slider(
        "Days to Forecast",
        min_value=1,
        max_value=30,
        value=7,
        step=1,
        key="forecast_days",
        disabled=not use_forecast,
    )

    # Shift parameters
    st.sidebar.subheader("Shift Parameters")
    shift_length = st.sidebar.slider(
        "Shift Length (hours)",
        min_value=4,
        max_value=12,
        value=8,
        step=1,
        key="shift_length",
        help="Standard length of a work shift in hours",
    )

    shifts_per_day = st.sidebar.slider(
        "Shifts per Day",
        min_value=1,
        max_value=4,
        value=3,
        step=1,
        key="shifts_per_day",
        help="Number of shifts per day (e.g., morning, afternoon, night)",
    )

    # Display options
    st.sidebar.subheader("Display Options")
    include_hourly_breakdown = st.sidebar.checkbox(
        "Include Hourly Breakdown", value=True, key="include_hourly_breakdown"
    )

    include_daily_summary = st.sidebar.checkbox(
        "Include Daily Summary", value=True, key="include_daily_summary"
    )

    include_shift_recommendations = st.sidebar.checkbox(
        "Include Shift Recommendations", value=True, key="include_shift_recommendations"
    )

    # Run scheduling button
    if st.sidebar.button("Run Scheduling", key="run_scheduling"):
        # Show spinner while loading
        with st.spinner("Optimizing agent scheduling..."):
            # Prepare request parameters
            params = {
                "start_date": (
                    start_date_input.strftime("%Y-%m-%d") if start_date_input else None
                ),
                "end_date": (
                    end_date_input.strftime("%Y-%m-%d") if end_date_input else None
                ),
                "campaigns": selected_campaigns if selected_campaigns else None,
                "target_service_level": float(target_service_level),
                "max_wait_time": int(max_wait_time),
                "by_language": by_language,
                "by_campaign": by_campaign,
                "use_forecast": use_forecast,
                "forecast_days": forecast_days,
                "shift_length": shift_length,
                "shifts_per_day": shifts_per_day,
                "highlight_arabic": highlight_arabic,
                "include_hourly_breakdown": include_hourly_breakdown,
                "include_daily_summary": include_daily_summary,
                "include_shift_recommendations": include_shift_recommendations,
            }

            # Make API request
            response = make_api_request(
                API_ENDPOINTS["agent_scheduling"], params=params
            )

            if response and response.get("success"):
                st.success("Agent scheduling completed successfully")

                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])

                # Display metrics
                display_staffing_metrics(result_data.get("overall_staffing", {}))

                # Display recommendations if available
                if "recommendations" in result_data:
                    display_recommendations(result_data["recommendations"])

                # Create tabs for different analysis sections
                tab1, tab2, tab3 = st.tabs(
                    ["Overall Staffing", "Language Breakdown", "Campaign Breakdown"]
                )

                with tab1:
                    st.subheader("Overall Staffing Requirements")
                    # Display overall staffing charts
                    overall_charts = [
                        chart
                        for chart in charts
                        if "Language" not in chart.get("title", "")
                        and "Campaign" not in chart.get("title", "")
                    ]
                    display_charts(overall_charts)

                    # Create subtabs for different tabular views
                    subtab1, subtab2, subtab3 = st.tabs(
                        ["Hourly Staffing", "Daily Summary", "Hourly by Language"]
                    )

                    with subtab1:
                        # Display hourly staffing data in a table if available
                        hourly_staffing = result_data.get("overall_staffing", {}).get(
                            "hourly_staffing", []
                        )
                        if hourly_staffing:
                            st.subheader("Hourly Staffing Details")
                            df = pd.DataFrame(hourly_staffing)
                            if not df.empty:
                                # Reorder columns for better readability
                                columns = [
                                    "WeekdayName",
                                    "Hour",
                                    "call_volume",
                                    "avg_handle_time",
                                    "required_staff",
                                ]
                                df = df[columns]
                                # Rename columns for better readability
                                df.columns = [
                                    "Day",
                                    "Hour",
                                    "Call Volume",
                                    "Avg Handle Time (sec)",
                                    "Required Staff",
                                ]

                                # Add time period for better readability
                                df["Time Period"] = df["Hour"].apply(
                                    lambda x: f"{x:02d}:00 - {(x+1):02d}:00"
                                )

                                # Reorder columns to include Time Period
                                df = df[
                                    [
                                        "Day",
                                        "Time Period",
                                        "Hour",
                                        "Call Volume",
                                        "Avg Handle Time (sec)",
                                        "Required Staff",
                                    ]
                                ]

                                # Sort by day of week and hour
                                day_order = [
                                    "Monday",
                                    "Tuesday",
                                    "Wednesday",
                                    "Thursday",
                                    "Friday",
                                    "Saturday",
                                    "Sunday",
                                ]
                                df["Day_Order"] = df["Day"].apply(
                                    lambda x: day_order.index(x)
                                )
                                df = df.sort_values(["Day_Order", "Hour"]).drop(
                                    "Day_Order", axis=1
                                )

                                st.dataframe(df, use_container_width=True)

                                # Add download button for the data
                                csv = df.to_csv(index=False).encode("utf-8")
                                st.download_button(
                                    "Download Hourly Staffing Data",
                                    csv,
                                    "hourly_staffing.csv",
                                    "text/csv",
                                    key="download-hourly-staffing",
                                )

                    with subtab2:
                        # Create a daily summary view
                        if hourly_staffing:
                            st.subheader("Daily Staffing Summary")
                            df = pd.DataFrame(hourly_staffing)
                            if not df.empty:
                                # Group by day to get daily totals and averages
                                daily_summary = (
                                    df.groupby("WeekdayName")
                                    .agg(
                                        {
                                            "call_volume": "sum",
                                            "avg_handle_time": "mean",
                                            "required_staff": ["mean", "max", "sum"],
                                        }
                                    )
                                    .reset_index()
                                )

                                # Flatten the multi-level columns
                                daily_summary.columns = [
                                    "Day",
                                    "Total Call Volume",
                                    "Avg Handle Time (sec)",
                                    "Avg Staff Required",
                                    "Peak Staff Required",
                                    "Total Staff Hours",
                                ]

                                # Round numeric columns for better readability
                                daily_summary["Avg Handle Time (sec)"] = daily_summary[
                                    "Avg Handle Time (sec)"
                                ].round(1)
                                daily_summary["Avg Staff Required"] = daily_summary[
                                    "Avg Staff Required"
                                ].round(1)
                                daily_summary["Total Staff Hours"] = daily_summary[
                                    "Total Staff Hours"
                                ].astype(int)

                                # Sort by day of week
                                day_order = [
                                    "Monday",
                                    "Tuesday",
                                    "Wednesday",
                                    "Thursday",
                                    "Friday",
                                    "Saturday",
                                    "Sunday",
                                ]
                                daily_summary["Day_Order"] = daily_summary["Day"].apply(
                                    lambda x: day_order.index(x)
                                )
                                daily_summary = daily_summary.sort_values(
                                    "Day_Order"
                                ).drop("Day_Order", axis=1)

                                st.dataframe(daily_summary, use_container_width=True)

                                # Add download button for the data
                                csv = daily_summary.to_csv(index=False).encode("utf-8")
                                st.download_button(
                                    "Download Daily Summary Data",
                                    csv,
                                    "daily_staffing_summary.csv",
                                    "text/csv",
                                    key="download-daily-summary",
                                )

                    with subtab3:
                        # Create a language-wise hourly staffing view
                        if by_language:
                            st.subheader("Hourly Staffing by Language")

                            # Get language-specific staffing data
                            language_staffing = result_data.get(
                                "language_staffing", {}
                            ).get("by_language", {})

                            if language_staffing:
                                # Create a combined dataframe with language information
                                combined_df = pd.DataFrame()

                                for (
                                    language,
                                    staffing_data,
                                ) in language_staffing.items():
                                    if staffing_data:
                                        lang_df = pd.DataFrame(staffing_data)
                                        lang_df["Language"] = language
                                        combined_df = pd.concat([combined_df, lang_df])

                                if not combined_df.empty:
                                    # Reorder and rename columns for better readability
                                    columns = [
                                        "Language",
                                        "WeekdayName",
                                        "Hour",
                                        "call_volume",
                                        "avg_handle_time",
                                        "required_staff",
                                    ]
                                    combined_df = combined_df[columns]

                                    combined_df.columns = [
                                        "Language",
                                        "Day",
                                        "Hour",
                                        "Call Volume",
                                        "Avg Handle Time (sec)",
                                        "Required Staff",
                                    ]

                                    # Add time period for better readability
                                    combined_df["Time Period"] = combined_df[
                                        "Hour"
                                    ].apply(lambda x: f"{x:02d}:00 - {(x+1):02d}:00")

                                    # Reorder columns to include Time Period
                                    combined_df = combined_df[
                                        [
                                            "Language",
                                            "Day",
                                            "Time Period",
                                            "Hour",
                                            "Call Volume",
                                            "Avg Handle Time (sec)",
                                            "Required Staff",
                                        ]
                                    ]

                                    # Sort by language, day of week, and hour
                                    day_order = [
                                        "Monday",
                                        "Tuesday",
                                        "Wednesday",
                                        "Thursday",
                                        "Friday",
                                        "Saturday",
                                        "Sunday",
                                    ]
                                    combined_df["Day_Order"] = combined_df["Day"].apply(
                                        lambda x: day_order.index(x)
                                    )
                                    combined_df = combined_df.sort_values(
                                        ["Language", "Day_Order", "Hour"]
                                    ).drop("Day_Order", axis=1)

                                    st.dataframe(combined_df, use_container_width=True)

                                    # Add download button for the data
                                    csv = combined_df.to_csv(index=False).encode(
                                        "utf-8"
                                    )
                                    st.download_button(
                                        "Download Language-wise Staffing Data",
                                        csv,
                                        "language_staffing.csv",
                                        "text/csv",
                                        key="download-language-staffing",
                                    )

                                    # Create a pivot table view for easier comparison
                                    st.subheader("Language Comparison by Day and Hour")

                                    # Create a pivot table with languages as columns
                                    pivot_df = combined_df.pivot_table(
                                        index=["Day", "Hour", "Time Period"],
                                        columns="Language",
                                        values="Required Staff",
                                        aggfunc="sum",
                                    ).reset_index()

                                    # Sort by day of week and hour
                                    day_order = [
                                        "Monday",
                                        "Tuesday",
                                        "Wednesday",
                                        "Thursday",
                                        "Friday",
                                        "Saturday",
                                        "Sunday",
                                    ]
                                    pivot_df["Day_Order"] = pivot_df["Day"].apply(
                                        lambda x: day_order.index(x)
                                    )
                                    pivot_df = pivot_df.sort_values(
                                        ["Day_Order", "Hour"]
                                    ).drop("Day_Order", axis=1)

                                    st.dataframe(pivot_df, use_container_width=True)

                                    # Add download button for the pivot data
                                    csv = pivot_df.to_csv(index=False).encode("utf-8")
                                    st.download_button(
                                        "Download Language Comparison Data",
                                        csv,
                                        "language_comparison.csv",
                                        "text/csv",
                                        key="download-language-comparison",
                                    )
                            else:
                                st.info("No language-specific staffing data available.")
                        else:
                            st.info(
                                "Language breakdown was not selected. Enable it in the sidebar to see language-specific staffing requirements."
                            )

                with tab2:
                    if by_language:
                        st.subheader("Staffing by Language")
                        # Display language breakdown charts
                        language_charts = [
                            chart
                            for chart in charts
                            if "Language" in chart.get("title", "")
                        ]
                        display_charts(language_charts)

                        # Display language totals if available
                        language_totals = result_data.get("language_staffing", {}).get(
                            "language_totals", {}
                        )
                        if language_totals:
                            st.subheader("Language Staffing Summary")
                            lang_df = pd.DataFrame(
                                {
                                    "Language": list(language_totals.keys()),
                                    "Total Staff Hours": list(language_totals.values()),
                                    "Avg Daily Hours": [
                                        hours / 7 for hours in language_totals.values()
                                    ],
                                    f"Avg Staff per Shift ({shift_length}h)": [
                                        round(hours / 7 / shift_length, 1)
                                        for hours in language_totals.values()
                                    ],
                                }
                            )
                            st.dataframe(lang_df, use_container_width=True)

                            # Add download button for the language summary
                            csv = lang_df.to_csv(index=False).encode("utf-8")
                            st.download_button(
                                "Download Language Summary",
                                csv,
                                "language_staffing_summary.csv",
                                "text/csv",
                                key="download-language-summary",
                            )

                            # Create detailed Arabic staffing section if Arabic is in the languages
                            if "Arabic" in language_totals:
                                st.subheader("Arabic Staff Requirements by Day")

                                # Get Arabic staffing data
                                arabic_staffing = (
                                    result_data.get("language_staffing", {})
                                    .get("by_language", {})
                                    .get("Arabic", [])
                                )

                                if arabic_staffing:
                                    arabic_df = pd.DataFrame(arabic_staffing)

                                    # Group by day to get daily totals and averages for Arabic
                                    arabic_daily = (
                                        arabic_df.groupby("WeekdayName")
                                        .agg(
                                            {
                                                "call_volume": "sum",
                                                "avg_handle_time": "mean",
                                                "required_staff": [
                                                    "mean",
                                                    "max",
                                                    "sum",
                                                ],
                                            }
                                        )
                                        .reset_index()
                                    )

                                    # Flatten the multi-level columns
                                    arabic_daily.columns = [
                                        "Day",
                                        "Total Arabic Calls",
                                        "Avg Handle Time (sec)",
                                        "Avg Arabic Staff Required",
                                        "Peak Arabic Staff Required",
                                        "Total Arabic Staff Hours",
                                    ]

                                    # Calculate staff per shift using the selected shift parameters
                                    arabic_daily[
                                        f"Arabic Staff per Shift ({shift_length}h)"
                                    ] = (
                                        arabic_daily["Total Arabic Staff Hours"]
                                        / shifts_per_day
                                    ).round(
                                        1
                                    )

                                    # Round numeric columns for better readability
                                    arabic_daily["Avg Handle Time (sec)"] = (
                                        arabic_daily["Avg Handle Time (sec)"].round(1)
                                    )
                                    arabic_daily["Avg Arabic Staff Required"] = (
                                        arabic_daily["Avg Arabic Staff Required"].round(
                                            1
                                        )
                                    )
                                    arabic_daily["Total Arabic Staff Hours"] = (
                                        arabic_daily["Total Arabic Staff Hours"].astype(
                                            int
                                        )
                                    )

                                    # Sort by day of week
                                    day_order = [
                                        "Monday",
                                        "Tuesday",
                                        "Wednesday",
                                        "Thursday",
                                        "Friday",
                                        "Saturday",
                                        "Sunday",
                                    ]
                                    arabic_daily["Day_Order"] = arabic_daily[
                                        "Day"
                                    ].apply(lambda x: day_order.index(x))
                                    arabic_daily = arabic_daily.sort_values(
                                        "Day_Order"
                                    ).drop("Day_Order", axis=1)

                                    st.dataframe(arabic_daily, use_container_width=True)

                                    # Add download button for Arabic daily data
                                    csv = arabic_daily.to_csv(index=False).encode(
                                        "utf-8"
                                    )
                                    st.download_button(
                                        "Download Arabic Daily Staffing",
                                        csv,
                                        "arabic_daily_staffing.csv",
                                        "text/csv",
                                        key="download-arabic-daily",
                                    )

                                    # Create hourly breakdown for Arabic
                                    st.subheader("Hourly Arabic Staff Requirements")

                                    # Prepare hourly data for Arabic
                                    arabic_hourly = arabic_df.copy()
                                    arabic_hourly["Time Period"] = arabic_hourly[
                                        "Hour"
                                    ].apply(lambda x: f"{x:02d}:00 - {(x+1):02d}:00")

                                    # Rename columns for better readability
                                    arabic_hourly = arabic_hourly.rename(
                                        columns={
                                            "WeekdayName": "Day",
                                            "call_volume": "Arabic Call Volume",
                                            "avg_handle_time": "Avg Handle Time (sec)",
                                            "required_staff": "Required Arabic Staff",
                                        }
                                    )

                                    # Select and reorder columns
                                    arabic_hourly = arabic_hourly[
                                        [
                                            "Day",
                                            "Time Period",
                                            "Hour",
                                            "Arabic Call Volume",
                                            "Avg Handle Time (sec)",
                                            "Required Arabic Staff",
                                        ]
                                    ]

                                    # Sort by day of week and hour
                                    day_order = [
                                        "Monday",
                                        "Tuesday",
                                        "Wednesday",
                                        "Thursday",
                                        "Friday",
                                        "Saturday",
                                        "Sunday",
                                    ]
                                    arabic_hourly["Day_Order"] = arabic_hourly[
                                        "Day"
                                    ].apply(lambda x: day_order.index(x))
                                    arabic_hourly = arabic_hourly.sort_values(
                                        ["Day_Order", "Hour"]
                                    ).drop("Day_Order", axis=1)

                                    st.dataframe(
                                        arabic_hourly, use_container_width=True
                                    )

                                    # Add download button for Arabic hourly data
                                    csv = arabic_hourly.to_csv(index=False).encode(
                                        "utf-8"
                                    )
                                    st.download_button(
                                        "Download Arabic Hourly Staffing",
                                        csv,
                                        "arabic_hourly_staffing.csv",
                                        "text/csv",
                                        key="download-arabic-hourly",
                                    )
                    else:
                        st.info(
                            "Language breakdown was not selected. Enable it in the sidebar to see language-specific staffing requirements."
                        )

                with tab3:
                    if by_campaign:
                        st.subheader("Staffing by Campaign")
                        # Display campaign breakdown charts
                        campaign_charts = [
                            chart
                            for chart in charts
                            if "Campaign" in chart.get("title", "")
                        ]
                        display_charts(campaign_charts)

                        # Display campaign totals if available
                        campaign_totals = result_data.get("campaign_staffing", {}).get(
                            "campaign_totals", {}
                        )
                        if campaign_totals:
                            st.subheader("Campaign Staffing Summary")
                            camp_df = pd.DataFrame(
                                {
                                    "Campaign": list(campaign_totals.keys()),
                                    "Total Staff Hours": list(campaign_totals.values()),
                                    "Avg Daily Hours": [
                                        hours / 7 for hours in campaign_totals.values()
                                    ],
                                }
                            )
                            st.dataframe(camp_df, use_container_width=True)
                    else:
                        st.info(
                            "Campaign breakdown was not selected. Enable it in the sidebar to see campaign-specific staffing requirements."
                        )
    else:
        # Display sample dashboard
        st.subheader("About Agent Scheduling")
        st.markdown(
            """
        This tool uses the Erlang C formula to calculate optimal staffing levels based on:

        1. **Call Volume**: Historical or forecasted call volumes
        2. **Average Handle Time (AHT)**: How long each call takes
        3. **Service Level Target**: Percentage of calls to answer within a time threshold
        4. **Maximum Wait Time**: How long customers should wait at most

        ### Key Features:

        - **Overall Staffing**: See required staff by hour and day
        - **Language Breakdown**: Optimize staffing for different languages
        - **Campaign Breakdown**: Allocate staff to different campaigns
        - **Arabic Staff Focus**: Detailed breakdown of Arabic staffing needs
        - **Actionable Recommendations**: Get specific staffing suggestions

        ### Understanding Service Level

        Service Level is a key call center metric that measures the percentage of calls answered within a specified time threshold.

        For example, a service level of 80% with a wait time of 20 seconds means that 80% of calls should be answered within 20 seconds.

        The formula is:
        ```
        Service Level = (Calls Answered Within Threshold ÷ Total Calls) × 100%
        ```

        ### How the Erlang C Model Works:

        The Erlang C formula calculates the probability of a caller waiting in a queue in a call center. It helps determine:

        - Minimum number of agents needed to meet service level targets
        - Probability of calls being answered within a specific time
        - Expected wait times for callers

        Click "Run Scheduling" to generate your staffing plan.
        """
        )


if __name__ == "__main__":
    main()
