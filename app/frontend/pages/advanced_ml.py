import streamlit as st
import pandas as pd
import numpy as np
import requests
import json
import os
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import io
from app.shared.config import API_URL

# Note: Page config is now set in the main app.py file
# Do not set page config here to avoid conflicts

# Define API endpoints
API_ENDPOINTS = {
    "advanced_call_volume": f"{API_URL}/advanced/call-volume-prediction",
    "train_model": f"{API_URL}/advanced/train-model",
    "data_summary": f"{API_URL}/data/summary",
}


# Helper functions
def make_api_request(endpoint, method="GET", data=None, files=None, params=None):
    """
    Make a request to the API

    Args:
        endpoint: API endpoint
        method: HTTP method
        data: Request data
        files: Files to upload
        params: Query parameters for GET requests

    Returns:
        API response
    """
    try:
        if method.upper() == "GET":
            response = requests.get(endpoint, params=params)
        elif method.upper() == "POST":
            if files:
                response = requests.post(endpoint, files=files)
            else:
                response = requests.post(endpoint, json=data)
        else:
            st.error(f"Unsupported method: {method}")
            return None

        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"API Error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        st.error(f"Error making API request: {str(e)}")
        return None


def render_chart(chart_data):
    """
    Render a chart based on chart data

    Args:
        chart_data: Chart data from API
    """
    chart_type = chart_data.get("chart_type", "")
    title = chart_data.get("title", "")
    x_label = chart_data.get("x_label", "")
    y_label = chart_data.get("y_label", "")
    data = chart_data.get("data", {})

    if not data:
        st.warning("No data available for chart")
        return

    if chart_type == "bar":
        fig = px.bar(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "line":
        if "series" in data:
            # Multiple series line chart
            df = pd.DataFrame(
                {
                    "x": data.get("x", []),
                    "y": data.get("y", []),
                    "series": data.get("series", []),
                }
            )
            fig = px.line(
                df,
                x="x",
                y="y",
                color="series",
                title=title,
                labels={"x": x_label, "y": y_label, "series": "Series"},
            )
        else:
            # Single series line chart
            fig = px.line(
                x=data.get("x", []),
                y=data.get("y", []),
                title=title,
                labels={"x": x_label, "y": y_label},
            )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "pie":
        fig = px.pie(
            names=data.get("labels", []), values=data.get("values", []), title=title
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "scatter":
        fig = px.scatter(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "heatmap":
        fig = px.imshow(
            data.get("z", []),
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    else:
        st.warning(f"Unsupported chart type: {chart_type}")


# Main content
st.title("Advanced ML Models")
st.write("Use advanced machine learning models for predictive analytics")

# Get data summary for metadata
summary_response = make_api_request(API_ENDPOINTS["data_summary"])
if summary_response and summary_response.get("success"):
    summary_data = summary_response.get("data", {}).get("summary", {})

    if summary_data:
        st.info(
            f"Data available from {summary_data.get('inbound', {}).get('date_range', ['', ''])[0]} to {summary_data.get('inbound', {}).get('date_range', ['', ''])[1]}"
        )

# Create tabs for different ML models
tab1, tab2, tab3 = st.tabs(
    ["Call Volume Prediction", "Model Training", "Model Evaluation"]
)

with tab1:
    st.header("Advanced Call Volume Prediction")
    st.write("Predict call volumes using advanced machine learning models")

    # Form for prediction parameters
    with st.form("advanced_call_volume_form"):
        # Date range
        col1, col2 = st.columns(2)
        start_date = col1.date_input(
            "Start Date (for training data)", datetime.now() - timedelta(days=90)
        )
        end_date = col2.date_input("End Date (for training data)", datetime.now())

        # Model parameters
        col3, col4 = st.columns(2)
        granularity = col3.selectbox(
            "Prediction Granularity", ["hourly", "daily", "weekly", "monthly"], index=1
        )
        model_type = col4.selectbox(
            "Model Type",
            ["linear", "rf", "gbm", "xgb", "lgb", "ensemble"],
            index=5,
            help="Linear: Linear Regression, RF: Random Forest, GBM: Gradient Boosting, XGB: XGBoost, LGB: LightGBM, Ensemble: Combination of models",
        )

        # Prediction parameters
        col5, col6 = st.columns(2)
        periods = col5.number_input(
            "Forecast Periods", min_value=1, max_value=90, value=14
        )
        retrain = col6.checkbox(
            "Retrain Model",
            value=False,
            help="Force model retraining even if a trained model exists",
        )

        # Filters
        campaigns = st.multiselect(
            "Filter by Campaigns",
            [
                "All",
                "Transaction_English",
                "GeneralArabic",
                "TechSupp",
                "ONLINE",
                "AGENTDIRECT",
            ],
        )
        languages = st.multiselect("Filter by Languages", ["All", "English", "Arabic"])

        submit_button = st.form_submit_button("Predict")

    if submit_button:
        # Prepare request data
        request_data = {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "granularity": granularity,
            "model_type": model_type,
            "periods": periods,
            "retrain": retrain,
        }

        # Add filters if not "All"
        if campaigns and "All" not in campaigns:
            request_data["campaigns"] = campaigns
        if languages and "All" not in languages:
            request_data["languages"] = languages

        # Make API request
        with st.spinner(f"Predicting call volumes using {model_type} model..."):
            response = make_api_request(
                API_ENDPOINTS["advanced_call_volume"], method="POST", data=request_data
            )

            if response and response.get("success"):
                st.success("Prediction completed successfully")

                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])

                # Display historical and forecast data
                if "historical" in result_data and "forecast" in result_data:
                    st.subheader("Call Volume Forecast")

                    # Create tabs for chart and data
                    tab1, tab2 = st.tabs(["Chart", "Data"])

                    with tab1:
                        # Render charts
                        if charts:
                            for chart in charts:
                                render_chart(chart)
                        else:
                            st.info("No charts available")

                    with tab2:
                        # Display forecast data
                        st.subheader("Forecast Data")
                        forecast_df = pd.DataFrame(result_data["forecast"])
                        st.dataframe(forecast_df)

                        # Display historical data
                        st.subheader("Historical Data")
                        historical_df = pd.DataFrame(result_data["historical"])
                        st.dataframe(historical_df)

                        # Display model info
                        st.subheader("Model Information")
                        st.write(
                            f"Model Type: {result_data.get('model_type', model_type)}"
                        )
                        st.write(
                            f"Granularity: {result_data.get('granularity', granularity)}"
                        )
                else:
                    st.info("No prediction data available")
            else:
                st.error("Failed to predict call volumes")

with tab2:
    st.header("Model Training")
    st.write("Train machine learning models for call flow analytics")

    # Form for model training
    with st.form("model_training_form"):
        # Model selection
        model_name = st.selectbox(
            "Model to Train", ["call_volume", "agent_performance"], index=0
        )

        # Date range
        col1, col2 = st.columns(2)
        start_date = col1.date_input(
            "Start Date (for training data)", datetime.now() - timedelta(days=90)
        )
        end_date = col2.date_input("End Date (for training data)", datetime.now())

        # Model parameters
        col3, col4 = st.columns(2)
        model_type = col3.selectbox(
            "Model Type", ["linear", "rf", "gbm", "xgb", "lgb", "ensemble"], index=5
        )

        # Additional parameters based on model
        parameters = {}
        if model_name == "call_volume":
            granularity = col4.selectbox(
                "Data Granularity", ["hourly", "daily", "weekly", "monthly"], index=1
            )
            parameters["granularity"] = granularity

        # Filters
        campaigns = st.multiselect(
            "Filter by Campaigns",
            [
                "All",
                "Transaction_English",
                "GeneralArabic",
                "TechSupp",
                "ONLINE",
                "AGENTDIRECT",
            ],
        )
        languages = st.multiselect("Filter by Languages", ["All", "English", "Arabic"])

        submit_button = st.form_submit_button("Train Model")

    if submit_button:
        # Prepare request data
        request_data = {
            "model_name": model_name,
            "model_type": model_type,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "parameters": parameters,
        }

        # Add filters if not "All"
        if campaigns and "All" not in campaigns:
            request_data["campaigns"] = campaigns
        if languages and "All" not in languages:
            request_data["languages"] = languages

        # Make API request
        with st.spinner(f"Training {model_name} model..."):
            response = make_api_request(
                API_ENDPOINTS["train_model"], method="POST", data=request_data
            )

            if response and response.get("success"):
                st.success(f"Model {model_name} trained successfully")

                # Display results
                result_data = response.get("data", {})

                # Display metrics if available
                if "metrics" in result_data:
                    st.subheader("Model Metrics")
                    metrics = result_data["metrics"]

                    # Create metrics display
                    cols = st.columns(len(metrics))
                    for i, (metric_name, metric_value) in enumerate(metrics.items()):
                        cols[i].metric(metric_name.upper(), f"{metric_value:.4f}")

                # Display model info
                st.subheader("Model Information")
                st.json(result_data)
            else:
                st.error(f"Failed to train {model_name} model")

with tab3:
    st.header("Model Evaluation")
    st.write("Evaluate trained machine learning models")

    st.info("Model evaluation functionality will be implemented in a future update.")

    # Placeholder for model evaluation
    st.write("This section will include:")
    st.write("- Model performance metrics")
    st.write("- Feature importance analysis")
    st.write("- Comparison between different model types")
    st.write("- Prediction error analysis")
