"""
Enhanced Missed Call Dashboard for the Call Flow Analytics application.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import os
import sys

# Try to import from parent directory first
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from app_config import API_URL
except ImportError:
    # If that fails, try to import from the current directory
    try:
        from .app_config import API_URL
    except ImportError:
        # Last resort, use a default value
        API_URL = "http://localhost:8002"

# Define API endpoints
API_ENDPOINTS = {
    "enhanced_missed_call": f"{API_URL}/analytics/enhanced-missed-call",
    "data_summary": f"{API_URL}/data/summary",
}


def make_api_request(endpoint, params=None, method="POST"):
    """
    Make a request to the API

    Args:
        endpoint: API endpoint
        params: Request parameters
        method: HTTP method

    Returns:
        Response JSON or None if error
    """
    import requests

    try:
        if method == "GET":
            response = requests.get(endpoint, json=params)
        else:
            response = requests.post(endpoint, json=params)

        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Error making API request: {str(e)}")
        return None


def render_chart(chart_data):
    """
    Render a chart based on chart data

    Args:
        chart_data: Chart data from API
    """
    chart_type = chart_data.get("chart_type", "")
    title = chart_data.get("title", "")
    x_label = chart_data.get("x_label", "")
    y_label = chart_data.get("y_label", "")
    data = chart_data.get("data", {})

    if not data:
        st.warning("No data available for chart")
        return

    if chart_type == "bar":
        fig = px.bar(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "line":
        fig = px.line(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "pie":
        fig = px.pie(
            names=data.get("labels", []),
            values=data.get("values", []),
            title=title,
        )
        st.plotly_chart(fig, use_container_width=True)
        
    elif chart_type == "area":
        fig = px.area(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    else:
        st.warning(f"Unsupported chart type: {chart_type}")


def display_recommendations(recommendations):
    """
    Display recommendations in a nice format

    Args:
        recommendations: List of recommendation strings
    """
    if not recommendations:
        st.info("No recommendations available")
        return
    
    st.subheader("📋 Actionable Recommendations")
    
    for i, recommendation in enumerate(recommendations):
        with st.container():
            st.markdown(f"**{i+1}. {recommendation}**")
            st.markdown("---")


def display_missed_call_metrics(metrics):
    """
    Display missed call metrics in a nice format

    Args:
        metrics: Dictionary of metrics
    """
    if not metrics:
        return
    
    # Create columns for metrics
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            label="Total Missed Calls",
            value=metrics.get("total_missed_calls", 0)
        )
    
    with col2:
        if "missed_call_rate" in metrics:
            st.metric(
                label="Missed Call Rate",
                value=f"{metrics['missed_call_rate']:.1%}"
            )
    
    with col3:
        if "callback_metrics" in metrics and "callback_rate" in metrics["callback_metrics"]:
            st.metric(
                label="Callback Rate",
                value=f"{metrics['callback_metrics']['callback_rate']:.1%}"
            )


def main():
    """Main function for the Enhanced Missed Call Dashboard"""
    
    # Main title
    st.title("📞 Enhanced Missed Call Dashboard")
    st.write("Analyze missed calls with actionable recommendations")
    
    # Sidebar filters
    st.sidebar.header("Filters")
    
    # Date range filter
    st.sidebar.subheader("Date Range")
    
    # Get current date and 30 days ago for default date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    # Date inputs
    start_date_input = st.sidebar.date_input(
        "Start Date", 
        value=start_date,
        key="start_date"
    )
    end_date_input = st.sidebar.date_input(
        "End Date", 
        value=end_date,
        key="end_date"
    )
    
    # Campaign filter
    st.sidebar.subheader("Campaigns")
    
    # Get data summary for available campaigns
    summary_response = make_api_request(API_ENDPOINTS["data_summary"], method="GET")
    available_campaigns = []
    
    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})
        if "missed" in summary_data and summary_data["missed"].get("campaigns"):
            available_campaigns = summary_data["missed"]["campaigns"]
    
    selected_campaigns = st.sidebar.multiselect(
        "Select Campaigns",
        options=available_campaigns,
        default=None,
        key="campaigns"
    )
    
    # Include callbacks option
    include_callbacks = st.sidebar.checkbox(
        "Include Callback Analysis",
        value=True,
        key="include_callbacks"
    )
    
    # Run analysis button
    if st.sidebar.button("Run Analysis", key="run_analysis"):
        # Show spinner while loading
        with st.spinner("Analyzing missed calls..."):
            # Prepare request parameters
            params = {
                "start_date": start_date_input.strftime("%Y-%m-%d") if start_date_input else None,
                "end_date": end_date_input.strftime("%Y-%m-%d") if end_date_input else None,
                "campaigns": selected_campaigns if selected_campaigns else None,
                "include_callbacks": include_callbacks,
                "include_recommendations": True
            }
            
            # Make API request
            response = make_api_request(API_ENDPOINTS["enhanced_missed_call"], params=params)
            
            if response and response.get("success"):
                st.success("Analysis completed successfully")
                
                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])
                
                # Display metrics
                display_missed_call_metrics(result_data)
                
                # Display recommendations if available
                if "recommendations" in result_data:
                    display_recommendations(result_data["recommendations"])
                
                # Create tabs for different analysis sections
                tab1, tab2, tab3 = st.tabs([
                    "Time Analysis", 
                    "Campaign & Reason Analysis", 
                    "Callback Analysis"
                ])
                
                with tab1:
                    st.header("Missed Calls by Time")
                    
                    # Display hourly chart
                    hourly_chart = next((chart for chart in charts if "Hour" in chart.get("title", "")), None)
                    if hourly_chart:
                        render_chart(hourly_chart)
                    
                    # Display weekday chart
                    weekday_chart = next((chart for chart in charts if "Day of Week" in chart.get("title", "")), None)
                    if weekday_chart:
                        render_chart(weekday_chart)
                    
                    # Display trend chart
                    trend_chart = next((chart for chart in charts if "Over Time" in chart.get("title", "")), None)
                    if trend_chart:
                        render_chart(trend_chart)
                
                with tab2:
                    st.header("Campaign & Reason Analysis")
                    
                    # Display campaign chart
                    campaign_chart = next((chart for chart in charts if "Campaign" in chart.get("title", "")), None)
                    if campaign_chart:
                        render_chart(campaign_chart)
                    
                    # Display reason chart
                    reason_chart = next((chart for chart in charts if "Reason" in chart.get("title", "")), None)
                    if reason_chart:
                        render_chart(reason_chart)
                    
                    # Display campaign data if available
                    if "campaign_missed" in result_data:
                        st.subheader("Campaign Data")
                        campaign_df = pd.DataFrame(result_data["campaign_missed"])
                        st.dataframe(campaign_df)
                
                with tab3:
                    st.header("Callback Analysis")
                    
                    if include_callbacks:
                        # Display callback chart
                        callback_chart = next((chart for chart in charts if "Callback" in chart.get("title", "")), None)
                        if callback_chart:
                            render_chart(callback_chart)
                        
                        # Display callback metrics
                        if "callback_metrics" in result_data:
                            st.subheader("Callback Metrics")
                            callback_metrics = result_data["callback_metrics"]
                            
                            col1, col2 = st.columns(2)
                            with col1:
                                st.metric(
                                    label="Callbacks Made",
                                    value=callback_metrics.get("callback_count", 0)
                                )
                            with col2:
                                st.metric(
                                    label="Callback Rate",
                                    value=f"{callback_metrics.get('callback_rate', 0):.1%}"
                                )
                    else:
                        st.info("Callback analysis was not included. Enable it in the sidebar to see callback metrics.")
            else:
                st.error("Failed to analyze missed calls")
    else:
        # Display instructions when the page loads
        st.info("👈 Use the filters in the sidebar and click 'Run Analysis' to generate missed call analytics with actionable recommendations.")
        
        # Display sample dashboard
        st.subheader("Sample Dashboard")
        st.markdown("""
        After running the analysis, you'll see:
        
        1. **Key Metrics**: Total missed calls, missed call rate, and callback rate
        2. **Time Analysis**: Missed calls by hour of day, day of week, and trends over time
        3. **Campaign & Reason Analysis**: Breakdown by campaign and reason for missed calls
        4. **Callback Analysis**: Callback metrics and performance
        5. **Actionable Recommendations**: Specific steps to reduce missed calls
        """)


if __name__ == "__main__":
    main()
