"""
Comprehensive Call Analytics page for the Call Flow Analytics application.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import os
import sys

# Try to import from parent directory first
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from app_config import API_URL
except ImportError:
    # If that fails, try to import from the current directory
    try:
        from .app_config import API_URL
    except ImportError:
        # Last resort, use a default value
        API_URL = "http://localhost:8002"

# Note: We don't set page config here as it's already set in the main app.py file
# This prevents the "set_page_config() can only be called once per app" error

# Define API endpoints
API_ENDPOINTS = {
    "call_analytics": f"{API_URL}/analytics/call-analytics",
    "data_summary": f"{API_URL}/data/summary",
}


def make_api_request(endpoint, params=None, method="POST"):
    """
    Make a request to the API

    Args:
        endpoint: API endpoint
        params: Request parameters
        method: HTTP method

    Returns:
        Response JSON or None if error
    """
    import requests

    try:
        if method == "GET":
            response = requests.get(endpoint, json=params)
        else:
            response = requests.post(endpoint, json=params)

        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Error making API request: {str(e)}")
        return None


def render_chart(chart_data):
    """
    Render a chart based on chart data

    Args:
        chart_data: Chart data from API
    """
    chart_type = chart_data.get("chart_type", "")
    title = chart_data.get("title", "")
    x_label = chart_data.get("x_label", "")
    y_label = chart_data.get("y_label", "")
    data = chart_data.get("data", {})

    if not data:
        st.warning("No data available for chart")
        return

    if chart_type == "bar":
        fig = px.bar(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "line":
        fig = px.line(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "pie":
        fig = px.pie(
            names=data.get("labels", []),
            values=data.get("values", []),
            title=title,
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "area":
        fig = px.area(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    else:
        st.warning(f"Unsupported chart type: {chart_type}")


def display_recommendations(recommendations):
    """
    Display recommendations in a nice format

    Args:
        recommendations: List of recommendation strings
    """
    if not recommendations:
        st.info("No recommendations available")
        return

    st.subheader("📋 Actionable Recommendations")

    for i, recommendation in enumerate(recommendations):
        with st.container():
            st.markdown(f"**{i+1}. {recommendation}**")
            st.markdown("---")


def main():
    """Main function for the Call Analytics page"""

    # Main title
    st.title("📊 Comprehensive Call Analytics")
    st.write("Analyze call data with actionable recommendations")

    # Sidebar filters
    st.sidebar.header("Filters")

    # Date range filter
    st.sidebar.subheader("Date Range")

    # Get current date and 30 days ago for default date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)

    # Date inputs
    start_date_input = st.sidebar.date_input(
        "Start Date", value=start_date, key="start_date"
    )
    end_date_input = st.sidebar.date_input("End Date", value=end_date, key="end_date")

    # Campaign filter
    st.sidebar.subheader("Campaigns")

    # Get data summary for available campaigns
    summary_response = make_api_request(API_ENDPOINTS["data_summary"], method="GET")
    available_campaigns = []

    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})
        if "inbound" in summary_data and summary_data["inbound"].get("campaigns"):
            available_campaigns = summary_data["inbound"]["campaigns"]

    selected_campaigns = st.sidebar.multiselect(
        "Select Campaigns", options=available_campaigns, default=None, key="campaigns"
    )

    # Agent filter
    st.sidebar.subheader("Agents")

    # Get available agents from summary
    available_agents = []

    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})
        if "inbound" in summary_data and summary_data["inbound"].get("users"):
            available_agents = summary_data["inbound"]["users"]

    selected_agents = st.sidebar.multiselect(
        "Select Agents", options=available_agents, default=None, key="agents"
    )

    # Run analysis button
    if st.sidebar.button("Run Analysis", key="run_analysis"):
        # Show spinner while loading
        with st.spinner("Running comprehensive call analytics..."):
            # Prepare request parameters
            params = {
                "start_date": (
                    start_date_input.strftime("%Y-%m-%d") if start_date_input else None
                ),
                "end_date": (
                    end_date_input.strftime("%Y-%m-%d") if end_date_input else None
                ),
                "campaigns": selected_campaigns if selected_campaigns else None,
                "agents": selected_agents if selected_agents else None,
                "include_recommendations": True,
            }

            # Make API request
            response = make_api_request(API_ENDPOINTS["call_analytics"], params=params)

            if response and response.get("success"):
                st.success("Analysis completed successfully")

                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])

                # Display recommendations if available
                if "recommendations" in result_data:
                    display_recommendations(result_data["recommendations"])

                # Create tabs for different analysis sections
                tab1, tab2, tab3, tab4 = st.tabs(
                    [
                        "Call Volume",
                        "Agent Performance",
                        "Call Status & Campaigns",
                        "Language & Queue Analysis",
                    ]
                )

                with tab1:
                    st.header("Call Volume Analysis")

                    # Display hourly volume chart
                    if charts and any(
                        chart.get("title") == "Call Volume by Hour" for chart in charts
                    ):
                        hourly_chart = next(
                            chart
                            for chart in charts
                            if chart.get("title") == "Call Volume by Hour"
                        )
                        render_chart(hourly_chart)

                    # Display forecasting chart
                    if charts and any(
                        chart.get("title") == "Cumulative Call Volume Over Time"
                        for chart in charts
                    ):
                        forecast_chart = next(
                            chart
                            for chart in charts
                            if chart.get("title") == "Cumulative Call Volume Over Time"
                        )
                        render_chart(forecast_chart)

                with tab2:
                    st.header("Agent Performance Analysis")

                    # Display agent performance charts
                    agent_charts = [
                        chart for chart in charts if "Agent" in chart.get("title", "")
                    ]
                    for chart in agent_charts:
                        render_chart(chart)

                    # Display agent performance data if available
                    if "agent_performance" in result_data:
                        st.subheader("Agent Performance Data")
                        agent_df = pd.DataFrame(result_data["agent_performance"])
                        st.dataframe(agent_df)

                with tab3:
                    st.header("Call Status & Campaign Analysis")

                    # Display call status chart
                    if charts and any(
                        chart.get("title") == "Call Status Distribution"
                        for chart in charts
                    ):
                        status_chart = next(
                            chart
                            for chart in charts
                            if chart.get("title") == "Call Status Distribution"
                        )
                        render_chart(status_chart)

                    # Display campaign chart
                    if charts and any(
                        chart.get("title") == "Call Distribution by Campaign"
                        for chart in charts
                    ):
                        campaign_chart = next(
                            chart
                            for chart in charts
                            if chart.get("title") == "Call Distribution by Campaign"
                        )
                        render_chart(campaign_chart)

                with tab4:
                    st.header("Language & Queue Analysis")

                    # Display language distribution chart
                    if charts and any(
                        chart.get("title") == "Call Distribution by Language"
                        for chart in charts
                    ):
                        language_chart = next(
                            chart
                            for chart in charts
                            if chart.get("title") == "Call Distribution by Language"
                        )
                        render_chart(language_chart)

                    # Display queue time chart
                    if charts and any(
                        chart.get("title") == "Average Queue Time by Hour"
                        for chart in charts
                    ):
                        queue_chart = next(
                            chart
                            for chart in charts
                            if chart.get("title") == "Average Queue Time by Hour"
                        )
                        render_chart(queue_chart)
            else:
                st.error("Failed to perform comprehensive call analytics")
    else:
        # Display instructions when the page loads
        st.info(
            "👈 Use the filters in the sidebar and click 'Run Analysis' to generate comprehensive call analytics with actionable recommendations."
        )

        # Display sample recommendations
        st.subheader("Sample Recommendations")
        st.markdown(
            """
        After running the analysis, you'll receive actionable recommendations like:

        1. **Optimize Scheduling**: Increase staffing between 10 AM and 3 PM to handle peak call volumes.
        2. **Balance Language Skills**: Maintain a balanced roster of Arabic and English-speaking agents.
        3. **Address Agent Not Available**: Add more agents or redistribute shifts to cover peak times.
        4. **Prioritize Transaction Campaigns**: Allocate more resources to Transaction calls.
        5. **Agent Training**: Identify agents who may benefit from training to handle complex calls more efficiently.
        """
        )


if __name__ == "__main__":
    main()
