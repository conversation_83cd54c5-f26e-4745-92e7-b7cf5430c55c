"""
Time Series Forecasting Page for Call Flow Analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import requests
import json
import os
import sys
import logging
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get API URL from config
try:
    from app_config import API_URL
except ImportError:
    API_URL = "http://localhost:8002"  # Default fallback


def main():
    """Main function for the time series forecast page"""
    st.title("Call Volume Forecasting (Debug Version)")

    # Debug information
    st.write(f"API URL: {API_URL}")
    st.write(f"Endpoint: {API_URL}/analytics/time-series-forecast")

    # Test API connection
    try:
        health_check = requests.get(f"{API_URL}/health", timeout=5)
        st.write(f"API health check: {health_check.status_code} - {health_check.text}")

        # Test analytics endpoint
        try:
            analytics_check = requests.get(f"{API_URL}/analytics", timeout=5)
            st.write(f"Analytics endpoint check: {analytics_check.status_code}")
            if analytics_check.status_code != 200:
                st.error(
                    "Analytics endpoint not available. This may cause forecast requests to fail."
                )
        except Exception as e:
            st.error(f"Analytics endpoint check failed: {str(e)}")

        # Test time-series-forecast endpoint with a simple GET request
        try:
            forecast_check = requests.get(
                f"{API_URL}/analytics/time-series-forecast", timeout=5
            )
            st.write(f"Forecast endpoint check: {forecast_check.status_code}")
            if (
                forecast_check.status_code != 405
            ):  # 405 = Method Not Allowed (expected for GET on a POST endpoint)
                st.error(
                    "Forecast endpoint not responding as expected. This may cause forecast requests to fail."
                )
        except Exception as e:
            st.error(f"Forecast endpoint check failed: {str(e)}")
    except Exception as e:
        st.error(f"API health check failed: {str(e)}")

    try:
        st.write("Predict future call volumes based on historical data")

        # Sidebar for forecast parameters
        with st.sidebar:
            st.header("Forecast Parameters")

            # Date range filter
            st.subheader("Historical Data Range")

            # Get current date and 30 days ago for default date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)

            # Date inputs
            start_date_input = st.date_input(
                "Start Date", value=start_date, key="start_date"
            )
            end_date_input = st.date_input("End Date", value=end_date, key="end_date")

            # Days to forecast
            st.subheader("Forecast Settings")
            forecast_days = st.slider(
                "Days to Forecast",
                min_value=1,
                max_value=30,
                value=7,
                help="Number of days to forecast into the future",
            )

            # Granularity
            granularity = st.selectbox(
                "Time Granularity",
                options=["hourly", "daily", "weekly"],
                index=1,  # Default to daily
                help="Time granularity for the forecast",
            )

            # Forecast method
            forecast_method = st.selectbox(
                "Forecast Method",
                options=[
                    "auto",  # Automatically select the best method
                    "ets",  # Exponential Smoothing
                    "arima",  # ARIMA
                    "linear",  # Linear Regression
                    "prophet",  # Facebook Prophet
                    "xgboost",  # XGBoost
                    "random_forest",  # Random Forest
                    "lstm",  # Long Short-Term Memory (Deep Learning)
                ],
                index=0,  # Default to auto
                help="Method used for forecasting",
            )

            # Add information about the selected method
            if forecast_method == "auto":
                st.info(
                    "Auto: Automatically selects the best forecasting method based on the data."
                )
            elif forecast_method == "ets":
                st.info(
                    "ETS: Exponential Smoothing - Good for data with trend and seasonality."
                )
            elif forecast_method == "arima":
                st.info(
                    "ARIMA: Auto-Regressive Integrated Moving Average - Good for stationary time series."
                )
            elif forecast_method == "linear":
                st.info("Linear Regression: Simple trend-based forecasting.")
            elif forecast_method == "prophet":
                st.info(
                    "Prophet: Facebook's forecasting tool - Handles seasonality and holidays well."
                )
            elif forecast_method == "xgboost":
                st.info("XGBoost: Gradient boosting - Good for complex patterns.")
            elif forecast_method == "random_forest":
                st.info("Random Forest: Ensemble learning - Robust to outliers.")
            elif forecast_method == "lstm":
                st.info(
                    "LSTM: Deep learning for time series - Good for complex, long-term patterns."
                )

            # Include language breakdown
            by_language = st.checkbox(
                "Include Language Breakdown",
                value=True,
                help="Break down forecasts by language",
            )

            # Include campaign breakdown
            by_campaign = st.checkbox(
                "Include Campaign Breakdown",
                value=True,
                help="Break down forecasts by campaign",
            )

        # Main content
        col1, col2 = st.columns([3, 1])

        with col1:
            # Test direct API call
            if st.button("Test Direct API Call"):
                st.write("Making a direct API call to test the endpoint...")
                try:
                    # Minimal request parameters - include date range if provided
                    test_params = {
                        "forecast_days": 7,
                        "granularity": "daily",
                        "forecast_method": forecast_method,  # Use the selected forecast method
                        "by_language": True,
                        "by_campaign": True,
                    }

                    # Add date range if provided
                    if start_date_input:
                        test_params["start_date"] = start_date_input.strftime(
                            "%Y-%m-%d"
                        )
                    if end_date_input:
                        test_params["end_date"] = end_date_input.strftime("%Y-%m-%d")

                    # Make the request directly
                    endpoint = f"{API_URL}/analytics/time-series-forecast"
                    st.write(f"Calling endpoint: {endpoint}")
                    st.write(f"With parameters: {test_params}")

                    response = requests.post(endpoint, json=test_params, timeout=30)
                    st.write(f"Response status code: {response.status_code}")

                    if response.status_code == 200:
                        st.success("Direct API call successful!")
                        try:
                            result = response.json()
                            st.write("Response contains data:", "data" in result)
                            st.write("Response contains charts:", "charts" in result)
                        except Exception as e:
                            st.error(f"Error parsing response: {str(e)}")
                    else:
                        st.error(
                            f"Direct API call failed with status code: {response.status_code}"
                        )
                        try:
                            error_detail = response.json()
                            st.error(f"Error details: {error_detail}")
                        except:
                            st.error(f"Response text: {response.text}")
                except Exception as e:
                    st.error(f"Error making direct API call: {str(e)}")

            # Run forecast button
            if st.button("Run Forecast", type="primary", key="run_forecast"):
                with st.spinner("Generating forecast..."):
                    try:
                        # Call the API to get forecast data
                        result_data = get_forecast_data(
                            forecast_days=forecast_days,
                            granularity=granularity,
                            forecast_method=forecast_method,
                            by_language=by_language,
                            by_campaign=by_campaign,
                            start_date=start_date_input,
                            end_date=end_date_input,
                        )

                        if result_data and "error" not in result_data:
                            # Display the forecast
                            display_forecast(result_data)
                        else:
                            error_msg = result_data.get(
                                "error", "Unknown error occurred"
                            )
                            st.error(f"Error generating forecast: {error_msg}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
                        logger.error(f"Error in forecast: {str(e)}", exc_info=True)
            else:
                st.info(
                    "Click 'Run Forecast' to generate a forecast based on your parameters"
                )

        with col2:
            st.subheader("About Forecasting")
            st.write(
                """
            This tool uses time series forecasting to predict future call volumes based on historical patterns.

            **Methods:**
            - **Auto**: Automatically selects the best method
            - **ETS**: Exponential smoothing
            - **ARIMA**: Auto-regressive integrated moving average
            - **Linear**: Linear regression
            - **Prophet**: Facebook's forecasting tool
            - **XGBoost**: Gradient boosting
            - **Random Forest**: Ensemble learning
            - **LSTM**: Deep learning for time series

            **Note:** Some advanced methods may require additional dependencies to be installed on the server.
            """
            )
    except Exception as e:
        st.error(f"Error loading page: {str(e)}")
        logger.error(f"Error loading page: {str(e)}", exc_info=True)


def get_forecast_data(
    forecast_days,
    granularity,
    forecast_method,
    by_language,
    by_campaign,
    start_date=None,
    end_date=None,
):
    """
    Get forecast data from the API

    Args:
        forecast_days: Number of days to forecast
        granularity: Time granularity (hourly, daily, weekly)
        forecast_method: Forecasting method (auto, ets, arima, linear, prophet, xgboost, random_forest, lstm)
        by_language: Whether to break down by language
        by_campaign: Whether to break down by campaign
        start_date: Optional start date for historical data
        end_date: Optional end date for historical data

    Returns:
        Dictionary with forecast data
    """
    try:
        # API endpoint - use the correct endpoint from the analytics router
        endpoint = f"{API_URL}/analytics/time-series-forecast"

        # Request parameters - include date range if provided
        params = {
            "forecast_days": forecast_days,
            "granularity": granularity,
            "forecast_method": forecast_method,
            "by_language": by_language,
            "by_campaign": by_campaign,
        }

        # Add date range if provided
        if start_date:
            params["start_date"] = start_date.strftime("%Y-%m-%d")
        if end_date:
            params["end_date"] = end_date.strftime("%Y-%m-%d")

        # Make the request
        st.write(f"Sending request to {endpoint}")
        st.write(f"Request parameters: {params}")

        # Make the API request
        try:
            response = requests.post(endpoint, json=params, timeout=30)
        except Exception as e:
            st.error(f"Error making API request: {str(e)}")
            return {"error": f"Error making API request: {str(e)}"}

        # Check if request was successful
        if response.status_code == 200:
            try:
                result = response.json()
                st.success("API request successful")
                return result.get("data", {})
            except Exception as e:
                st.error(f"Error parsing API response: {str(e)}")
                return {"error": f"Error parsing API response: {str(e)}"}
        else:
            st.error(f"API Error: {response.status_code}")
            try:
                error_detail = response.json()
                st.error(f"Error details: {error_detail}")
            except:
                st.error(f"Response text: {response.text}")

            # Try to check if the API is running
            try:
                health_check = requests.get(f"{API_URL}/health", timeout=5)
                st.write(
                    f"API health check: {health_check.status_code} - {health_check.text}"
                )
            except Exception as e:
                st.error(f"API health check failed: {str(e)}")

            return {"error": f"API Error: {response.status_code}"}
    except Exception as e:
        logger.error(f"Error getting forecast data: {str(e)}", exc_info=True)
        return {"error": f"Error getting forecast data: {str(e)}"}


def display_forecast(result_data):
    """
    Display the forecast results

    Args:
        result_data: Dictionary with forecast data
    """
    try:
        # Display overall forecast
        if "overall_forecast" in result_data:
            st.subheader("Overall Call Volume Forecast")

            try:
                # Create dataframes for historical and forecast data
                hist_values = safely_convert_to_float(
                    result_data["overall_forecast"].get("historical_values", [])
                )
                forecast_values = safely_convert_to_float(
                    result_data["overall_forecast"].get("forecast_values", [])
                )

                hist_df = pd.DataFrame(
                    {
                        "Date": result_data["overall_forecast"].get(
                            "historical_dates", []
                        ),
                        "Call Count": hist_values,
                    }
                )

                forecast_df = pd.DataFrame(
                    {
                        "Date": result_data["overall_forecast"].get(
                            "forecast_dates", []
                        ),
                        "Predicted Call Count": forecast_values,
                    }
                )

                # Display the dataframes
                col1, col2 = st.columns(2)
                with col1:
                    st.write("Historical Data")
                    st.dataframe(hist_df, height=200)

                with col2:
                    st.write("Forecast Data")
                    st.dataframe(forecast_df, height=200)

                # Create and display the chart
                create_forecast_chart(
                    historical_dates=result_data["overall_forecast"].get(
                        "historical_dates", []
                    ),
                    historical_values=hist_values,
                    forecast_dates=result_data["overall_forecast"].get(
                        "forecast_dates", []
                    ),
                    forecast_values=forecast_values,
                    title="Call Volume Forecast",
                )

                # Display method used
                method_used = result_data["overall_forecast"].get(
                    "method_used", "unknown"
                )
                st.caption(f"Forecast method: {method_used}")
            except Exception as e:
                st.error(f"Error displaying overall forecast: {str(e)}")
                logger.error(
                    f"Error displaying overall forecast: {str(e)}", exc_info=True
                )

        # Display recommendations
        if "recommendations" in result_data and result_data["recommendations"]:
            st.subheader("Recommendations")
            for rec in result_data["recommendations"]:
                st.info(rec)

        # Display language forecasts
        if "language_forecasts" in result_data and result_data["language_forecasts"]:
            st.subheader("Language-based Forecasts")

            try:
                for language, forecast in result_data["language_forecasts"].items():
                    if "error" in forecast:
                        st.warning(f"Error in {language} forecast: {forecast['error']}")
                        continue

                    with st.expander(f"{language} Forecast"):
                        forecast_values = safely_convert_to_float(
                            forecast.get("forecast_values", [])
                        )

                        create_forecast_chart(
                            historical_dates=forecast.get("historical_dates", []),
                            historical_values=safely_convert_to_float(
                                forecast.get("historical_values", [])
                            ),
                            forecast_dates=forecast.get("forecast_dates", []),
                            forecast_values=forecast_values,
                            title=f"{language} Call Volume Forecast",
                        )
            except Exception as e:
                st.error(f"Error displaying language forecasts: {str(e)}")
                logger.error(
                    f"Error displaying language forecasts: {str(e)}", exc_info=True
                )

        # Display campaign forecasts
        if "campaign_forecasts" in result_data and result_data["campaign_forecasts"]:
            st.subheader("Campaign-based Forecasts")

            try:
                for campaign, forecast in result_data["campaign_forecasts"].items():
                    if "error" in forecast:
                        st.warning(f"Error in {campaign} forecast: {forecast['error']}")
                        continue

                    with st.expander(f"{campaign} Forecast"):
                        forecast_values = safely_convert_to_float(
                            forecast.get("forecast_values", [])
                        )

                        create_forecast_chart(
                            historical_dates=forecast.get("historical_dates", []),
                            historical_values=safely_convert_to_float(
                                forecast.get("historical_values", [])
                            ),
                            forecast_dates=forecast.get("forecast_dates", []),
                            forecast_values=forecast_values,
                            title=f"{campaign} Call Volume Forecast",
                        )
            except Exception as e:
                st.error(f"Error displaying campaign forecasts: {str(e)}")
                logger.error(
                    f"Error displaying campaign forecasts: {str(e)}", exc_info=True
                )
    except Exception as e:
        st.error(f"Error displaying forecast: {str(e)}")
        logger.error(f"Error displaying forecast: {str(e)}", exc_info=True)


def safely_convert_to_float(values):
    """
    Safely convert values to float

    Args:
        values: List of values to convert

    Returns:
        List of float values
    """
    result = []
    for val in values:
        try:
            result.append(float(val))
        except (ValueError, TypeError):
            logger.warning(f"Error converting value to float: {val}, type: {type(val)}")
            result.append(0.0)
    return result


# claude version 2
def create_forecast_chart(
    historical_dates, historical_values, forecast_dates, forecast_values, title
):
    """
    Create and display a forecast chart with proper type handling

    Args:
        historical_dates: List of historical dates
        historical_values: List of historical values
        forecast_dates: List of forecast dates
        forecast_values: List of forecast values
        title: Chart title
    """
    import pandas as pd
    import plotly.graph_objects as go
    from datetime import datetime
    import streamlit as st

    try:
        # Create separate dataframes for historical and forecast data to avoid type mixing
        hist_df = pd.DataFrame(
            {
                "Date": [str(d) for d in historical_dates],
                "Value": [
                    float(v) if v is not None else 0.0 for v in historical_values
                ],
                "Type": ["Historical"] * len(historical_dates),
            }
        )

        forecast_df = pd.DataFrame(
            {
                "Date": [str(d) for d in forecast_dates],
                "Value": [float(v) if v is not None else 0.0 for v in forecast_values],
                "Type": ["Forecast"] * len(forecast_dates),
            }
        )

        # Create figure using graph_objects for more control
        fig = go.Figure()

        # Add historical data trace
        if not hist_df.empty:
            fig.add_trace(
                go.Scatter(
                    x=hist_df["Date"],
                    y=hist_df["Value"],
                    mode="lines",
                    name="Historical",
                    line=dict(color="blue"),
                )
            )

        # Add forecast data trace
        if not forecast_df.empty:
            fig.add_trace(
                go.Scatter(
                    x=forecast_df["Date"],
                    y=forecast_df["Value"],
                    mode="lines",
                    name="Forecast",
                    line=dict(color="red"),
                )
            )

        # Update layout
        fig.update_layout(
            title=title,
            xaxis_title="Date",
            yaxis_title="Number of Calls",
            legend_title="Data Type",
            hovermode="x unified",
        )

        # Add vertical line to separate historical from forecast
        if not hist_df.empty and not forecast_df.empty:
            # Get the last historical date
            last_hist_date = hist_df["Date"].iloc[-1]

            # Add a shape for the vertical line
            fig.add_shape(
                type="line",
                x0=last_hist_date,
                y0=0,
                x1=last_hist_date,
                y1=1,
                yref="paper",
                line=dict(color="gray", width=2, dash="dash"),
            )

            # Add annotation for the vertical line
            fig.add_annotation(
                x=last_hist_date,
                y=1.05,
                yref="paper",
                text="Forecast Start",
                showarrow=False,
                font=dict(color="gray"),
            )

        # Display the chart
        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"Error creating chart: {str(e)}")
        import traceback

        st.error(f"Traceback: {traceback.format_exc()}")


# CLAUDE VERSION 1
def create_forecast_chart_v1(
    historical_dates, historical_values, forecast_dates, forecast_values, title
):
    """
    Create and display a forecast chart

    Args:
        historical_dates: List of historical dates
        historical_values: List of historical values
        forecast_dates: List of forecast dates
        forecast_values: List of forecast values
        title: Chart title
    """
    try:
        # Convert all dates to strings to avoid type issues
        historical_dates_str = []
        for d in historical_dates:
            try:
                historical_dates_str.append(str(d))
            except Exception as e:
                st.warning(f"Error converting historical date {d} to string: {str(e)}")
                historical_dates_str.append("")

        forecast_dates_str = []
        for d in forecast_dates:
            try:
                forecast_dates_str.append(str(d))
            except Exception as e:
                st.warning(f"Error converting forecast date {d} to string: {str(e)}")
                forecast_dates_str.append("")

        # Ensure all values are float
        historical_values_float = []
        for v in historical_values:
            try:
                historical_values_float.append(float(v))
            except (ValueError, TypeError) as e:
                st.warning(f"Error converting historical value {v} to float: {str(e)}")
                historical_values_float.append(0.0)

        forecast_values_float = []
        for v in forecast_values:
            try:
                forecast_values_float.append(float(v))
            except (ValueError, TypeError) as e:
                st.warning(f"Error converting forecast value {v} to float: {str(e)}")
                forecast_values_float.append(0.0)

        # Create dataframe for plotting
        try:
            # Filter out any empty date strings
            valid_hist_indices = [i for i, d in enumerate(historical_dates_str) if d]
            valid_forecast_indices = [i for i, d in enumerate(forecast_dates_str) if d]

            # Create filtered lists
            filtered_hist_dates = [historical_dates_str[i] for i in valid_hist_indices]
            filtered_hist_values = [
                historical_values_float[i] for i in valid_hist_indices
            ]
            filtered_forecast_dates = [
                forecast_dates_str[i] for i in valid_forecast_indices
            ]
            filtered_forecast_values = [
                forecast_values_float[i] for i in valid_forecast_indices
            ]

            # Create the dataframe with filtered data
            df = pd.DataFrame(
                {
                    "Date": filtered_hist_dates + filtered_forecast_dates,
                    "Value": filtered_hist_values + filtered_forecast_values,
                    "Type": ["Historical"] * len(filtered_hist_dates)
                    + ["Forecast"] * len(filtered_forecast_dates),
                }
            )
        except Exception as e:
            st.error(f"Error creating dataframe: {str(e)}")
            # Create an empty dataframe as fallback
            df = pd.DataFrame({"Date": [], "Value": [], "Type": []})

        # Create the chart
        fig = px.line(
            df,
            x="Date",
            y="Value",
            color="Type",
            title=title,
            labels={"Value": "Number of Calls", "Date": "Date", "Type": "Data Type"},
            color_discrete_map={"Historical": "blue", "Forecast": "red"},
        )

        # Add a vertical line to separate historical from forecast
        if len(filtered_hist_dates) > 0 and len(filtered_forecast_dates) > 0:
            try:
                # Get the last historical date
                last_date = filtered_hist_dates[-1]

                # Add a vertical line as a shape - AVOID using add_vline()
                fig.add_shape(
                    type="line",
                    x0=last_date,
                    y0=0,
                    x1=last_date,
                    y1=1,
                    yref="paper",
                    line=dict(color="gray", width=2, dash="dash"),
                )

                # Add a separate annotation
                fig.add_annotation(
                    x=last_date,
                    y=1.05,
                    yref="paper",
                    text="Forecast Start",
                    showarrow=False,
                    font=dict(color="gray"),
                )
            except Exception as e:
                st.warning(f"Could not add vertical line: {str(e)}")

        # Display the chart
        st.plotly_chart(fig, use_container_width=True)
    except Exception as e:
        st.error(f"Error creating chart: {str(e)}")
        logger.error(f"Error creating chart: {str(e)}", exc_info=True)


# AUGMENT VERSION
# def create_forecast_chart(
#     historical_dates, historical_values, forecast_dates, forecast_values, title
# ):
#     """
#     Create and display a forecast chart

#     Args:
#         historical_dates: List of historical dates
#         historical_values: List of historical values
#         forecast_dates: List of forecast dates
#         forecast_values: List of forecast values
#         title: Chart title
#     """
#     try:
#         # Debug information
#         st.write("Creating chart with:")
#         st.write(f"Historical dates (first 3): {historical_dates[:3]}")
#         st.write(f"Historical dates types: {[type(d) for d in historical_dates[:3]]}")
#         st.write(f"Historical values (first 3): {historical_values[:3]}")
#         st.write(f"Historical values types: {[type(v) for v in historical_values[:3]]}")
#         st.write(f"Forecast dates (first 3): {forecast_dates[:3]}")
#         st.write(f"Forecast dates types: {[type(d) for d in forecast_dates[:3]]}")
#         st.write(f"Forecast values (first 3): {forecast_values[:3]}")
#         st.write(f"Forecast values types: {[type(v) for v in forecast_values[:3]]}")

#         # Ensure all values are the correct type
#         # Convert all dates to strings to avoid type issues
#         historical_dates_str = []
#         for d in historical_dates:
#             try:
#                 historical_dates_str.append(str(d))
#             except Exception as e:
#                 st.warning(f"Error converting historical date {d} to string: {str(e)}")
#                 historical_dates_str.append("")

#         forecast_dates_str = []
#         for d in forecast_dates:
#             try:
#                 forecast_dates_str.append(str(d))
#             except Exception as e:
#                 st.warning(f"Error converting forecast date {d} to string: {str(e)}")
#                 forecast_dates_str.append("")

#         # Ensure all values are float
#         historical_values_float = []
#         for v in historical_values:
#             try:
#                 historical_values_float.append(float(v))
#             except (ValueError, TypeError) as e:
#                 st.warning(f"Error converting historical value {v} to float: {str(e)}")
#                 historical_values_float.append(0.0)

#         forecast_values_float = []
#         for v in forecast_values:
#             try:
#                 forecast_values_float.append(float(v))
#             except (ValueError, TypeError) as e:
#                 st.warning(f"Error converting forecast value {v} to float: {str(e)}")
#                 forecast_values_float.append(0.0)

#         # Create dataframe for plotting
#         try:
#             # Filter out any empty date strings
#             valid_hist_indices = [i for i, d in enumerate(historical_dates_str) if d]
#             valid_forecast_indices = [i for i, d in enumerate(forecast_dates_str) if d]

#             # Create filtered lists
#             filtered_hist_dates = [historical_dates_str[i] for i in valid_hist_indices]
#             filtered_hist_values = [
#                 historical_values_float[i] for i in valid_hist_indices
#             ]
#             filtered_forecast_dates = [
#                 forecast_dates_str[i] for i in valid_forecast_indices
#             ]
#             filtered_forecast_values = [
#                 forecast_values_float[i] for i in valid_forecast_indices
#             ]

#             # Debug the filtered data
#             st.write(f"Filtered historical dates count: {len(filtered_hist_dates)}")
#             st.write(f"Filtered forecast dates count: {len(filtered_forecast_dates)}")

#             # Create the dataframe with filtered data
#             df = pd.DataFrame(
#                 {
#                     "Date": filtered_hist_dates + filtered_forecast_dates,
#                     "Value": filtered_hist_values + filtered_forecast_values,
#                     "Type": ["Historical"] * len(filtered_hist_dates)
#                     + ["Forecast"] * len(filtered_forecast_dates),
#                 }
#             )
#         except Exception as e:
#             st.error(f"Error creating dataframe: {str(e)}")
#             # Create an empty dataframe as fallback
#             df = pd.DataFrame({"Date": [], "Value": [], "Type": []})

#         # Create the chart
#         fig = px.line(
#             df,
#             x="Date",
#             y="Value",
#             color="Type",
#             title=title,
#             labels={"Value": "Number of Calls", "Date": "Date", "Type": "Data Type"},
#             color_discrete_map={"Historical": "blue", "Forecast": "red"},
#         )

#         # Add a vertical line to separate historical from forecast
#         if len(historical_dates_str) > 0:
#             # Instead of using add_vline which is causing type errors,
#             # add a shape directly which gives us more control
#             try:
#                 # Get the last historical date - ensure it's a string
#                 last_date = str(historical_dates_str[-1])

#                 # Debug the type
#                 st.write(
#                     f"Last historical date type: {type(last_date)}, value: {last_date}"
#                 )

#                 # Add a vertical line as a shape
#                 fig.add_shape(
#                     type="line",
#                     x0=last_date,
#                     y0=0,
#                     x1=last_date,
#                     y1=1,
#                     yref="paper",
#                     line=dict(color="gray", width=2, dash="dash"),
#                 )

#                 # Add a separate annotation
#                 fig.add_annotation(
#                     x=last_date,
#                     y=1.05,
#                     yref="paper",
#                     text="Forecast Start",
#                     showarrow=False,
#                     font=dict(color="gray"),
#                 )
#             except Exception as e:
#                 st.warning(f"Could not add vertical line: {str(e)}")
#                 # Continue without the line

#         # Display the chart
#         st.plotly_chart(fig, use_container_width=True)
#     except Exception as e:
#         st.error(f"Error creating chart: {str(e)}")
#         logger.error(f"Error creating chart: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
