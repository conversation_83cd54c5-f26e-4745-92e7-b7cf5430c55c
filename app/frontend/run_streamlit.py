#!/usr/bin/env python
"""
Wrapper script for running the Streamlit app with the correct Python path.
"""
import os
import sys
import streamlit.web.cli as stcli

# Add the project root to Python path
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, current_dir)

# Run the Streamlit app
if __name__ == "__main__":
    sys.argv = ["streamlit", "run", os.path.join(os.path.dirname(__file__), "app.py")]
    sys.exit(stcli.main())
