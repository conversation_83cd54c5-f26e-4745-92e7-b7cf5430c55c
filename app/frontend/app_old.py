# Import the page config first to ensure it's set before any other Streamlit code
import streamlit as st


# Set page config directly here instead of importing from another module
def setup_page_config():
    """Set up the page configuration for Streamlit"""
    # Try to set the page config, but catch the exception if it's already set
    try:
        st.set_page_config(
            page_title="Call Flow Analytics",
            page_icon="📞",
            layout="wide",
            initial_sidebar_state="expanded",
        )
    except Exception as e:
        # If the config is already set, this will raise an exception
        # We can safely ignore it
        pass


# Call the function to set up the page config
setup_page_config()

# Now import other modules
# streamlit is already imported above
import pandas as pd
import numpy as np
import requests
import json
import os
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import io

# Try different import approaches
try:
    # First try the standard import
    from app.shared.config import API_URL
except ImportError:
    try:
        # Try relative import
        import sys
        import os

        # Add project root to path
        sys.path.insert(
            0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
        )
        from app.shared.config import API_URL
    except ImportError:
        # Fallback to direct import
        import sys
        import os

        # Get the absolute path to the shared config
        shared_dir = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "../shared")
        )
        sys.path.insert(0, shared_dir)
        from config import API_URL

# Define API endpoints
API_ENDPOINTS = {
    "upload_files": f"{API_URL}/data/upload",
    "list_files": f"{API_URL}/data/files",
    "data_summary": f"{API_URL}/data/summary",
    "agent_performance": f"{API_URL}/analytics/agent-performance",
    "call_volume_prediction": f"{API_URL}/analytics/call-volume-prediction",
    "staffing_optimization": f"{API_URL}/analytics/staffing-optimization",
    "campaign_analysis": f"{API_URL}/analytics/campaign-analysis",
    "language_analysis": f"{API_URL}/analytics/language-analysis",
    # Add other endpoints as needed
}


# Helper functions
def make_api_request(endpoint, method="GET", data=None, files=None, params=None):
    """
    Make a request to the API

    Args:
        endpoint: API endpoint
        method: HTTP method
        data: Request data
        files: Files to upload
        params: Query parameters for GET requests

    Returns:
        API response
    """
    try:
        if method.upper() == "GET":
            response = requests.get(endpoint, params=params)
        elif method.upper() == "POST":
            if files:
                response = requests.post(endpoint, files=files)
            else:
                response = requests.post(endpoint, json=data)
        else:
            st.error(f"Unsupported method: {method}")
            return None

        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"API Error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        st.error(f"Error making API request: {str(e)}")
        return None


def render_chart(chart_data):
    """
    Render a chart based on chart data

    Args:
        chart_data: Chart data from API
    """
    chart_type = chart_data.get("chart_type", "")
    title = chart_data.get("title", "")
    x_label = chart_data.get("x_label", "")
    y_label = chart_data.get("y_label", "")
    data = chart_data.get("data", {})

    if not data:
        st.warning("No data available for chart")
        return

    if chart_type == "bar":
        fig = px.bar(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "line":
        if "series" in data:
            # Multiple series line chart
            df = pd.DataFrame(
                {
                    "x": data.get("x", []),
                    "y": data.get("y", []),
                    "series": data.get("series", []),
                }
            )
            fig = px.line(
                df,
                x="x",
                y="y",
                color="series",
                title=title,
                labels={"x": x_label, "y": y_label, "series": "Series"},
            )
        else:
            # Single series line chart
            fig = px.line(
                x=data.get("x", []),
                y=data.get("y", []),
                title=title,
                labels={"x": x_label, "y": y_label},
            )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "pie":
        fig = px.pie(
            names=data.get("labels", []), values=data.get("values", []), title=title
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "scatter":
        fig = px.scatter(
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    elif chart_type == "heatmap":
        fig = px.imshow(
            data.get("z", []),
            x=data.get("x", []),
            y=data.get("y", []),
            title=title,
            labels={"x": x_label, "y": y_label},
        )
        st.plotly_chart(fig, use_container_width=True)

    else:
        st.warning(f"Unsupported chart type: {chart_type}")


# Sidebar navigation
st.sidebar.title("Call Flow Analytics")
st.sidebar.image("https://img.icons8.com/color/96/000000/phone-office.png", width=100)

# Add links to advanced features
st.sidebar.markdown("---")
st.sidebar.subheader("Advanced Features")
# Navigation buttons are defined below with unique keys

# Handle navigation to advanced pages
# Initialize rerun counter if not present
if "navigation_rerun_counter" not in st.session_state:
    st.session_state.navigation_rerun_counter = 0

# Safety limit for navigation reruns
max_navigation_reruns = 3

# Add unique keys to each button to avoid duplicate widget IDs
advanced_ml_url = st.sidebar.button("🧠 Advanced ML Models", key="nav_advanced_ml")
model_training_url = st.sidebar.button("🔧 Model Training", key="nav_model_training")
task_monitor_url = st.sidebar.button("📊 Task Monitor", key="nav_task_monitor")
custom_reports_url = st.sidebar.button("📝 Custom Reports", key="nav_custom_reports")

if advanced_ml_url:
    # Set the page in session state instead of importing directly
    st.session_state.page = "Advanced ML Models"
    if st.session_state.navigation_rerun_counter < max_navigation_reruns:
        st.session_state.navigation_rerun_counter += 1
        st.rerun()
    else:
        st.warning("Please use the dropdown menu to navigate to Advanced ML Models")

if model_training_url:
    # Set the page in session state instead of importing directly
    st.session_state.page = "Model Training"
    if st.session_state.navigation_rerun_counter < max_navigation_reruns:
        st.session_state.navigation_rerun_counter += 1
        st.rerun()
    else:
        st.warning("Please use the dropdown menu to navigate to Model Training")

if task_monitor_url:
    # Set the page in session state instead of importing directly
    st.session_state.page = "Task Monitor"
    if st.session_state.navigation_rerun_counter < max_navigation_reruns:
        st.session_state.navigation_rerun_counter += 1
        st.rerun()
    else:
        st.warning("Please use the dropdown menu to navigate to Task Monitor")

if custom_reports_url:
    # Set the page in session state instead of importing directly
    st.session_state.page = "Custom Reports"
    if st.session_state.navigation_rerun_counter < max_navigation_reruns:
        st.session_state.navigation_rerun_counter += 1
        st.rerun()
    else:
        st.warning("Please use the dropdown menu to navigate to Custom Reports")

# Main navigation
# Use session state to remember the current page
if "page" not in st.session_state:
    st.session_state.page = "Dashboard"

# Create the page selection dropdown
page = st.sidebar.selectbox(
    "Select Analysis",
    [
        "Dashboard",
        "Data Upload",
        "Agent Performance",
        "Call Volume Prediction",
        "Staffing Optimization",
        "Campaign Analysis",
        "Language Analysis",
        "Missed Call Analysis",
        "Customer Journey",
        "First Call Resolution",
        "Sentiment Analysis",
        "Agent Specialization",
        "Conversion Rate Optimization",
        "Queue Optimization",
        "Seasonal Trend Analysis",
        "Geographic Insights",
        "Call Quality Scoring",
        "A/B Testing Framework",
        "Churn Prediction",
        "Cross-selling Opportunities",
        "Anomaly Detection",
        "Callback Optimization",
        "Knowledge Base Enhancement",
        "Advanced ML Models",
        "Model Training",
        "Task Monitor",
        "Custom Reports",
    ],
    index=(
        [
            "Dashboard",
            "Data Upload",
            "Agent Performance",
            "Call Volume Prediction",
            "Staffing Optimization",
            "Campaign Analysis",
            "Language Analysis",
            "Missed Call Analysis",
            "Customer Journey",
            "First Call Resolution",
            "Sentiment Analysis",
            "Agent Specialization",
            "Conversion Rate Optimization",
            "Queue Optimization",
            "Seasonal Trend Analysis",
            "Geographic Insights",
            "Call Quality Scoring",
            "A/B Testing Framework",
            "Churn Prediction",
            "Cross-selling Opportunities",
            "Anomaly Detection",
            "Callback Optimization",
            "Knowledge Base Enhancement",
            "Advanced ML Models",
            "Model Training",
            "Task Monitor",
            "Custom Reports",
        ].index(st.session_state.page)
        if st.session_state.page
        in [
            "Dashboard",
            "Data Upload",
            "Agent Performance",
            "Call Volume Prediction",
            "Staffing Optimization",
            "Campaign Analysis",
            "Language Analysis",
            "Missed Call Analysis",
            "Customer Journey",
            "First Call Resolution",
            "Sentiment Analysis",
            "Agent Specialization",
            "Conversion Rate Optimization",
            "Queue Optimization",
            "Seasonal Trend Analysis",
            "Geographic Insights",
            "Call Quality Scoring",
            "A/B Testing Framework",
            "Churn Prediction",
            "Cross-selling Opportunities",
            "Anomaly Detection",
            "Callback Optimization",
            "Knowledge Base Enhancement",
            "Advanced ML Models",
            "Model Training",
            "Task Monitor",
            "Custom Reports",
        ]
        else 0
    ),
)

# Update session state with the selected page
st.session_state.page = page

# Dashboard page
if page == "Dashboard":
    st.title("Call Flow Analytics Dashboard")
    st.write(
        "Welcome to the Call Flow Analytics Dashboard. Use the sidebar to navigate to different analyses."
    )

    # Add filters for the dashboard
    with st.expander("Filter Dashboard Data", expanded=False):
        filter_col1, filter_col2 = st.columns(2)

        # Date range
        with filter_col1:
            start_date = st.date_input(
                "Start Date", datetime.now() - timedelta(days=30)
            )
            end_date = st.date_input("End Date", datetime.now())

        # Campaigns and languages
        with filter_col2:
            campaigns = st.multiselect(
                "Filter by Campaigns",
                [
                    "Transaction_English",
                    "GeneralArabic",
                    "TechSupp",
                    "ONLINE",
                    "AGENTDIRECT",
                ],
            )
            languages = st.multiselect("Filter by Languages", ["English", "Arabic"])

        # Apply filters button
        apply_filters = st.button("Apply Filters")

        # Store filter state
        if apply_filters:
            st.session_state.dashboard_filters = {
                "start_date": start_date.strftime("%Y-%m-%d") if start_date else None,
                "end_date": end_date.strftime("%Y-%m-%d") if end_date else None,
                "campaigns": ",".join(campaigns) if campaigns else None,
                "languages": ",".join(languages) if languages else None,
            }

        # Clear filters button
        if st.button("Clear Filters"):
            st.session_state.dashboard_filters = {}
            st.rerun()

    # Initialize filters in session state if not present
    if "dashboard_filters" not in st.session_state:
        st.session_state.dashboard_filters = {}

    # Get data summary with filters
    summary_response = make_api_request(
        API_ENDPOINTS["data_summary"], params=st.session_state.dashboard_filters
    )

    # Show active filters if any
    if any(st.session_state.dashboard_filters.values()):
        filter_info = []
        if st.session_state.dashboard_filters.get(
            "start_date"
        ) or st.session_state.dashboard_filters.get("end_date"):
            date_range = f"{st.session_state.dashboard_filters.get('start_date', 'all')} to {st.session_state.dashboard_filters.get('end_date', 'all')}"
            filter_info.append(f"Date Range: {date_range}")
        if st.session_state.dashboard_filters.get("campaigns"):
            filter_info.append(
                f"Campaigns: {st.session_state.dashboard_filters.get('campaigns')}"
            )
        if st.session_state.dashboard_filters.get("languages"):
            filter_info.append(
                f"Languages: {st.session_state.dashboard_filters.get('languages')}"
            )

        st.info("Active Filters: " + " | ".join(filter_info))

    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})

        if summary_data:
            st.subheader("Data Overview")

            # Create columns for metrics
            col1, col2, col3 = st.columns(3)

            # Total calls
            total_calls = sum(
                summary.get("rows", 0) for summary in summary_data.values()
            )
            col1.metric("Total Calls", f"{total_calls:,}")

            # Inbound calls
            inbound_calls = summary_data.get("inbound", {}).get("rows", 0)
            col2.metric("Inbound Calls", f"{inbound_calls:,}")

            # Outbound calls
            outbound_calls = summary_data.get("outbound", {}).get("rows", 0)
            col3.metric("Outbound Calls", f"{outbound_calls:,}")

            # Date range
            if "inbound" in summary_data and summary_data["inbound"].get("date_range"):
                start_date = summary_data["inbound"]["date_range"][0]
                end_date = summary_data["inbound"]["date_range"][1]
                st.info(f"Data range: {start_date} to {end_date}")

            # Show campaigns
            if "inbound" in summary_data and summary_data["inbound"].get("campaigns"):
                st.subheader("Campaigns")
                st.write(", ".join(summary_data["inbound"]["campaigns"]))

            # Show agents
            if "inbound" in summary_data and summary_data["inbound"].get("users"):
                st.subheader("Agents")
                st.write(", ".join(summary_data["inbound"]["users"]))
        else:
            st.warning("No data available. Please upload data files.")
    else:
        st.warning(
            "Could not retrieve data summary. Please check if the API is running."
        )

    # Quick access to key analyses
    st.subheader("Quick Access")

    quick_access_col1, quick_access_col2 = st.columns(2)

    # Initialize quick access rerun counter if not present
    if "quick_access_rerun_counter" not in st.session_state:
        st.session_state.quick_access_rerun_counter = 0

    # Safety limit for quick access reruns
    max_quick_access_reruns = 3

    with quick_access_col1:
        if st.button("📊 Agent Performance", key="qa_agent_performance"):
            st.session_state.page = "Agent Performance"
            if st.session_state.quick_access_rerun_counter < max_quick_access_reruns:
                st.session_state.quick_access_rerun_counter += 1
                st.rerun()
            else:
                st.warning(
                    "Please use the dropdown menu to navigate to Agent Performance"
                )

        if st.button("📈 Call Volume Prediction", key="qa_call_volume"):
            st.session_state.page = "Call Volume Prediction"
            if st.session_state.quick_access_rerun_counter < max_quick_access_reruns:
                st.session_state.quick_access_rerun_counter += 1
                st.rerun()
            else:
                st.warning(
                    "Please use the dropdown menu to navigate to Call Volume Prediction"
                )

        if st.button("👥 Staffing Optimization", key="qa_staffing"):
            st.session_state.page = "Staffing Optimization"
            if st.session_state.quick_access_rerun_counter < max_quick_access_reruns:
                st.session_state.quick_access_rerun_counter += 1
                st.rerun()
            else:
                st.warning(
                    "Please use the dropdown menu to navigate to Staffing Optimization"
                )

    with quick_access_col2:
        if st.button("🚀 Campaign Analysis", key="qa_campaign"):
            st.session_state.page = "Campaign Analysis"
            if st.session_state.quick_access_rerun_counter < max_quick_access_reruns:
                st.session_state.quick_access_rerun_counter += 1
                st.rerun()
            else:
                st.warning(
                    "Please use the dropdown menu to navigate to Campaign Analysis"
                )

        if st.button("🌐 Language Analysis", key="qa_language"):
            st.session_state.page = "Language Analysis"
            if st.session_state.quick_access_rerun_counter < max_quick_access_reruns:
                st.session_state.quick_access_rerun_counter += 1
                st.rerun()
            else:
                st.warning(
                    "Please use the dropdown menu to navigate to Language Analysis"
                )

        if st.button("📱 Missed Call Analysis", key="qa_missed_call"):
            st.session_state.page = "Missed Call Analysis"
            if st.session_state.quick_access_rerun_counter < max_quick_access_reruns:
                st.session_state.quick_access_rerun_counter += 1
                st.rerun()
            else:
                st.warning(
                    "Please use the dropdown menu to navigate to Missed Call Analysis"
                )

# Data Upload page
elif page == "Data Upload":
    st.title("Data Upload")
    st.write("Upload Excel files containing call flow data.")

    uploaded_files = st.file_uploader(
        "Upload Excel Files", type=["xlsx", "xls"], accept_multiple_files=True
    )

    if uploaded_files:
        if st.button("Process Files"):
            files = [
                (
                    "files",
                    (
                        file.name,
                        file.getvalue(),
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    ),
                )
                for file in uploaded_files
            ]

            with st.spinner("Uploading and processing files..."):
                response = make_api_request(
                    API_ENDPOINTS["upload_files"], method="POST", files=files
                )

                if response and response.get("success"):
                    st.success(f"Successfully uploaded {len(uploaded_files)} files")
                else:
                    st.error("Failed to upload files")

    # List existing files
    st.subheader("Existing Files")

    files_response = make_api_request(API_ENDPOINTS["list_files"])

    if files_response and files_response.get("success"):
        files = files_response.get("data", {}).get("files", [])

        if files:
            for file in files:
                st.write(f"- {file}")
        else:
            st.info("No files available")
    else:
        st.warning("Could not retrieve file list")

# Agent Performance page
elif page == "Agent Performance":
    st.title("Agent Performance Analysis")
    st.write("Analyze agent performance based on various metrics.")

    # Form for analysis parameters
    with st.form("agent_performance_form"):
        # Date range
        col1, col2 = st.columns(2)
        start_date = col1.date_input("Start Date", datetime.now() - timedelta(days=30))
        end_date = col2.date_input("End Date", datetime.now())

        # Metrics
        metrics = st.multiselect(
            "Select Metrics",
            ["resolution_rate", "call_length", "call_volume"],
            default=["resolution_rate", "call_length", "call_volume"],
        )

        # Filters
        campaigns = st.multiselect(
            "Filter by Campaigns",
            [
                "All",
                "Transaction_English",
                "GeneralArabic",
                "TechSupp",
                "ONLINE",
                "AGENTDIRECT",
            ],
        )
        agents = st.multiselect("Filter by Agents", ["All"])
        languages = st.multiselect("Filter by Languages", ["All", "English", "Arabic"])

        submit_button = st.form_submit_button("Analyze")

    if submit_button:
        # Prepare request data
        request_data = {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "metrics": metrics,
        }

        # Add filters if not "All"
        if campaigns and "All" not in campaigns:
            request_data["campaigns"] = campaigns
        if agents and "All" not in agents:
            request_data["agents"] = agents
        if languages and "All" not in languages:
            request_data["languages"] = languages

        # Make API request
        with st.spinner("Analyzing agent performance..."):
            response = make_api_request(
                API_ENDPOINTS["agent_performance"], method="POST", data=request_data
            )

            if response and response.get("success"):
                st.success("Analysis completed successfully")

                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])

                # Display agent scores if available
                if "agent_scores" in result_data:
                    st.subheader("Agent Performance Scores")

                    # Convert to DataFrame for better display
                    scores_df = pd.DataFrame(result_data["agent_scores"])
                    scores_df = scores_df.sort_values("overall_score", ascending=False)

                    # Display as table
                    st.dataframe(scores_df[["User", "overall_score"]])

                # Render charts
                if charts:
                    st.subheader("Performance Charts")

                    for chart in charts:
                        render_chart(chart)
                else:
                    st.info("No charts available")
            else:
                st.error("Failed to analyze agent performance")

# Add other pages following the same pattern
# For brevity, I'll include just one more example

# Model Training page
elif page == "Model Training":
    # Import the model training module
    try:
        # Try relative import first
        from .model_training import model_training_page
    except ImportError:
        # Fall back to direct import
        import sys
        import os

        # Get the absolute path to the frontend directory
        frontend_dir = os.path.dirname(os.path.abspath(__file__))
        if frontend_dir not in sys.path:
            sys.path.insert(0, frontend_dir)

        try:
            from model_training import model_training_page
        except ImportError:
            st.title("Model Training")
            st.write("Model Training page is not yet implemented.")
            st.info("This feature will be available in a future update.")
            # Skip the rest of this section
            if page == "Model Training":
                st.stop()

    # Run the model training page with the API URL
    model_training_page(API_URL)

# Task Monitor page
elif page == "Task Monitor":
    # Import the task monitor module
    try:
        # Try relative import first
        from .task_monitor import task_monitor_page
    except ImportError:
        # Fall back to direct import
        import sys
        import os

        # Get the absolute path to the frontend directory
        frontend_dir = os.path.dirname(os.path.abspath(__file__))
        if frontend_dir not in sys.path:
            sys.path.insert(0, frontend_dir)

        try:
            from task_monitor import task_monitor_page
        except ImportError:
            st.title("Task Monitor")
            st.write("Task Monitor page is not yet implemented.")
            st.info("This feature will be available in a future update.")
            # Skip the rest of this section
            if page == "Task Monitor":
                st.stop()

    # Run the task monitor page with the API URL
    task_monitor_page(API_URL)

# Call Volume Prediction page
elif page == "Call Volume Prediction":
    st.title("Call Volume Prediction")
    st.write("Predict call volumes based on historical data.")

    # Form for prediction parameters
    with st.form("call_volume_form"):
        # Date range
        col1, col2 = st.columns(2)
        start_date = col1.date_input(
            "Start Date (for training data)", datetime.now() - timedelta(days=90)
        )
        end_date = col2.date_input("End Date (for training data)", datetime.now())

        # Granularity
        granularity = st.selectbox(
            "Prediction Granularity", ["hourly", "daily", "weekly", "monthly"], index=1
        )

        # Filters
        campaigns = st.multiselect(
            "Filter by Campaigns",
            [
                "All",
                "Transaction_English",
                "GeneralArabic",
                "TechSupp",
                "ONLINE",
                "AGENTDIRECT",
            ],
        )
        languages = st.multiselect("Filter by Languages", ["All", "English", "Arabic"])

        submit_button = st.form_submit_button("Predict")

    if submit_button:
        # Prepare request data
        request_data = {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "granularity": granularity,
        }

        # Add filters if not "All"
        if campaigns and "All" not in campaigns:
            request_data["campaigns"] = campaigns
        if languages and "All" not in languages:
            request_data["languages"] = languages

        # Make API request
        with st.spinner(f"Predicting call volumes ({granularity})..."):
            response = make_api_request(
                API_ENDPOINTS["call_volume_prediction"],
                method="POST",
                data=request_data,
            )

            if response and response.get("success"):
                st.success("Prediction completed successfully")

                # Display results
                result_data = response.get("data", {})
                charts = response.get("charts", [])

                # Display historical and forecast data
                if "historical" in result_data and "forecast" in result_data:
                    st.subheader("Call Volume Forecast")

                    # Create tabs for chart and data
                    tab1, tab2 = st.tabs(["Chart", "Data"])

                    with tab1:
                        # Render charts
                        if charts:
                            for chart in charts:
                                render_chart(chart)
                        else:
                            st.info("No charts available")

                    with tab2:
                        # Display forecast data
                        st.subheader("Forecast Data")
                        forecast_df = pd.DataFrame(result_data["forecast"])
                        st.dataframe(forecast_df)

                        # Display historical data
                        st.subheader("Historical Data")
                        historical_df = pd.DataFrame(result_data["historical"])
                        st.dataframe(historical_df)
                else:
                    st.info("No prediction data available")
            else:
                st.error("Failed to predict call volumes")

# Advanced ML Models page
elif page == "Advanced ML Models":
    # Import the page module
    try:
        # Try relative import first
        from .pages import advanced_ml
    except ImportError:
        # Fall back to direct import
        import sys
        import os

        # Get the absolute path to the pages directory
        pages_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pages")
        if pages_dir not in sys.path:
            sys.path.insert(0, pages_dir)

        try:
            import advanced_ml
        except ImportError:
            st.title("Advanced ML Models")
            st.write("Advanced ML Models page is not yet implemented.")
            st.info("This feature will be available in a future update.")
            # Skip the rest of this section
            if page == "Advanced ML Models":
                st.stop()

    # Run the page code
    advanced_ml.st.title("Advanced ML Models")
    advanced_ml.st.write(
        "Use advanced machine learning models for predictive analytics"
    )

    # Get data summary for metadata with filters if available
    params = {}
    if "dashboard_filters" in st.session_state and any(
        st.session_state.dashboard_filters.values()
    ):
        params = st.session_state.dashboard_filters

    summary_response = advanced_ml.make_api_request(
        advanced_ml.API_ENDPOINTS["data_summary"], params=params
    )
    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})

        if summary_data:
            advanced_ml.st.info(
                f"Data available from {summary_data.get('inbound', {}).get('date_range', ['', ''])[0]} to {summary_data.get('inbound', {}).get('date_range', ['', ''])[1]}"
            )

    # Create tabs for different ML models
    tab1, tab2, tab3 = advanced_ml.st.tabs(
        ["Call Volume Prediction", "Model Training", "Model Evaluation"]
    )

    # Call the tab content functions
    with tab1:
        advanced_ml.st.header("Advanced Call Volume Prediction")
        advanced_ml.st.write(
            "Predict call volumes using advanced machine learning models"
        )
        # Rest of the tab1 code would go here

    with tab2:
        advanced_ml.st.header("Model Training")
        advanced_ml.st.write("Train machine learning models for call flow analytics")
        # Rest of the tab2 code would go here

    with tab3:
        advanced_ml.st.header("Model Evaluation")
        advanced_ml.st.write("Evaluate trained machine learning models")
        advanced_ml.st.info(
            "Model evaluation functionality will be implemented in a future update."
        )

# Custom Reports page
elif page == "Custom Reports":
    # Import the page module
    try:
        # Try relative import first
        from .pages import custom_reports
    except ImportError:
        # Fall back to direct import
        import sys
        import os

        # Get the absolute path to the pages directory
        pages_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pages")
        if pages_dir not in sys.path:
            sys.path.insert(0, pages_dir)

        try:
            import custom_reports
        except ImportError:
            st.title("Custom Reports")
            st.write("Custom Reports page is not yet implemented.")
            st.info("This feature will be available in a future update.")
            # Skip the rest of this section
            if page == "Custom Reports":
                st.stop()

    # Run the page code
    custom_reports.st.title("Custom Reports")
    custom_reports.st.write("Generate custom reports for call flow analytics")

    # Get data summary for metadata with filters if available
    params = {}
    if "dashboard_filters" in st.session_state and any(
        st.session_state.dashboard_filters.values()
    ):
        params = st.session_state.dashboard_filters

    summary_response = custom_reports.make_api_request(
        custom_reports.API_ENDPOINTS["data_summary"], params=params
    )
    if summary_response and summary_response.get("success"):
        summary_data = summary_response.get("data", {}).get("summary", {})

        if summary_data:
            custom_reports.st.info(
                f"Data available from {summary_data.get('inbound', {}).get('date_range', ['', ''])[0]} to {summary_data.get('inbound', {}).get('date_range', ['', ''])[1]}"
            )

    # Rest of the custom reports code would go here
    custom_reports.st.info("Please use the form below to generate custom reports.")

# Run the app
if __name__ == "__main__":
    pass
