import streamlit as st
import pandas as pd
import requests
import time
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
import json


def task_monitor_page(api_url):
    """
    Render the task monitoring page

    Args:
        api_url: Base API URL
    """
    st.title("Task Monitor")
    st.write("Monitor and manage background tasks for call flow analysis.")

    # Define API endpoints
    API_ENDPOINTS = {
        "task_status": f"{api_url}/tasks/status/",
        "task_list": f"{api_url}/tasks/list",
    }

    # Create tabs for different sections
    tab1, tab2 = st.tabs(["Task Dashboard", "Flower Monitor"])

    # Tab 1: Task Dashboard
    with tab1:
        st.subheader("Task Dashboard")

        # Add refresh button
        col1, col2 = st.columns([3, 1])
        with col1:
            if st.button("Refresh Dashboard"):
                st.rerun()
        with col2:
            refresh_interval = st.selectbox(
                "Auto-refresh",
                [None, 5, 10, 30, 60],
                format_func=lambda x: "Off" if x is None else f"Every {x}s",
            )

        # Set up auto-refresh if enabled
        if refresh_interval:
            # Initialize refresh counter to prevent infinite recursion
            if "refresh_counter" not in st.session_state:
                st.session_state.refresh_counter = 0

            # Initialize last refresh time if not present
            if "last_refresh" not in st.session_state:
                st.session_state.last_refresh = datetime.now()

            # Display auto-refresh status
            st.write(f"Auto-refreshing every {refresh_interval} seconds...")
            st.write(
                f"Last refresh: {st.session_state.last_refresh.strftime('%H:%M:%S')}"
            )

            # Safety mechanism: limit consecutive refreshes to prevent infinite recursion
            max_consecutive_refreshes = 10

            # Check if it's time to refresh and we haven't exceeded the safety limit
            current_time = datetime.now()
            time_since_last_refresh = (
                current_time - st.session_state.last_refresh
            ).total_seconds()

            if (
                time_since_last_refresh >= refresh_interval
                and st.session_state.refresh_counter < max_consecutive_refreshes
            ):
                # Update the last refresh time and increment counter
                st.session_state.last_refresh = current_time
                st.session_state.refresh_counter += 1

                # Add a small delay to prevent rapid consecutive refreshes
                time.sleep(0.5)

                # Rerun the app
                st.rerun()
            elif st.session_state.refresh_counter >= max_consecutive_refreshes:
                st.error(
                    "Auto-refresh stopped: too many consecutive refreshes. Please refresh manually."
                )
                # Reset the counter after displaying the error
                if st.button("Reset Auto-refresh"):
                    st.session_state.refresh_counter = 0
                    st.session_state.last_refresh = current_time

        # Get task list from API
        try:
            response = requests.get(API_ENDPOINTS["task_list"], timeout=5)
            tasks = response.json().get("tasks", [])

            if not tasks:
                st.info("No tasks found. Start a new task to see it here.")

                # Add a note about Flower for monitoring
                st.info(
                    """
                You can also monitor Celery tasks using Flower. Start it with:
                ```
                ./run_flower.sh
                ```
                Then click the "Flower Monitor" tab above.
                """
                )
            else:
                # Create a DataFrame for the tasks
                task_data = []
                for task in tasks:
                    task_data.append(
                        {
                            "Task ID": task.get("task_id", "Unknown"),
                            "Status": task.get("status", "Unknown"),
                            "Progress": task.get("progress", 0),
                            "Message": task.get("message", ""),
                            "Started": task.get("started_at", "Unknown"),
                            "Completed": task.get("completed_at", "Unknown"),
                        }
                    )

                # Create a DataFrame
                df = pd.DataFrame(task_data)

                # Add color coding based on status
                def highlight_status(val):
                    if val == "SUCCESS":
                        return "background-color: #d4edda; color: #155724"
                    elif val == "FAILURE":
                        return "background-color: #f8d7da; color: #721c24"
                    elif val == "RUNNING":
                        return "background-color: #cce5ff; color: #004085"
                    elif val == "PENDING":
                        return "background-color: #fff3cd; color: #856404"
                    else:
                        return ""

                # Display the DataFrame with styling
                st.dataframe(df.style.applymap(highlight_status, subset=["Status"]))

                # Create a status summary
                status_counts = df["Status"].value_counts().reset_index()
                status_counts.columns = ["Status", "Count"]

                # Create a pie chart for status distribution
                fig = px.pie(
                    status_counts,
                    values="Count",
                    names="Status",
                    title="Task Status Distribution",
                    color="Status",
                    color_discrete_map={
                        "SUCCESS": "#28a745",
                        "FAILURE": "#dc3545",
                        "RUNNING": "#007bff",
                        "PENDING": "#ffc107",
                    },
                )
                st.plotly_chart(fig, use_container_width=True)

                # Display task details in expandable sections
                st.subheader("Task Details")
                for i, task in enumerate(tasks):
                    task_id = task.get("task_id", "Unknown")
                    status = task.get("status", "Unknown")
                    progress = task.get("progress", 0)

                    with st.expander(f"Task {task_id} ({status} - {progress}%)"):
                        # Get detailed task info
                        try:
                            task_info = requests.get(
                                f"{API_ENDPOINTS['task_status']}{task_id}"
                            ).json()

                            # Display progress bar
                            st.progress(task_info.get("progress", 0) / 100)

                            # Display task details
                            col1, col2 = st.columns(2)
                            with col1:
                                st.write(
                                    "**Status:**", task_info.get("status", "Unknown")
                                )
                                st.write(
                                    "**Progress:**", f"{task_info.get('progress', 0)}%"
                                )
                                st.write("**Message:**", task_info.get("message", ""))

                            with col2:
                                st.write(
                                    "**Started:**",
                                    task_info.get("started_at", "Unknown"),
                                )
                                st.write(
                                    "**Completed:**",
                                    task_info.get("completed_at", "Unknown"),
                                )

                            # Display result if available
                            result = task_info.get("result", {})
                            if result:
                                st.subheader("Task Result")

                                # Check if result is a success
                                if (
                                    isinstance(result, dict)
                                    and result.get("success") is True
                                ):
                                    st.success("Task completed successfully!")

                                    # Display metrics if available
                                    metrics = result.get("metrics", {})
                                    if metrics:
                                        st.write("**Metrics:**")
                                        metrics_df = pd.DataFrame(
                                            {
                                                "Metric": list(metrics.keys()),
                                                "Value": list(metrics.values()),
                                            }
                                        )
                                        st.dataframe(metrics_df)

                                    # Display feature importance if available
                                    feature_importance = result.get(
                                        "feature_importance", {}
                                    )
                                    if feature_importance:
                                        st.write("**Feature Importance:**")

                                        # Create feature importance chart
                                        importance_df = pd.DataFrame(
                                            {
                                                "Feature": list(
                                                    feature_importance.keys()
                                                ),
                                                "Importance": list(
                                                    feature_importance.values()
                                                ),
                                            }
                                        ).sort_values("Importance", ascending=False)

                                        fig = px.bar(
                                            importance_df,
                                            x="Importance",
                                            y="Feature",
                                            orientation="h",
                                            title="Feature Importance",
                                        )
                                        st.plotly_chart(fig, use_container_width=True)

                                elif (
                                    isinstance(result, dict)
                                    and result.get("success") is False
                                ):
                                    st.error(
                                        f"Task failed: {result.get('message', 'Unknown error')}"
                                    )

                                else:
                                    # Just display the raw result
                                    st.json(result)

                        except Exception as e:
                            st.error(f"Error fetching task details: {str(e)}")

        except Exception as e:
            st.error(f"Error fetching task list: {str(e)}")

    # Tab 2: Flower Monitor
    with tab2:
        st.subheader("Flower Task Monitor")
        st.write("Flower is a web-based tool for monitoring Celery tasks.")

        # Display Flower iframe
        st.markdown(
            """
        <div style="background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
            <h3>Flower Monitor</h3>
            <p>Flower provides a comprehensive dashboard for monitoring Celery tasks.</p>
            <p>Click the button below to open Flower in a new tab:</p>
            <a href="http://localhost:5555" target="_blank" style="text-decoration: none;">
                <button style="background-color: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                    🌸 Open Flower Monitor
                </button>
            </a>
        </div>
        """,
            unsafe_allow_html=True,
        )

        st.write("")
        st.info(
            "Note: Flower must be running for the monitor to work. Run `./run_flower.sh` to start Flower."
        )

        # Display Flower features
        st.subheader("Flower Features")

        feature_col1, feature_col2 = st.columns(2)

        with feature_col1:
            st.write("**Real-time Monitoring**")
            st.write("- View active tasks")
            st.write("- Monitor worker status")
            st.write("- Track task progress")

        with feature_col2:
            st.write("**Task Management**")
            st.write("- View task details")
            st.write("- Revoke or terminate tasks")
            st.write("- View task history")
