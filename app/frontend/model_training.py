import streamlit as st
import pandas as pd
import requests
import time
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json


def model_training_page(api_url):
    """
    Render the model training page

    Args:
        api_url: Base API URL
    """
    st.title("Model Training")
    st.write("Train and manage machine learning models for call flow analysis.")

    # Define API endpoints
    API_ENDPOINTS = {
        "train_model": f"{api_url}/tasks/train-model",  # This should match the FastAPI router endpoint
        "train_model_alt": f"{api_url}/tasks/train_model",  # Alternative with underscore
        "direct_train_model": f"{api_url}/direct-train-model",  # Direct endpoint for testing
        "task_status": f"{api_url}/tasks/status/",
        "task_list": f"{api_url}/tasks/list",
        "data_summary": f"{api_url}/data/summary",
        "test_tasks": f"{api_url}/tasks/test",  # Test endpoint to verify the router is working
    }

    # Log the API endpoints for debugging
    st.write("API URL:", api_url)
    st.write("Train Model Endpoint:", API_ENDPOINTS["train_model"])

    # Test the tasks router
    try:
        test_response = requests.get(API_ENDPOINTS["test_tasks"], timeout=5)
        st.write(f"Tasks Router Test Status: {test_response.status_code}")
        if test_response.status_code == 200:
            st.write("Tasks Router Test Response:", test_response.json())
        else:
            st.error(f"Tasks Router Test Failed: {test_response.text}")
    except Exception as e:
        st.error(f"Error testing tasks router: {str(e)}")

    # Create tabs for different sections
    tab1, tab2, tab3 = st.tabs(["Train New Model", "Training Status", "Model Gallery"])

    # Tab 1: Train New Model
    with tab1:
        st.subheader("Train a New Model")

        # Model selection
        model_name = st.selectbox(
            "Select Model Type",
            ["call_volume", "agent_performance"],
            format_func=lambda x: (
                "Call Volume Prediction" if x == "call_volume" else "Agent Performance"
            ),
        )

        # Model parameters
        if model_name == "call_volume":
            col1, col2 = st.columns(2)

            with col1:
                model_type = st.selectbox(
                    "Model Algorithm",
                    ["ensemble", "xgb", "lgb", "rf", "gbm", "linear"],
                    format_func=lambda x: {
                        "ensemble": "Ensemble (Best Performance)",
                        "xgb": "XGBoost",
                        "lgb": "LightGBM",
                        "rf": "Random Forest",
                        "gbm": "Gradient Boosting",
                        "linear": "Linear Regression (Fastest)",
                    }.get(x, x),
                )

            with col2:
                granularity = st.selectbox(
                    "Time Granularity",
                    ["hourly", "daily", "weekly", "monthly"],
                    index=1,  # Default to daily
                )
        else:
            model_type = "ensemble"  # Default for agent performance

        # Data filters
        with st.expander("Data Filters", expanded=False):
            filter_col1, filter_col2 = st.columns(2)

            # Date range
            with filter_col1:
                start_date = st.date_input(
                    "Start Date", datetime.now() - timedelta(days=30)
                )
                end_date = st.date_input("End Date", datetime.now())

            # Campaigns and languages
            with filter_col2:
                # Get available campaigns and languages from API
                summary_response = requests.get(API_ENDPOINTS["data_summary"]).json()

                available_campaigns = []
                available_languages = []

                if summary_response.get("success"):
                    summary_data = summary_response.get("data", {}).get("summary", {})
                    if "inbound" in summary_data:
                        available_campaigns = summary_data["inbound"].get(
                            "campaigns", []
                        )
                        # Extract languages if available
                        if "Language" in summary_data["inbound"].get("columns", []):
                            available_languages = [
                                "English",
                                "Arabic",
                            ]  # Default if not found

                campaigns = st.multiselect("Filter by Campaigns", available_campaigns)

                languages = st.multiselect("Filter by Languages", available_languages)

        # Prepare filter parameters
        filter_params = {}
        if start_date:
            filter_params["start_date"] = start_date.strftime("%Y-%m-%d")
        if end_date:
            filter_params["end_date"] = end_date.strftime("%Y-%m-%d")
        if campaigns:
            filter_params["campaigns"] = campaigns
        if languages:
            filter_params["languages"] = languages

        # Training button
        if st.button("Start Training"):
            # Prepare request data
            request_data = {
                "model_name": model_name,
                "model_type": model_type,
                "filter_params": filter_params,
            }

            if model_name == "call_volume":
                request_data["granularity"] = granularity

            # We'll skip the Celery check since it's already running
            # Just show a note about Celery for information
            st.info(
                "Note: This operation requires Celery to be running. If you encounter errors, please verify Celery is running with `./run_celery.sh`"
            )

            # Make API request
            with st.spinner("Starting model training..."):
                try:
                    # Display the request data for debugging
                    st.write("Sending request with data:", request_data)

                    # Try both hyphen and underscore versions of the endpoint
                    try:
                        # First try the hyphen version
                        st.write(
                            "Trying endpoint with hyphen:", API_ENDPOINTS["train_model"]
                        )
                        raw_response = requests.post(
                            API_ENDPOINTS["train_model"], json=request_data, timeout=10
                        )

                        # If that fails with 404, try the underscore version
                        if raw_response.status_code == 404:
                            st.warning(
                                "Hyphen version returned 404, trying underscore version..."
                            )
                            st.write(
                                "Trying endpoint with underscore:",
                                API_ENDPOINTS["train_model_alt"],
                            )
                            raw_response = requests.post(
                                API_ENDPOINTS["train_model_alt"],
                                json=request_data,
                                timeout=10,
                            )

                            # If that also fails with 404, try the direct endpoint
                            if raw_response.status_code == 404:
                                st.warning(
                                    "Underscore version also returned 404, trying direct endpoint..."
                                )
                                st.write(
                                    "Trying direct endpoint:",
                                    API_ENDPOINTS["direct_train_model"],
                                )
                                raw_response = requests.post(
                                    API_ENDPOINTS["direct_train_model"],
                                    json=request_data,
                                    timeout=10,
                                )

                        # Log the raw response for debugging
                        st.write(f"API Status Code: {raw_response.status_code}")
                        st.write(f"Response Headers: {dict(raw_response.headers)}")
                        st.write(
                            f"Raw Response Text: {raw_response.text[:500]}..."
                        )  # Show first 500 chars

                        # Try to parse the JSON response
                        try:
                            response = raw_response.json()
                            st.write("Response received:", response)
                        except Exception as json_error:
                            st.error(
                                f"Failed to parse API response as JSON: {str(json_error)}"
                            )
                            st.error(f"Raw response: {raw_response.text}")
                            return
                    except requests.exceptions.RequestException as req_error:
                        st.error(f"Request failed: {str(req_error)}")
                        return

                    if response.get("success"):
                        task_id = response.get("task_id")
                        if not task_id:
                            st.error(
                                "Task ID is missing in the response even though success=True"
                            )
                            st.write("Full response:", response)
                            return

                        st.success(f"Training started! Task ID: {task_id}")

                        # Store task ID in session state
                        if "training_tasks" not in st.session_state:
                            st.session_state.training_tasks = []

                        st.session_state.training_tasks.append(
                            {
                                "task_id": task_id,
                                "model_name": model_name,
                                "model_type": model_type,
                                "start_time": datetime.now().strftime(
                                    "%Y-%m-%d %H:%M:%S"
                                ),
                                "status": "RUNNING",
                            }
                        )

                        # Switch to the Training Status tab
                        st.session_state.active_tab = "Training Status"

                        # Add a safety mechanism to prevent infinite recursion
                        if "rerun_counter" not in st.session_state:
                            st.session_state.rerun_counter = 0

                        # Only rerun if we haven't exceeded the safety limit
                        if st.session_state.rerun_counter < 3:
                            st.session_state.rerun_counter += 1
                            st.rerun()
                        else:
                            st.warning(
                                "Please manually switch to the Training Status tab."
                            )
                    else:
                        error_message = response.get("message", "Unknown error")
                        st.error(f"Failed to start training: {error_message}")

                        # Show the full response for debugging
                        st.write("Full response:", response)

                        # Provide more helpful information if it's an unknown error
                        if error_message == "Unknown error":
                            st.error(
                                """
                            This might be due to one of the following issues:
                            1. Celery worker is not properly configured
                            2. Redis server is not running
                            3. There's an issue with the task registration

                            Please check the server logs for more details.
                            """
                            )
                except requests.exceptions.Timeout:
                    st.error(
                        "Request timed out. The server might be overloaded or not responding."
                    )
                except requests.exceptions.ConnectionError:
                    st.error(
                        "Connection error. Please check if the API server is running."
                    )
                except Exception as e:
                    st.error(f"Error starting training: {str(e)}")

    # Tab 2: Training Status
    with tab2:
        st.subheader("Model Training Status")

        # Add Flower monitoring link
        col1, col2 = st.columns([3, 1])
        with col1:
            # Refresh button
            if st.button("Refresh Status"):
                st.rerun()
        with col2:
            st.markdown(
                "[🌸 Open Flower Monitor](http://localhost:5555)",
                unsafe_allow_html=True,
            )

        # Get tasks from session state
        if "training_tasks" not in st.session_state:
            st.session_state.training_tasks = []

        # Get task list from API
        try:
            response = requests.get(API_ENDPOINTS["task_list"]).json()
            tasks = response.get("tasks", [])

            # Update session state with tasks from API
            for task in tasks:
                # Check if task is already in session state
                task_ids = [t["task_id"] for t in st.session_state.training_tasks]
                if task["task_id"] not in task_ids:
                    # Add new task to session state
                    st.session_state.training_tasks.append(
                        {
                            "task_id": task["task_id"],
                            "model_name": task.get("params", {}).get(
                                "model_name", "Unknown"
                            ),
                            "model_type": task.get("params", {}).get(
                                "model_type", "Unknown"
                            ),
                            "start_time": task.get("started_at", "Unknown"),
                            "status": task.get("status", "Unknown"),
                        }
                    )
                else:
                    # Update existing task
                    for i, t in enumerate(st.session_state.training_tasks):
                        if t["task_id"] == task["task_id"]:
                            st.session_state.training_tasks[i]["status"] = task.get(
                                "status", "Unknown"
                            )
        except Exception as e:
            st.warning(f"Could not fetch task list: {str(e)}")

        # Display tasks
        if not st.session_state.training_tasks:
            st.info(
                "No training tasks found. Start a new training job in the 'Train New Model' tab."
            )
        else:
            for i, task in enumerate(st.session_state.training_tasks):
                with st.expander(
                    f"{task['model_name']} - {task['model_type']} ({task['status']})",
                    expanded=i == 0,
                ):
                    # Get task status
                    try:
                        status_response = requests.get(
                            f"{API_ENDPOINTS['task_status']}{task['task_id']}"
                        ).json()

                        # Display progress
                        progress = status_response.get("progress", 0)
                        message = status_response.get("message", "")
                        status = status_response.get("status", "UNKNOWN")

                        # Update task status in session state
                        st.session_state.training_tasks[i]["status"] = status

                        # Display progress bar
                        st.progress(progress / 100)
                        st.write(f"Status: {status}")
                        st.write(f"Progress: {progress}%")
                        st.write(f"Message: {message}")

                        # Display result if available
                        result = status_response.get("result", {})
                        if result and result.get("success") and status == "SUCCESS":
                            st.success("Training completed successfully!")

                            # Display metrics
                            metrics = result.get("metrics", {})
                            if metrics:
                                st.subheader("Model Performance")
                                metrics_df = pd.DataFrame(
                                    {
                                        "Metric": list(metrics.keys()),
                                        "Value": list(metrics.values()),
                                    }
                                )
                                st.dataframe(metrics_df)

                            # Display feature importance
                            feature_importance = result.get("feature_importance", {})
                            if feature_importance:
                                st.subheader("Feature Importance")

                                # Create feature importance chart
                                importance_df = pd.DataFrame(
                                    {
                                        "Feature": list(feature_importance.keys()),
                                        "Importance": list(feature_importance.values()),
                                    }
                                ).sort_values("Importance", ascending=False)

                                fig = px.bar(
                                    importance_df,
                                    x="Importance",
                                    y="Feature",
                                    orientation="h",
                                    title="Feature Importance",
                                )
                                st.plotly_chart(fig, use_container_width=True)

                        elif status == "FAILURE":
                            st.error(
                                f"Training failed: {result.get('message', 'Unknown error')}"
                            )

                    except Exception as e:
                        st.error(f"Error fetching task status: {str(e)}")

    # Tab 3: Model Gallery
    with tab3:
        st.subheader("Model Gallery")
        st.write("View and manage trained models.")

        # This would typically fetch models from a model registry
        # For now, we'll just show a placeholder
        st.info("Model gallery functionality coming soon!")

        # Placeholder for model cards
        col1, col2 = st.columns(2)

        with col1:
            with st.container():
                st.markdown("---")
                st.subheader("Call Volume Prediction")
                st.write("Ensemble model trained on daily data")
                st.write("Accuracy: 87%")
                st.write("Last updated: 2023-11-15")
                st.button("Use Model", key="use_model_1")
                st.markdown("---")

        with col2:
            with st.container():
                st.markdown("---")
                st.subheader("Agent Performance")
                st.write("XGBoost model for agent scoring")
                st.write("Accuracy: 92%")
                st.write("Last updated: 2023-11-10")
                st.button("Use Model", key="use_model_2")
                st.markdown("---")
