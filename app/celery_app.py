from celery import Celery
import os
from celery.signals import task_postrun, task_prerun
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Redis URL - can be configured via environment variable
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# Create Celery app
celery_app = Celery("call_flow", broker=REDIS_URL, backend=REDIS_URL)

# Set up more detailed logging for Celery
logging.getLogger("celery").setLevel(logging.DEBUG)

# Explicitly set the tasks module with more specific paths
celery_app.conf.imports = ("app.tasks", "app.api.services.ml_models")

# Auto-discover tasks with more paths
celery_app.autodiscover_tasks(["app", "app.api", "app.api.services"])

# Log the registered tasks
logger.info("Registered Celery tasks:")
for task_name in celery_app.tasks.keys():
    if not task_name.startswith("celery."):
        logger.info(f"  - {task_name}")

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_publish_retry=True,
    worker_prefetch_multiplier=1,  # Process one task at a time per worker
    task_acks_late=True,  # Acknowledge tasks after they are executed
)

# Task state tracking
task_states = {}


@task_prerun.connect
def task_prerun_handler(task_id, task, *args, **kwargs):
    """Handler called before a task is run"""
    logger.info(f"Task started: {task_id}")
    task_states[task_id] = {
        "status": "STARTED",
        "progress": 0,
        "message": "Task started",
    }


@task_postrun.connect
def task_postrun_handler(task_id, task, retval, state, *args, **kwargs):
    """Handler called after a task is run"""
    logger.info(f"Task completed: {task_id} with state {state}")

    # Update task state based on result
    if state == "SUCCESS":
        if isinstance(retval, dict) and "success" in retval:
            if retval["success"]:
                task_states[task_id] = {
                    "status": "SUCCESS",
                    "progress": 100,
                    "message": "Task completed successfully",
                    "result": retval,
                }
            else:
                task_states[task_id] = {
                    "status": "FAILURE",
                    "progress": 100,
                    "message": retval.get("message", "Task failed"),
                    "result": retval,
                }
        else:
            task_states[task_id] = {
                "status": "SUCCESS",
                "progress": 100,
                "message": "Task completed successfully",
                "result": retval,
            }
    else:
        task_states[task_id] = {
            "status": "FAILURE",
            "progress": 100,
            "message": f"Task failed with state {state}",
            "result": retval if state == "SUCCESS" else None,
        }


def update_task_progress(task_id, progress, message=None):
    """
    Update task progress

    Args:
        task_id: Task ID
        progress: Progress percentage (0-100)
        message: Optional progress message
    """
    if task_id in task_states:
        task_states[task_id]["progress"] = min(max(0, progress), 100)
        if message:
            task_states[task_id]["message"] = message
        logger.info(f"Task {task_id} progress: {progress}% - {message}")
    else:
        task_states[task_id] = {
            "status": "RUNNING",
            "progress": min(max(0, progress), 100),
            "message": message or "Task in progress",
        }
        logger.info(f"Created progress for task {task_id}: {progress}% - {message}")


def get_task_info(task_id):
    """
    Get task information

    Args:
        task_id: Task ID

    Returns:
        Task information or None if not found
    """
    return task_states.get(task_id)


def get_all_tasks():
    """
    Get all tasks

    Returns:
        Dictionary of all tasks
    """
    return task_states
