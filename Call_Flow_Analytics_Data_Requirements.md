# Call Flow Analytics System
## Data Requirements and Format Specifications

This document outlines the data requirements and format specifications for the Call Flow Analytics System. Following these guidelines will ensure optimal performance and accurate analysis results.

## 1. File Format

### 1.1 Supported File Types

The system accepts data in the following formats:
- Microsoft Excel (.xlsx, .xls)
- CSV files (.csv) - must be properly formatted with sheet names as the first row

### 1.2 File Structure

Each file should contain the following sheets:
- **Inbound**: Data about inbound calls
- **Outbound**: Data about outbound calls
- **Missed Call**: Data about missed calls

Note: The system will recognize variations in sheet names such as "missed call" or "Missed call" (case-insensitive).

## 2. Required Data Fields

### 2.1 Common Fields (Required for All Sheets)

| Field Name | Data Type | Description | Example |
|------------|-----------|-------------|---------|
| Lead ID | String/Number | Unique identifier for the lead | "L12345" or 12345 |
| Phone | String | Customer phone number | "+1234567890" |
| Campaign | String | Campaign name or identifier | "TechSupp" |
| Call Date | Date/Time | Date and time of the call | "2023-01-15 14:30:00" |
| Status | String | Status of the call | "ANSWERED", "ABANDONED" |
| User | String | Agent who handled the call | "Agent001" |
| List ID | String/Number | Identifier for the call list | "LIST123" or 123 |

### 2.2 Inbound Sheet Specific Fields

| Field Name | Data Type | Description | Example |
|------------|-----------|-------------|---------|
| Queue Time | Number | Time in queue (seconds) | 45 |
| Length | Number | Call duration (seconds) | 180 |
| IVR Path | String | Path through the IVR system | "1-3-2" |
| Abandoned | Boolean | Whether the call was abandoned | TRUE/FALSE |

### 2.3 Outbound Sheet Specific Fields

| Field Name | Data Type | Description | Example |
|------------|-----------|-------------|---------|
| Length | Number | Call duration (seconds) | 180 |
| Attempts | Number | Number of attempts made | 2 |
| Disposition | String | Call disposition | "SALE", "NO ANSWER" |
| Agent Notes | String | Notes from the agent | "Customer requested callback" |

### 2.4 Missed Call Sheet Specific Fields

| Field Name | Data Type | Description | Example |
|------------|-----------|-------------|---------|
| Callback Time | Date/Time | Time of callback (if any) | "2023-01-15 16:30:00" |
| Callback Status | String | Status of the callback | "COMPLETED", "SCHEDULED" |
| Reason | String | Reason for missed call | "AFTER HOURS", "QUEUE FULL" |

## 3. Data Format Guidelines

### 3.1 Date and Time Format

Date and time fields should be in one of the following formats:
- ISO format: "YYYY-MM-DD HH:MM:SS"
- Excel date/time format (will be automatically converted)

The system will attempt to parse other date formats, but these are recommended for optimal performance.

### 3.2 Phone Number Format

Phone numbers should include the country code and be in one of the following formats:
- International format: "+1234567890"
- Local format with spaces: "************"
- Local format with dashes: "************"

### 3.3 Duration Format

Call durations and queue times should be in seconds as integer values.

### 3.4 Campaign Naming Convention

For optimal language detection and campaign analysis, use consistent naming conventions:
- Include language identifier in campaign name (e.g., "TechSupp_English", "GeneralArabic")
- Use underscores or camel case for multi-word campaign names
- Avoid special characters in campaign names

## 4. Sample Data

### 4.1 Inbound Sheet Example

| Lead ID | Phone | Campaign | Call Date | Status | User | List ID | Queue Time | Length |
|---------|-------|----------|-----------|--------|------|---------|------------|--------|
| L12345 | +1234567890 | TechSupp_English | 2023-01-15 14:30:00 | ANSWERED | Agent001 | LIST123 | 45 | 180 |
| L12346 | +1234567891 | GeneralArabic | 2023-01-15 14:35:00 | ABANDONED | Agent002 | LIST123 | 120 | 0 |
| L12347 | +1234567892 | Sales_English | 2023-01-15 14:40:00 | ANSWERED | Agent003 | LIST124 | 30 | 240 |

### 4.2 Outbound Sheet Example

| Lead ID | Phone | Campaign | Call Date | Status | User | List ID | Length | Attempts | Disposition |
|---------|-------|----------|-----------|--------|------|---------|--------|----------|-------------|
| L12348 | +1234567893 | TechSupp_English | 2023-01-15 15:30:00 | ANSWERED | Agent001 | LIST125 | 180 | 1 | RESOLVED |
| L12349 | +1234567894 | GeneralArabic | 2023-01-15 15:35:00 | NO ANSWER | Agent002 | LIST125 | 0 | 2 | NO ANSWER |
| L12350 | +1234567895 | Sales_English | 2023-01-15 15:40:00 | ANSWERED | Agent003 | LIST126 | 300 | 1 | SALE |

### 4.3 Missed Call Sheet Example

| Lead ID | Phone | Campaign | Call Date | Status | User | List ID | Callback Time | Callback Status | Reason |
|---------|-------|----------|-----------|--------|------|---------|---------------|----------------|--------|
| L12351 | +1234567896 | TechSupp_English | 2023-01-15 16:30:00 | MISSED | System | LIST127 | 2023-01-16 10:30:00 | SCHEDULED | AFTER HOURS |
| L12352 | +1234567897 | GeneralArabic | 2023-01-15 16:35:00 | MISSED | System | LIST127 | 2023-01-16 11:00:00 | COMPLETED | QUEUE FULL |
| L12353 | +1234567898 | Sales_English | 2023-01-15 16:40:00 | MISSED | System | LIST128 | NULL | NOT SCHEDULED | ABANDONED |

## 5. Data Quality Guidelines

### 5.1 Missing Values

The system can handle missing values, but for optimal analysis results:
- Avoid missing values in key fields (Lead ID, Phone, Call Date, Status, User)
- Use consistent representations for missing values (empty cell, "NULL", "N/A")

### 5.2 Duplicate Handling

- Each call should have a unique combination of Lead ID and Call Date
- If duplicates exist, the system will use the first occurrence by default
- For multiple calls from the same lead on the same day, ensure the Call Date includes time

### 5.3 Data Consistency

- Use consistent naming conventions for campaigns, statuses, and users
- Ensure time zones are consistent across all data
- Use consistent formats for phone numbers and other identifiers

## 6. Historical Data Requirements

### 6.1 Minimum Data for Accurate Analysis

For optimal results, provide:
- At least 3 months of historical data for basic analytics
- At least 6 months of historical data for seasonal trend analysis
- At least 12 months of historical data for annual forecasting

### 6.2 Data Volume Considerations

The system can handle large volumes of data, but performance may be affected:
- Optimal performance: Up to 1 million calls per analysis
- Acceptable performance: Up to 5 million calls per analysis
- For larger volumes, consider filtering or aggregating data

## 7. Data Privacy and Security

### 7.1 Sensitive Data Handling

- Remove or mask personally identifiable information (PII) if not required
- Consider anonymizing customer phone numbers and names
- The system does not require full phone numbers for analysis

### 7.2 Data Retention

- The system stores uploaded data for analysis purposes
- Data is stored in memory and temporary files
- Consider implementing a data retention policy

## 8. Troubleshooting Data Issues

### 8.1 Common Data Problems

| Problem | Symptom | Solution |
|---------|---------|----------|
| Date format issues | Incorrect date filtering | Ensure dates are in ISO format |
| Missing required fields | Analysis errors or incomplete results | Check data for required fields |
| Inconsistent campaign names | Inaccurate campaign analysis | Standardize campaign naming |
| Duplicate records | Inflated metrics | Remove duplicates before upload |
| Character encoding issues | Strange characters in text fields | Save files with UTF-8 encoding |

### 8.2 Data Validation

The system performs basic validation on upload:
- Checks for required fields
- Validates date formats
- Identifies potential duplicates
- Reports data quality issues

## 9. Conclusion

Following these data requirements and format specifications will ensure optimal performance and accurate results from the Call Flow Analytics System. If you have questions or need assistance with data preparation, please contact the support team.

---

*For more information on using the system, refer to the Quick Start Guide and Technical Documentation.*
