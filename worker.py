"""
Celery worker script for debugging and direct execution
"""
import os
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the Celery app
from app.celery_app import celery_app

# Import tasks to ensure they are registered
import app.tasks

if __name__ == '__main__':
    # Start the worker
    celery_app.worker_main(argv=['worker', '--loglevel=info', '-E'])
